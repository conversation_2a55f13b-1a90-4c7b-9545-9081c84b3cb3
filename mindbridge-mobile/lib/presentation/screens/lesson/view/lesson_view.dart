import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:get/get.dart';
import 'package:mindbridge/presentation/screens/home/<USER>/evaluation_param.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/routes.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../data/providers/remote/dio_client.dart';
import '../../../widgets/btm_navigation_bar.dart';
import '../../../widgets/global_empty.dart';
import '../../../widgets/global_error.dart';
import '../../../widgets/global_header.dart';
import '../../../widgets/global_loader.dart';
import '../../../widgets/global_white_card.dart';
import '../../../widgets/gradient_button.dart';
import '../controller/lesson_controller.dart';
import '../widgets/page_number_card.dart';

class LessonView extends StatelessWidget {
  final String? lessonTitle;
  final String? pdfPath;
  final int? contentId;

  const LessonView({
    super.key,
    this.lessonTitle,
    this.pdfPath,
    this.contentId,
  });

  @override
  Widget build(BuildContext context) {
    final LessonController lessonController =
        Get.put(LessonController(dioClient: Get.find<DioClient>()));

    lessonController.loadPDF(pdfPath!);
    bool lessonComplated = false;
    

    return BaseScreen(
      child: Scaffold(
        appBar: const GlobalHeader(),
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
              child: GlobalWhiteCard(
                title: lessonTitle ?? '',
                footer: Obx(() {
                  if (lessonController.totalPages.value > 0) {
                    return GradientButton(
                      text: AppStrings.evaluateUnderstanding,
                      onPressed: () {
                        Get.toNamed(Routes.evaluation, arguments: EvaluationParam(contentId: contentId, isLesonCompleted: lessonComplated));
                      },
                    );
                  }
                  return const SizedBox.shrink();
                }),
                child: Obx(() {
                  if (lessonController.isLoading.value) {
                    return const Center(child: GlobalLoader());
                  }

                  if (lessonController.isError.value) {
                    return GlobalErrorHandler(
                      onRetry: () {
                        lessonController.loadPDF(pdfPath!);
                      },
                    );
                  }

                  if (lessonController.localPath.isEmpty) {
                    return GlobalEmptyHandler(
                      onBack: () {
                        Get.back();
                      },
                    );
                  }

                  return PDFView(
                    filePath: lessonController.localPath.value,
                    enableSwipe: true,
                    swipeHorizontal: false,
                    autoSpacing: true,
                    pageSnap: true,
                    fitPolicy: FitPolicy.BOTH,
                    preventLinkNavigation: false,
                    onError: (error) {
                      debugPrint("PDF Error: $error");
                      lessonController.isError.value = true;
                    },
                    onRender: (pages) {
                      lessonController.updateTotalPages(pages ?? 0);
                      debugPrint("PDF Rendered with $pages pages");
                    },
                    onPageChanged: (page, _) {
                      lessonController.updateCurrentPage(page ?? 0);
                      if (lessonController.totalPages.value == (page ?? 0) + 1) {
                          lessonComplated = true;
                      }
                    },
                  );
                }),
              ),
            ),
            Obx(() {
              if (lessonController.totalPages.value > 0) {
                return PageNumberCard(
                  currentPage: lessonController.currentPage.value + 1,
                  totalPages: lessonController.totalPages.value,
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      ),
    );
  }
}
