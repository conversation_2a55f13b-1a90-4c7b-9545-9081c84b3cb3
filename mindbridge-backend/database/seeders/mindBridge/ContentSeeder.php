<?php

namespace Database\Seeders\mindBridge;

use App\Models\MindBridge\Chapter;
use App\Models\MindBridge\Content;
use Illuminate\Database\Seeder;
use App\Models\MindBridge\Test;
use App\Models\MindBridge\Question;

class ContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        /**
         * On prépare un tableau structuré de données à insérer :
         * - Chaque élément représente un "Chapter"
         *   - Chaque "Chapter" contient plusieurs "contents" 
         *     - Chacun de ces "contents" peut avoir plusieurs "tests"
         *       - Chaque test possède des "steps" (questions + options)
         */
        $chaptersData = [
            // ============================================
            // CHAPITRE 1 (Français, niveau Terminale -> ex. niveau_id = 3)
            // ============================================
            [
                'chapter'    => 'Chap-1 Français Terminale',
                'description'=> 'Chapitre dédié aux notions avancées de français en Terminale',
                'matiere_id' => 1, // Français
                'niveau_id'  => 1, // Terminale (selon ton seeder NiveauxMatiere)
                'contents'   => [
                    // -- 1er contenu
                    [
                        'title'       => 'Les Fonctions Syntaxiques',
                        'description' => 'Approfondissement sur les fonctions dans la phrase (sujet, COD, COI, etc.)',
                        'content'     => 'mindbridge/uploads/content/syntaxe_fonctions.pdf',
                        'tests'       => [
                            [
                                'difficulty_level' => 'tres_facile',
                                'title'       => 'Test : Fonctions Syntaxiques (Partie 1)',
                                'description' => 'Évalue tes connaissances sur les fonctions syntaxiques (niveau avancé).',
                                'type'        => 'test_content',
                                'target'      => null,  // Sera complété dynamiquement avec l’ID du content
                                'category_id' => 4,
                                'matiere_id'  => 1, // Français
                                'niveau_id'   => 1, // Terminale
                                'created_by'  => 1,
                                'steps'       => [
                                    [
                                        'order'     => 1,
                                        'type'      => 'one',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => "Qu'est-ce qu'une fonction syntaxique ?",
                                            'description' => 'Choisis la réponse correcte.',
                                            'options'     => [
                                                [
                                                    'name'      => 'Un élément grammatical qui remplit un rôle précis dans une phrase.', 
                                                    'icon'      => 'U+1F4DA', 
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => 'Un type de mot comme un verbe ou un adjectif.', 
                                                    'icon' => 'U+1F4AC'
                                                ],
                                                [
                                                    'name' => 'Une règle de conjugaison.', 
                                                    'icon' => 'U+1F4D6'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 2,
                                        'type'      => 'many',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quels sont des exemples de fonctions syntaxiques ?',
                                            'description' => 'Sélectionne toutes les bonnes réponses.',
                                            'options'     => [
                                                ['name' => 'Sujet',             'icon' => 'U+2705', 'isCorrect' => true],
                                                ['name' => "Complément d'objet", 'icon' => 'U+2705', 'isCorrect' => true],
                                                ['name' => "Présent de l'indicatif", 'icon' => 'U+1F4D6'],
                                                ['name' => 'Adjectif qualificatif',    'icon' => 'U+1F4AC'],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 3,
                                        'type'      => 'true_false',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quels sont des exemples de fonctions syntaxiques ?',
                                            'description' => 'Sélectionne toutes les bonnes réponses.',
                                            'is_true' => true,
                                            'answer' => true,
                                            'is_false' => false,
                                        ],
                                    ],
                                ],
                            ],
                            [
                                'difficulty_level' => 'tres_facile',
                                'title'       => 'Test : Fonctions Syntaxiques (Partie 2)',
                                'description' => 'Seconde évaluation sur les fonctions syntaxiques',
                                'type'        => 'test_content',
                                'category_id' => 4,
                                'matiere_id'  => 1,
                                'niveau_id'   => 1,
                                'created_by'  => 1,
                                'steps'       => [
                                    [
                                        'order'     => 1,
                                        'type'      => 'one',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quelle est la fonction du mot en majuscules : "JEAN aime la musique." ?',
                                            'description' => 'Sélectionne la réponse correcte.',
                                            'options'     => [
                                                [
                                                    'name'      => 'Sujet', 
                                                    'icon'      => 'U+1F464', 
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => 'Complément d’objet direct', 
                                                    'icon' => 'U+1F4AC'
                                                ],
                                                [
                                                    'name' => 'Apposition', 
                                                    'icon' => 'U+1F4D6'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 2,
                                        'type'      => 'many',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Dans la phrase suivante : "La voiture de mon père est rouge", quelles sont les fonctions possibles ?',
                                            'description' => 'Coche toutes les fonctions exactes.',
                                            'options'     => [
                                                [
                                                    'name'      => "de mon père = complément du nom",
                                                    'icon'      => 'U+1F4DA', 
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name'      => "La voiture = sujet",
                                                    'icon'      => 'U+1F698',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name'      => "est = verbe d’état (copule)",
                                                    'icon'      => 'U+1F6B2',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name'      => "rouge = COD",
                                                    'icon'      => 'U+1F534'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 3,
                                        'type'      => 'true_false',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quels sont des exemples de fonctions syntaxiques ?',
                                            'description' => 'Sélectionne toutes les bonnes réponses.',
                                            'is_true' => true,
                                            'answer' => true,
                                            'is_false' => false,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    // -- 2e contenu
                    [
                        'title'       => 'Conjugaison des Verbes Complexes',
                        'description' => 'Points spécifiques sur la conjugaison des verbes irréguliers et formes composées.',
                        'content'     => 'mindbridge/uploads/content/conjugaison_avancee.pdf',
                        'tests'       => [
                            [
                                'difficulty_level' => 'tres_facile',
                                'title'       => 'Test Conjugaison : Verbes irréguliers',
                                'description' => 'Teste tes connaissances sur la conjugaison des verbes irréguliers.',
                                'type'        => 'test_content',
                                'category_id' => 4,
                                'matiere_id'  => 1,
                                'niveau_id'   => 1,
                                'created_by'  => 1,
                                'steps'       => [
                                    [
                                        'order'     => 1,
                                        'type'      => 'one',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quel est le verbe conjugué dans cette phrase : "Nous irons au marché demain." ?',
                                            'description' => 'Choisis la bonne réponse.',
                                            'options'     => [
                                                [
                                                    'name'      => 'irons', 
                                                    'icon'      => 'U+1F4C5', 
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => 'nous', 
                                                    'icon' => 'U+1F464'
                                                ],
                                                [
                                                    'name' => 'marché', 
                                                    'icon' => 'U+1F6CD'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 2,
                                        'type'      => 'many',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quelles formes verbales appartiennent au futur simple ?',
                                            'description' => 'Sélectionne toutes les réponses correctes.',
                                            'options'     => [
                                                [
                                                    'name'      => "j'irai", 
                                                    'icon'      => 'U+23F3',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name'      => 'je venais', 
                                                    'icon'      => 'U+1F4D6'
                                                ],
                                                [
                                                    'name'      => 'ils auront', 
                                                    'icon'      => 'U+1F511', 
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name'      => 'nous allions', 
                                                    'icon'      => 'U+1F6B2'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 3,
                                        'type'      => 'true_false',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quels sont des exemples de fonctions syntaxiques ?',
                                            'description' => 'Sélectionne toutes les bonnes réponses.',
                                            'is_true' => true,
                                            'answer' => true,
                                            'is_false' => false,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],

            // ============================================
            // CHAPITRE 2 (Mathématiques, niveau 7C -> ex. niveau_id = 1)
            // ============================================
            [
                'chapter'    => 'Chap-1 Maths 7C',
                'description'=> 'Bases de calcul et géométrie simple pour la 7C',
                'matiere_id' => 2, // Mathématiques (par exemple)
                'niveau_id'  => 1, // 7C (cf. ton NiveauxMatiereSeederCentrale)
                'contents'   => [
                    // -- 1er contenu
                    [
                        'title'       => 'Opérations de base (addition, soustraction, multiplication, division)',
                        'description' => 'Révision des opérations élémentaires',
                        'content'     => 'mindbridge/uploads/content/operations_base.pdf',
                        'tests'       => [
                            [
                                'difficulty_level' => 'tres_facile',
                                'title'       => 'Test : Opérations de base',
                                'description' => 'Valide tes acquis sur les quatre opérations fondamentales.',
                                'type'        => 'test_content',
                                'category_id' => 4,
                                'matiere_id'  => 2,
                                'niveau_id'   => 1,
                                'created_by'  => 1,
                                'steps'       => [
                                    [
                                        'order'     => 1,
                                        'type'      => 'one',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Combien font 12 + 7 ?',
                                            'description' => 'Choisis la bonne réponse.',
                                            'options'     => [
                                                [
                                                    'name'      => '19', 
                                                    'icon'      => 'U+2728', 
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => '21', 
                                                    'icon' => 'U+1F4AF'
                                                ],
                                                [
                                                    'name' => '14', 
                                                    'icon' => 'U+1F600'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 2,
                                        'type'      => 'one',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quelle est la résultat de (24 / 6) x 3 ?',
                                            'description' => 'Une seule réponse correcte.',
                                            'options'     => [
                                                [
                                                    'name' => '12', 
                                                    'icon' => 'U+1F4AF'
                                                ],
                                                [
                                                    'name'      => '2', 
                                                    'icon'      => 'U+1F4A1'
                                                ],
                                                [
                                                    'name'      => '12', 
                                                    'icon'      => 'U+1F4A9',
                                                    'isCorrect' => true
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 3,
                                        'type'      => 'true_false',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quels sont des exemples de fonctions syntaxiques ?',
                                            'description' => 'Sélectionne toutes les bonnes réponses.',
                                            'is_true' => true,
                                            'answer' => true,
                                            'is_false' => false,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    // -- 2e contenu
                    [
                        'title'       => 'Découverte de la géométrie',
                        'description' => 'Introduction aux formes géométriques de base : carré, rectangle, triangle...',
                        'content'     => 'mindbridge/uploads/content/geometrie_debut.pdf',
                        'tests'       => [
                            [
                                'difficulty_level' => 'tres_facile',
                                'title'       => 'Test : Formes géométriques',
                                'description' => 'Identifie et nomme les formes géométriques simples.',
                                'type'        => 'test_content',
                                'category_id' => 4,
                                'matiere_id'  => 2,
                                'niveau_id'   => 1,
                                'created_by'  => 1,
                                'steps'       => [
                                    [
                                        'order'     => 1,
                                        'type'      => 'one',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => "Combien de côtés possède un triangle équilatéral ?",
                                            'description' => 'Choisis la bonne réponse.',
                                            'options'     => [
                                                [
                                                    'name' => '3', 
                                                    'icon' => 'U+1F4C8',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => '4', 
                                                    'icon' => 'U+1F4D6'
                                                ],
                                                [
                                                    'name' => '6', 
                                                    'icon' => 'U+1F4A1'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 2,
                                        'type'      => 'many',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Sélectionne les propriétés vraies pour un carré parfait.',
                                            'description' => 'Coche toutes les bonnes réponses.',
                                            'options'     => [
                                                [
                                                    'name'      => '4 côtés de même longueur',
                                                    'icon'      => 'U+25A1',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name'      => '4 angles droits (90°)',
                                                    'icon'      => 'U+27A1',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => 'Plus long que large',
                                                    'icon' => 'U+1F4A1'
                                                ],
                                                [
                                                    'name' => '5 sommets',
                                                    'icon' => 'U+1F52E'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 3,
                                        'type'      => 'true_false',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quels sont des exemples de fonctions syntaxiques ?',
                                            'description' => 'Sélectionne toutes les bonnes réponses.',
                                            'is_true' => true,
                                            'answer' => true,
                                            'is_false' => false,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],

            // ============================================
            // CHAPITRE 3 (Sciences, niveau 6D -> ex. niveau_id = 6)
            // ============================================
            [
                'chapter'    => 'Chap-2 Sciences 6D',
                'description'=> 'Notions fondamentales de sciences physiques et biologiques pour la 6D',
                'matiere_id' => 3, // Sciences (ex. si 3 = Physique-Chimie ou SVT selon ton paramétrage)
                'niveau_id'  => 1,
                'contents'   => [
                    [
                        'title'       => 'Introduction à la biologie cellulaire',
                        'description' => 'Découvrir la cellule, son fonctionnement et ses composants',
                        'content'     => 'mindbridge/uploads/content/biologie_cellulaire_intro.pdf',
                        'tests'       => [
                            [
                                'difficulty_level' => 'tres_facile',
                                'title'       => 'Test : Biologie cellulaire',
                                'description' => 'Évalue tes connaissances de base sur la cellule.',
                                'type'        => 'test_content',
                                'category_id' => 4,
                                'matiere_id'  => 3,
                                'niveau_id'   => 1,
                                'created_by'  => 1,
                                'steps'       => [
                                    [
                                        'order'     => 1,
                                        'type'      => 'one',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => "La cellule est l'unité de base du vivant. Vrai ou Faux ?",
                                            'description' => 'Choisis la bonne réponse.',
                                            'options'     => [
                                                [
                                                    'name'      => 'Vrai', 
                                                    'icon'      => 'U+2705', 
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => 'Faux', 
                                                    'icon' => 'U+1F4A9'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 2,
                                        'type'      => 'many',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => "Quels éléments trouve-t-on dans une cellule eucaryote ?",
                                            'description' => 'Sélectionne tous les éléments corrects.',
                                            'options'     => [
                                                [
                                                    'name'      => 'Un noyau', 
                                                    'icon'      => 'U+1F9E0', 
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name'      => 'Des ribosomes', 
                                                    'icon'      => 'U+1F9EC',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => 'Des vacuoles (dans les cellules végétales)', 
                                                    'icon' => 'U+1F331',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => 'Une trompe', 
                                                    'icon' => 'U+1F418'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 3,
                                        'type'      => 'true_false',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quels sont des exemples de fonctions syntaxiques ?',
                                            'description' => 'Sélectionne toutes les bonnes réponses.',
                                            'is_true' => true,
                                            'answer' => true,
                                            'is_false' => false,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    [
                        'title'       => 'Matière, atomes et molécules',
                        'description' => 'Bases de la chimie : structure de la matière, classification, etc.',
                        'content'     => 'mindbridge/uploads/content/matiere_chimie_base.pdf',
                        'tests'       => [
                            [
                                'difficulty_level' => 'tres_facile',
                                'title'       => 'Test : Introduction à la chimie',
                                'description' => 'Vérifie tes notions élémentaires sur les atomes, molécules et leur organisation.',
                                'type'        => 'test_content',
                                'category_id' => 4,
                                'matiere_id'  => 3,
                                'niveau_id'   => 1,
                                'created_by'  => 1,
                                'steps'       => [
                                    [
                                        'order'     => 1,
                                        'type'      => 'one',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quel est le plus petit constituant de la matière ?',
                                            'description' => 'Choisis la réponse correcte.',
                                            'options'     => [
                                                [
                                                    'name'      => 'L’atome',
                                                    'icon'      => 'U+1F52C',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => 'La molécule',
                                                    'icon' => 'U+1F9EA'
                                                ],
                                                [
                                                    'name' => 'La cellule',
                                                    'icon' => 'U+1F9E0'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 2,
                                        'type'      => 'many',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Sélectionne les éléments chimiques ci-dessous :',
                                            'description' => 'Plusieurs réponses peuvent être correctes.',
                                            'options'     => [
                                                [
                                                    'name'      => 'Hydrogène (H)',
                                                    'icon'      => 'U+1F4A7',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name'      => 'Oxygène (O)',
                                                    'icon'      => 'U+1F4A8',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => 'Azote (N)',
                                                    'icon' => 'U+1F4A5',
                                                    'isCorrect' => true
                                                ],
                                                [
                                                    'name' => 'Napoléon (Np)',
                                                    'icon' => 'U+1F934'
                                                ],
                                            ],
                                        ],
                                    ],
                                    [
                                        'order'     => 3,
                                        'type'      => 'true_false',
                                        'condition' => null,
                                        'question'  => [
                                            'type'        => 'text',
                                            'content'     => 'Quels sont des exemples de fonctions syntaxiques ?',
                                            'description' => 'Sélectionne toutes les bonnes réponses.',
                                            'is_true' => true,
                                            'answer' => true,
                                            'is_false' => false,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        // ---------------------------------------------
        // On crée les chapitres, contenus, tests, steps, etc.
        // ---------------------------------------------
        foreach ($chaptersData as $chapData) {
            // 1) Créer un chapitre
            $chapter = Chapter::create([
                'chapter'    => $chapData['chapter'],
                'description'=> $chapData['description'],
                'matiere_id' => $chapData['matiere_id'],
                'niveau_id'  => $chapData['niveau_id'],
            ]);

            // 2) Boucler sur les contenus du chapitre
            if (! empty($chapData['contents'])) {
                foreach ($chapData['contents'] as $contentData) {
                    // Récupérer l'éventuelle liste de tests
                    $testsData = $contentData['tests'] ?? [];
                    unset($contentData['tests']); // On retire la clé "tests" pour créer le content

                    // On crée le contenu
                    $content = Content::create([
                        'chapter_id'  => $chapter->id,
                        'niveau_id'   => $chapData['niveau_id'],
                        'matiere_id'  => $chapData['matiere_id'],
                        'title'       => $contentData['title'],
                        'description' => $contentData['description'],
                        'content'     => $contentData['content'],
                    ]);

                    // 3) Pour chaque contenu, on crée ses tests
                    foreach ($testsData as $testData) {
                        $stepsData = $testData['steps'] ?? [];
                        unset($testData['steps']);

                        // Remplir la clé "target" si besoin
                        // Dans ton code, "target" semble pointer vers l’ID du content
                        $testData['content_id'] = $content->id;

                        // On crée le test
                        $test = Test::create($testData);

                        // 4) Créer les steps/questions de chaque test
                        foreach ($stepsData as $stepItem) {
                            $questionData = $stepItem['question'];
                            $optionsData  = $questionData['options'] ?? [];
                            unset($questionData['options']);

                            // On crée la question
                            $question = Question::create($questionData);

                            if ($stepItem['type'] !== 'true_false') {
                                // Créer les options associées
                                foreach ($optionsData as $opt) {
                                    $question->options()->create($opt);
                                }
                            }

                            // Préparer le step en liant la question
                            $stepItem['question_id'] = $question->id;
                            unset($stepItem['question']);

                            // On rattache le step au test
                            $test->steps()->create($stepItem);
                        }
                    }
                }
            }
        }
    }
}
