import { HostListener, Injectable, inject } from "@angular/core";
import { Store } from "@ngrx/store";
import { AuthService } from "../services/auth.services";
import { Router } from "@angular/router";
import { catchError, finalize, map, switchMap, tap } from 'rxjs/operators';
import { LoginAction, LoginActionError, LoginActionSuccess } from "./auth.actions";
import { AuthResponse } from "../models/auth-response";
import { of } from "rxjs";
import { AppState } from "../../../core/app.state";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { CommonService } from "../../shared/common.service";
import { SetLoading } from "../../shared/store/shared.actions";
import { LocalStorageService } from "../../shared/localstorage.service";
import { HttpErrorResponse } from "@angular/common/http";


@Injectable()
export class AuthEffects {
    private actions$ = inject(Actions);
    store = inject<Store<AppState>>(Store);
    private authService = inject(AuthService);
    private router = inject(Router);
    private commonService = inject(CommonService);


    isScreenSizeValid(): boolean {
        return window.innerWidth >= 600;
    }

    @HostListener('window:resize', ['$event'])
    onResize() {
        this.isScreenSizeValid();
    }

    // login effects
    LoginEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(LoginAction),
            switchMap((action) =>
                this.authService.login(action.payload).pipe(
                    map((payload: AuthResponse) => LoginActionSuccess({ payload })),
                    tap((res) => {
                        if (!res.payload.user.active) {
                            this.commonService.showToast("Votre compte n'est pas activé. Veuillez contacter l'administrateur pour activer votre compte.!", 'ERROR')
                        } else {
                            LocalStorageService.put('currentUser', res.payload),
                            
                            LocalStorageService.put('schoolName', res.payload.schoolName),
                            LocalStorageService.put('bankilyCode', res.payload.bankilyCode),
                            
                            this.router.navigate(this.isScreenSizeValid() ? ['tests'] : ['etudiants']);
                        }
                    }),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            LoginActionError()
                        ).pipe(
                            tap(() => this.commonService.showToast(err.error, 'ERROR'))
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    )

}