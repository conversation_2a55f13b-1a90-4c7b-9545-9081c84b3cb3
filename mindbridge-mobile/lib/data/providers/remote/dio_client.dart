import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:mindbridge/app/routes.dart';

import '../../../core/constants/api_routes.dart';
import '../local/secure_storage_service.dart';

class DioClient {
  final Dio _dio;
  final SecureStorageService _secureStorage;

  final List<String> excludedUnauthorizedRoutes = [
    ApiRoutes.login,
  ];

  DioClient({required SecureStorageService secureStorage})
      : _secureStorage = secureStorage,
        _dio = Dio(
          BaseOptions(
              baseUrl: ApiRoutes.apiBaseUrl,
              connectTimeout: const Duration(minutes: 1),
              receiveTimeout: const Duration(seconds: 30),
              headers: {
                'Content-Type': 'application/json',
              },
              validateStatus: (status) {
                if (status == 404 || status == 403) return true;
                return status != null && status < 500;
              }),
        ) {
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        await _addAuthorizationHeader(options);
        _logRequest(options);
        handler.next(options);
      },
      onResponse: (response, handler) {
        _logResponse(response);
        handler.next(response);
      },
      onError: (DioException e, handler) async {
        _logError(e);

        if (e.response?.statusCode == 401 &&
            !_isExcludedRoute(e.requestOptions.path)) {
          await _handleUnauthorized(e);
        }
        handler.next(e);
      },
    ));
  }

  Dio get dio => _dio;

  Future<void> _addAuthorizationHeader(RequestOptions options) async {
    final token = await _secureStorage.read('auth_token');

    print("Token: $token");
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
  }

  bool _isExcludedRoute(String path) {
    return excludedUnauthorizedRoutes.any((route) => path.contains(route));
  }

  Future<void> _handleUnauthorized(DioException e) async {
    debugPrint('Unauthorized response: ${e.response?.data}');
    await _secureStorage.clear();
    Get.offAllNamed(Routes.login);
  }

  // Logging Functions
  void _logRequest(RequestOptions options) {
    debugPrint('--- Request ---');
    debugPrint('URL: ${options.uri}');
    debugPrint('Headers: ${options.headers}');
    debugPrint('Body: ${options.data}');
  }

  void _logResponse(Response response) {
    debugPrint('--- Response ---');
    debugPrint('URL: ${response.requestOptions.uri}');
    debugPrint('Status Code: ${response.statusCode}');
    debugPrint('Body: ${response.data}');
  }

  void _logError(DioException e) {
    debugPrint('--- Error ---');
    debugPrint('URL: ${e.requestOptions.uri}');
    debugPrint('Status Code: ${e.response?.statusCode}');
    debugPrint('Error Data: ${e.response?.data}');
  }
}
