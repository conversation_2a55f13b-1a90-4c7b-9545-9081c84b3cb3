// To parse this JSON data, do
//
//     final testProfiling = testProfilingFromJson(jsonString);

import 'dart:convert';

TestProfiling testProfilingFromJson(String str) =>
    TestProfiling.fromJson(json.decode(str));

String testProfilingToJson(TestProfiling data) => json.encode(data.toJson());

class TestProfiling {
  Test? test;

  TestProfiling({
    this.test,
  });

  factory TestProfiling.fromJson(Map<String, dynamic> json) => TestProfiling(
        test: json["test"] == null ? null : Test.fromJson(json["test"]),
      );

  Map<String, dynamic> toJson() => {
        "test": test?.toJson(),
      };
}

class Test {
  int? id;
  String? title;
  String? description;
  dynamic target;
  String? type;
  DateTime? createdAt;
  DateTime? updatedAt;
  List<Step>? steps;

  Test({
    this.id,
    this.title,
    this.description,
    this.target,
    this.type,
    this.createdAt,
    this.updatedAt,
    this.steps,
  });

  factory Test.fromJson(Map<String, dynamic> json) => Test(
        id: json["id"],
        title: json["title"],
        description: json["description"],
        target: json["target"],
        type: json["type"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        steps: json["steps"] == null
            ? []
            : List<Step>.from(json["steps"]!.map((x) => Step.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "target": target,
        "type": type,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "steps": steps == null
            ? []
            : List<dynamic>.from(steps!.map((x) => x.toJson())),
      };
}

class Step {
  int? id;
  int? testId;
  int? questionId;
  dynamic required;
  String? type;
  dynamic condition;
  int? order;
  DateTime? createdAt;
  DateTime? updatedAt;
  Question? question;

  Step({
    this.id,
    this.testId,
    this.questionId,
    this.required,
    this.type,
    this.condition,
    this.order,
    this.createdAt,
    this.updatedAt,
    this.question,
  });

  bool get requiredBool {
    if (required is bool) return required as bool;
    if (required is int) return required == 1;
    return false;
  }

  factory Step.fromJson(Map<String, dynamic> json) => Step(
        id: json["id"],
        testId: json["test_id"],
        questionId: json["question_id"],
        required: json["required"],
        type: json["type"],
        condition: json["condition"],
        order: json["order"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        question: json["question"] == null
            ? null
            : Question.fromJson(json["question"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "test_id": testId,
        "question_id": questionId,
        "required": required,
        "type": type,
        "condition": condition,
        "order": order,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "question": question?.toJson(),
      };
}

class Question {
  int? id;
  String? type;
  String? content;
  String? description;
  DateTime? createdAt;
  DateTime? updatedAt;
  List<Option>? options;

  Question({
    this.id,
    this.type,
    this.content,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.options,
  });

  factory Question.fromJson(Map<String, dynamic> json) => Question(
        id: json["id"],
        type: json["type"],
        content: json["content"],
        description: json["description"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        options: json["options"] == null
            ? []
            : List<Option>.from(
                json["options"]!.map((x) => Option.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "type": type,
        "content": content,
        "description": description,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "options": options == null
            ? []
            : List<dynamic>.from(options!.map((x) => x.toJson())),
      };
}

class Option {
  int? id;
  String? name;
  String? icon;
  int? questionId;
  DateTime? createdAt;
  DateTime? updatedAt;

  Option({
    this.id,
    this.name,
    this.icon,
    this.questionId,
    this.createdAt,
    this.updatedAt,
  });

  factory Option.fromJson(Map<String, dynamic> json) => Option(
        id: json["id"],
        name: json["name"],
        icon: json["icon"],
        questionId: json["question_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "icon": icon,
        "question_id": questionId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
