import { TestResponse } from './models/test-response';
import { patchState, signalState } from '@ngrx/signals';
import { inject, Injectable } from '@angular/core';
import { TestManagementService } from './test-management.service';
import { Observable, of, pipe } from 'rxjs';
import { catchError, finalize, switchMap, tap, take } from 'rxjs/operators';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { SetLoading } from '../shared/store/shared.actions';
import { Store } from '@ngrx/store';
import { AppState } from '../../core/app.state';
import { ApiResponse } from '../../core/models/api-response';
import {
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from '../shared/global.config';
import { Pagination } from '../shared/helpers/query.helper';
import { ITestSearchForm, TestSearchForm } from './models/test-search-request';
import {
  ITestCategoriesBoardResponse,
  TestCategoriesBoardResponse,
} from './models/test-categories-board';
import { TestCreationRequestWrapper } from './models/test-creation-form';
import * as ListsTestsManagement from './store/test-management.actions';
import { HttpErrorResponse } from '@angular/common/http';
import { CommonService } from '../shared/common.service';
import { TestCreationResponseWrapper } from './models/test-creation-response';
import { Router } from '@angular/router';
import {
  TestUpdateRequestWrapper,
  TestUpdateRequestWrapperComplete,
} from './models/update-creation-form';
import { TestUpdateResponseWrapper } from './models/test-update-response';

export type TestManagementState = {
  tests: TestResponse[];
  testsSearchForm: ITestSearchForm;
  current_page: number;
  total_pages: number;
  per_page: number;
  total_items: number;
  testDetails: TestResponse;
  selectedTest: TestResponse;
  testCategoriesBoard: ITestCategoriesBoardResponse;
};

export const initialTestManagementState: TestManagementState = {
  tests: [],
  testsSearchForm: new TestSearchForm(),
  current_page: DEFAULT_CURRENT_PAGE,
  total_pages: 1,
  per_page: DEFAULT_PAGE_SIZE,
  total_items: 0,
  testDetails: null,
  selectedTest: null,
  testCategoriesBoard: new TestCategoriesBoardResponse(),
};

@Injectable()
export class TestManagementStore {
  store = inject<Store<AppState>>(Store);

  readonly state = signalState<TestManagementState>(initialTestManagementState);
  #testManagementService = inject(TestManagementService);
  #commonService = inject(CommonService);
  #router = inject(Router);

  setSearchForm = (testsSearchForm: ITestSearchForm) =>
    patchState(this.state, { testsSearchForm });

  setCurrentPage = (current_page: number) =>
    patchState(this.state, { current_page });

  getTests = rxMethod<void>(
    pipe(
      switchMap(() => {
        const pagination: Pagination = {
          current_page: this.state.current_page(),
          per_page: this.state.per_page(),
        };
        return this.#testManagementService
          .getListeTests(this.state.testsSearchForm(), pagination)
          .pipe(
            tap((response: ApiResponse<TestResponse>) => {
              patchState(this.state, {
                tests: response.data,

                current_page: response.current_page,
                per_page: response.per_page,
                total_pages: response.total_pages,
                total_items: response.total_items,
              });
            }),
            finalize(() => {
              this.store.dispatch(SetLoading({ isAppLoading: false }));
            })
          );
      }),
      finalize(() => {})
    )
  );

  getTestDetail = rxMethod<number>(
    pipe(
      switchMap((testId: number) =>
        this.#testManagementService.getTestById(testId).pipe(
          tap((testDetails: TestResponse) => {
            patchState(this.state, { testDetails });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      )
    )
  );



  createTest = rxMethod<TestCreationRequestWrapper>(
    pipe(
      switchMap((testCreationRequestWrapper: TestCreationRequestWrapper) =>
        this.#testManagementService.createTest(testCreationRequestWrapper).pipe(
          tap((response: TestCreationResponseWrapper) => {
            this.#commonService.showToast('Test crée avec succès', 'SUCCESS');
            this.#router.navigate([`/add-test/${response.object.id}`]);
          }),
          catchError((err: HttpErrorResponse) =>
            of(ListsTestsManagement.AddTestManagementError()).pipe(
              tap(() => {
                console.log('err', err.error);
                this.#commonService.showToast(err.error, 'ERROR');
              })
            )
          ),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      )
    )
  );
  getTestCategoriesBoard = rxMethod<void>(
    pipe(
      take(1),
      tap(() => {
        console.log('[TestManagementStore] getTestCategoriesBoard rxMethod called');
      }),
      switchMap(() => {
        console.log('[TestManagementStore] Fetching test categories board');
        return this.#testManagementService.getTestCategoriesBoard().pipe(
          tap((testCategoriesBoard: ITestCategoriesBoardResponse) => {
            console.log('[TestManagementStore] testCategoriesBoard received: ', testCategoriesBoard);
            console.log('[TestManagementStore] testCategoriesBoard type:', typeof testCategoriesBoard);
            console.log('[TestManagementStore] testCategoriesBoard keys:', Object.keys(testCategoriesBoard));
            console.log('[TestManagementStore] mental_health object:', testCategoriesBoard?.mental_health);
            console.log('[TestManagementStore] mental_health type:', typeof testCategoriesBoard?.mental_health);
            console.log('[TestManagementStore] mental_health keys:', Object.keys(testCategoriesBoard?.mental_health || {}));
            console.log('[TestManagementStore] mental_health children:', testCategoriesBoard?.mental_health?.children);
            patchState(this.state, { testCategoriesBoard });
          }),
          catchError((error: any) => {
            console.error('[TestManagementStore] Error fetching categories board:', error);
            console.error('[TestManagementStore] Error status:', error.status);
            console.error('[TestManagementStore] Error message:', error.message);
            console.error('[TestManagementStore] Error response body:', error.error);
            this.store.dispatch(SetLoading({ isAppLoading: false }));
            return of(new TestCategoriesBoardResponse());
          }),
          finalize(() => {
            console.log('[TestManagementStore] Finalize - setting loading to false');
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        );
      })
    )
  );

  deleteTest = rxMethod<number>(
    pipe(
      switchMap((id: number) =>
        this.#testManagementService.deleteTest(id).pipe(
          tap(() => {
            this.#commonService.showToast(
              'Test supprimé avec succès',
              'SUCCESS'
            );
            this.getTests();
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      )
    )
  );

  editTest = rxMethod<TestUpdateRequestWrapperComplete>(
    pipe(
      switchMap(
        (testUpdateRequestWrapperComplete: TestUpdateRequestWrapperComplete) =>
          this.#testManagementService
            .editTest(
              testUpdateRequestWrapperComplete.id,
              testUpdateRequestWrapperComplete.testUpdateRequestWrapper
            )
            .pipe(
              tap((response: TestUpdateResponseWrapper) => {
                this.#commonService.showToast(
                  'Test modifié avec succès',
                  'SUCCESS'
                );
                this.#router.navigate([`/add-test/${response.object.id}`]);
              }),
              finalize(() => {
                this.store.dispatch(SetLoading({ isAppLoading: false }));
              })
            )
      )
    )
  );
}
