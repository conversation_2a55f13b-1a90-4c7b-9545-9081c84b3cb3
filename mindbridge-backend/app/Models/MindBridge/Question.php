<?php

namespace App\Models\MindBridge;

use App\Models\MatiereCentrale;
use App\Models\NiveauCentrale;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    use HasFactory;
    protected $connection = 'mind_bridge';

    protected $table = 'questions';

    protected $fillable = [
        'type',
        'description',
        'content',
        'image_path',
        'is_required',
        'difficulty',
        'matier_id',
        'niveau_id',
        'response_type',
        'is_true',
        'is_false',
        'answer'
    ];  

    protected $casts = [
        'is_required' => 'boolean',
        'is_true' => 'boolean',
        'answer' => 'boolean',
        'is_false' => 'boolean'
    ];

    public function getImagePathAttribute($value)
    {
        return $value ? asset('storage/' . $value) : null;
    }

    
    public function steps()
    {
        return $this->hasMany(Step::class, 'question_id');
    }

    public function matier()
    {
        return $this->belongsTo(MatiereCentrale::class, 'matier_id');
    }

    public function niveau()
    {
        return $this->belongsTo(NiveauCentrale::class, 'niveau_id');
    }

    public function options()
    {
        return $this->hasMany(Option::class);
    }
}
