<div class="mt-2 flex flex-col items-center px-7">
  <form
    class="flex w-full flex-row justify-end"
    [formGroup]="form"
    (ngSubmit)="searchEtudiants()"
  >
    <div
      class="my-4 flex flex-col items-center justify-center md:flex-row md:items-end md:justify-end"
    >
      <div class="mr-2 rounded-lg border-2 border-gray-200 bg-white p-2">
        <select
          class="mr-10 w-full"
          formControlName="school"
          (change)="searchEtudiants()"
        >
          <option [ngValue]="OPTION_ALL" selected>
            Tous les établissements
          </option>
          @for (school of allSchools(); track school.id) {
            <option [ngValue]="school.id">
              {{ school.name }}
            </option>
          }
        </select>
      </div>
      <div class="mr-2 rounded-lg border-2 border-gray-200 bg-white p-2">
        <select
          class="w-48"
          formControlName="niveau"
          (change)="searchEtudiants()"
        >
          <option [ngValue]="OPTION_ALL" selected>Tous les niveaux</option>
          @for (niveau of allNiveaux(); track niveau.id) {
            <option [ngValue]="niveau.id">{{ niveau.name }}</option>
          }
        </select>
      </div>
    </div>
  </form>
  @if (allEtudiants()) {
    <div class="container mx-auto mt-2">
      <div class="overflow-x-auto rounded-lg border shadow-md">
        <table class="min-w-full divide-y divide-gray-200 bg-white">
          <tr class="flex w-full items-start justify-start text-left">
            <th class="w-64 px-4 py-3 font-medium">Etudiant</th>
            <th class="w-64 px-4 py-3 font-medium">Etablissement</th>
            <th class="w-36 px-4 py-3 font-medium">Niveau</th>
            <th class="w-44 px-4 py-3 font-medium">Santé mentale</th>
            <th class="w-44 px-4 py-3 font-medium">Taux d’engagement</th>
            <th class="w-32"></th>
          </tr>
          @for (etudiant of allEtudiants(); track etudiant.id) {
            <tr class="flex items-center justify-start">
              <td class="flex w-64 gap-2 px-4 py-3">
                <img
                  class="mr-1 h-10 w-10 rounded-full object-cover"
                  [src]="etudiant.avatar || 'assets/icones/etudiant-avatar.png'"
                  alt=""
                />
                <div class="flex flex-col">
                  <span>
                    {{ etudiant.first_name }} {{ etudiant.last_name }}
                  </span>
                  <span class="text-[#475467]">
                    {{ etudiant.email ? etudiant.email : '-' }}
                  </span>
                </div>
              </td>
              <td class="w-64 px-4 py-3 text-[#475467]">
                {{ etudiant.school.name }}
              </td>
              <td class="w-36 px-4 py-3">
                <span
                  class="rounded-xl px-2 py-1 text-xs"
                  [ngStyle]="{
                    color: etudiant.niveau.color,
                    'background-color': etudiant.niveau.background,
                  }"
                >
                  {{ etudiant.niveau.name }}
                </span>
              </td>
              @if (false) {
                <td class="flex w-44 flex-col px-4 py-3">
                  <span>{{ 'Cognitif' }}</span>
                  <span class="text-[#475467]">Score: {{ '12/20' }}</span>
                </td>
              } @else {
                <td class="flex w-44 flex-col px-4 py-3">
                  <span>-</span>
                </td>
              }
              <td class="w-44 px-4 py-3 text-[#475467]">
                {{ etudiant.taux_engagement }}
              </td>
              <td class="w-32 px-4 py-3 flex gap-2">
                <button (click)="getEtudiant(etudiant.id)" title="Voir les détails">
                  <img src="assets/icones/visibility.png" alt="Voir" />
                </button>
                <button
                  (click)="openMentalHealthTrigger(etudiant)"
                  title="Déclencher un test de santé mentale"
                  class="trigger-btn"
                >
                  <img src="assets/icones/sante_mentale.png" alt="Déclencher" />
                </button>
              </td>
            </tr>
          }
        </table>
        <hr />
        <div class="flex items-start justify-start gap-2 bg-white px-4 py-4">
          <div class="rounded-lg border border-gray-300 bg-white p-1.5">
            <button
              class="w-16 text-center font-medium"
              [disabled]="current_page() == 1"
              (click)="goToPage(Number(current_page()) - 1)"
            >
              Précédent
            </button>
          </div>
          <div class="rounded-lg border border-gray-300 bg-white p-1.5">
            <button
              class="w-14 text-center font-medium"
              [disabled]="current_page() == total_pages()"
              (click)="goToPage(Number(current_page()) + 1)"
            >
              Suivant
            </button>
          </div>
          <p class="flex-1 p-2 text-right font-medium">
            Page {{ current_page() }} de {{ total_pages() }}
          </p>
        </div>
      </div>
    </div>
  }
</div>
