export interface Options {
    name: string,
    icon: string,
    question_id: number
}

export interface Question {
    type: string,
    content: string,
    description: string,
    created_at: string,
    updated_at: string,
    options: Options[]
};

export interface Steps {
    type: string,
    condition: null,
    order: number,
    created_at: string,
    updated_at: string,
    question: Question
}

export interface Test {
    title: string,
    description: string,
    type: string,
}

export interface TestManagement {
    test: Test,
    steps: Steps[]
};