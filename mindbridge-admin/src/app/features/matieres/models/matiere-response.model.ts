export type MatiereResponse = {
  id: number;
  name_fr: string;
  name_ar: string;
  description: string;
  niveaux: Niveau[];
};
export type MatiereResponseMini = {
  id: number;
  name_fr: string;
  name_ar: string;
};

export type Niveau = {
  id: number;
  name: string;
  color: string;
  background: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
  description: string;
  pivot: Pivot;
};
export type NiveauMini = {
  id: number;
  name: string;
  color: string;
  background: string;
  description: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
};

export type Pivot = {
  matiere_id: number;
  niveau_id: number;
  created_at: Date;
  updated_at: Date;
};
