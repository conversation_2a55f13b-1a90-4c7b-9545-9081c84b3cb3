// To parse this JSON data, do
//
//     final recommandation = recommandationFrom<PERSON>son(jsonString);

import 'dart:convert';

Recommandation recommandationFromJson(String str) => Recommandation.fromJson(json.decode(str));

String recommandationToJson(Recommandation data) => json.encode(data.toJson());

class Recommandation {
    final int? matiereId;
    final String? level;
    final String? title;
    final List<Step>? steps;

    Recommandation({
        this.matiereId,
        this.level,
        this.title,
        this.steps,
    });

    factory Recommandation.fromJson(Map<String, dynamic> json) => Recommandation(
        matiereId: json["matiere_id"],
        level: json["level"],
        title: json["title"],
        steps: json["steps"] == null ? [] : List<Step>.from(json["steps"]!.map((x) => Step.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "matiere_id": matiereId,
        "level": level,
        "title": title,
        "steps": steps == null ? [] : List<dynamic>.from(steps!.map((x) => x.toJson())),
    };
}

class Step {
    final int? order;
    final bool? required;
    final String? type;
    final int? questionId;
    final Question? question;

    Step({
        this.order,
        this.required,
        this.type,
        this.questionId,
        this.question,
    });

    factory Step.fromJson(Map<String, dynamic> json) => Step(
        order: json["order"],
        required: json["required"],
        type: json["type"],
        questionId: json["question_id"],
        question: json["question"] == null ? null : Question.fromJson(json["question"]),
    );

    Map<String, dynamic> toJson() => {
        "order": order,
        "required": required,
        "type": type,
        "question_id": questionId,
        "question": question?.toJson(),
    };
}

class Question {
    final int? id;
    final String? content;
    final String? description;
    final dynamic image;
    final bool? answer;
    final String? responseType;
    final bool? isTrue;
    final bool? isFalse;
    final List<Option>? options;

    Question({
        this.id,
        this.content,
        this.description,
        this.image,
        this.answer,
        this.responseType,
        this.isTrue,
        this.isFalse,
        this.options,
    });

    factory Question.fromJson(Map<String, dynamic> json) => Question(
        id: json["id"],
        content: json["content"],
        description: json["description"],
        image: json["image"],
        answer: json["answer"],
        responseType: json["response_type"],
        isTrue: json["is_true"],
        isFalse: json["is_false"],
        options: json["options"] == null ? [] : List<Option>.from(json["options"]!.map((x) => Option.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "content": content,
        "description": description,
        "image": image,
        "answer": answer,
        "response_type": responseType,
        "is_true": isTrue,
        "is_false": isFalse,
        "options": options == null ? [] : List<dynamic>.from(options!.map((x) => x.toJson())),
    };
}

class Option {
    final int? id;
    final String? name;
    final int? isCorrect;
    final String? icon;

    Option({
        this.id,
        this.name,
        this.isCorrect,
        this.icon,
    });

    factory Option.fromJson(Map<String, dynamic> json) => Option(
        id: json["id"],
        name: json["name"],
        isCorrect: json["isCorrect"],
        icon: json["icon"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "isCorrect": isCorrect,
        "icon": icon,
    };
}
