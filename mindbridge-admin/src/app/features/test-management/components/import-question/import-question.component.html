<div class="content_import">
  <div class="head_import justify-end">
    <button class="close_import" (click)="close()">
      <img src="/assets/icones/close.svg" alt="" />
    </button>
  </div>
  <div class="input_import">
    <h2>Séléctionner questions</h2>
    <div class="search_import">
      <input
        type="text"
        placeholder="Rechercher"
        [(ngModel)]="title"
        (blur)="search()"
      />
      <img
        src="/assets/icones/search.svg"
        class="cursor-pointer"
        (click)="search()"
      />
    </div>
  </div>
  <div class="body_import">
    @for (question of questions(); track question) {
      <div class="questions_answers">
        <app-question-display-read-only [question]="question" />
        <mat-checkbox
          class="check_question"
          (change)="selectQuestion($event, question)"
        ></mat-checkbox>
      </div>
    }
  </div>

  @if (questions()) {
    <hr />
    <div class="flex items-start justify-start gap-2 bg-white px-4 py-4">
      <div class="rounded-lg border border-gray-300 bg-white p-1.5">
        <button
          class="w-16 text-center font-medium"
          [disabled]="current_page() === 1"
          (click)="goToPage(current_page() - 1)"
        >
          Précédent
        </button>
      </div>
      <div class="rounded-lg border border-gray-300 bg-white p-1.5">
        <button
          class="w-14 text-center font-medium"
          [disabled]="current_page() === total_pages()"
          (click)="goToPage(current_page() + 1)"
        >
          Suivant
        </button>
      </div>
      <p class="flex-1 p-2 text-right font-medium">
        Page {{ current_page() }} de {{ total_pages() }}
      </p>
    </div>
  }
  <div class="actions_import">
    <button class="cancel_import" (click)="close()">Annuler</button>
    <button class="save_import" (click)="save()">Enregistrer</button>
  </div>
</div>
