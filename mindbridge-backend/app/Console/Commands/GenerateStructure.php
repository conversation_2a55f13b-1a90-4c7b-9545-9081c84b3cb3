<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateStructure extends Command
{
    protected $signature = 'generate:structure {modelName} {tableName} {fields} {--connection=tenant}';
    protected $description = 'Generate basic Laravel structure for a given model';
    // call php artisan generate:structure Post posts "string:title integer:age foreign:fourniseur_id:on:fournisseurs"

    public function handle()
    {
        try {
            $modelName = $this->argument('modelName');
            $tableName = $this->argument('tableName');
            $fields = $this->argument('fields');
            $connection = $this->option('connection');

            // Generate Model
            $this->generateModel($modelName, $tableName, $fields, $connection);
            // Generate Migration
            $this->generateMigration($modelName, $tableName, $fields, $connection);
            // Generate Controller
            $this->generateController($modelName);
            // Generate Request
            $this->generateRequest($modelName);
            // Generate Resource Collection
            $this->generateCollection($modelName);
            $this->generateResource($modelName);
            $this->generateTranslationFile($modelName);

            $this->info("Structure for {$modelName} generated successfully!");
        } catch (\Exception $e) {
            $this->error("Error: " . $e);
        }
    }

    protected function generateModel($modelName, $tableName, $fields, $connection = 'default')
    {
        $fillableFields = [];
        $relationships = "";
        $fieldsArr = explode(' ', $fields);

        foreach ($fieldsArr as $fieldDefinition) {
            $fieldParts = explode(':', $fieldDefinition);

            if ($fieldParts[0] == 'foreign') {
                if (count($fieldParts) < 4) {
                    throw new \Exception("Invalid foreign key definition for '{$fieldDefinition}'");
                }

                $fieldName = $fieldParts[1];
                $relatedTable = $fieldParts[3];  // the table the foreign key references
                $relatedModel = Str::studly(Str::singular($relatedTable));

                $relationships .= <<<EOD
        public function $relatedModel()
        {
            return \$this->belongsTo($relatedModel::class, '$fieldName');
        }
    
        EOD;
            } else {
                if (!isset($fieldParts[1])) {
                    throw new \Exception("Invalid field definition for '{$fieldDefinition}'");
                }

                $fillableFields[] = "'$fieldParts[1]'";
            }
        }

        $fillableStr = implode(',', $fillableFields);
        $modelContent = <<<EOD
    <?php
    
    namespace App\Models;
    
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\SoftDeletes;
    
    class $modelName extends Model
    {
        use SoftDeletes;
    
        protected \$connection = '$connection';
        protected \$table = '$tableName';
        protected \$fillable = [$fillableStr];
    
    $relationships
    }
    
    EOD;

        $modelPath = app_path("Models/{$modelName}.php");
        if (file_exists($modelPath)) {
            $this->warn("Model file already exists: {$modelPath}. Skipping model generation.");
            return;  // Return early to skip this model's generation
        }
        file_put_contents($modelPath, $modelContent);
    }



    protected function generateMigration($modelName, $tableName, $fields, $connection)
    {
        $fieldsStr = "";
        $foreignKeysStr = "";
        $fieldsArr = explode(' ', $fields);

        foreach ($fieldsArr as $fieldDefinition) {
            $fieldParts = explode(':', $fieldDefinition);

            if (count($fieldParts) == 2) {  // For standard fields
                list($type, $field) = $fieldParts;
                $fieldsStr .= "\$table->$type('$field');\n        ";
            } elseif (count($fieldParts) == 4) {  // For foreign keys
                list($type, $field, $on, $relatedTable) = $fieldParts;

                $foreignKeysStr .= "\$table->unsignedBigInteger('$field');\n        ";
                $foreignKeysStr .= "\$table->foreign('$field')->references('id')->on('$relatedTable');\n        ";
            }
        }
        $fieldsStr .= $foreignKeysStr;

        $datePrefix = date('Y_m_d_His');
        $migrationName = "create_{$tableName}_table";
        $migrationFileName = str_replace('.', '_', "$datePrefix.$migrationName");

        // Determine the migration directory based on the connection
        $migrationDirectory = ($connection === 'tenant') ? 'database/migrations/tenant' : 'database/migrations/centrale';
        if (!file_exists($migrationDirectory)) {
            mkdir($migrationDirectory, 0755, true);
        }

        $migrationPath = "{$migrationDirectory}/{$migrationFileName}.php";

        $migrationContent = <<<EOD
    <?php
    use Illuminate\Database\Migrations\Migration;
    use Illuminate\Database\Schema\Blueprint;
    use Illuminate\Support\Facades\Schema;
    
    return new class extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up()
        {
            Schema::create('$tableName', function (Blueprint \$table) {
                \$table->id();
                $fieldsStr
                \$table->timestamps();
                \$table->softDeletes();
            });
        }
    
        /**
         * Reverse the migrations.
         */
        public function down()
        {
            Schema::dropIfExists('$tableName');
        }
    };
    EOD;

        file_put_contents($migrationPath, $migrationContent);
    }



    protected function generateController($modelName)
    {
        $controllerContent = $this->generateControllerTemplate($modelName);
        $controllerPath = app_path("Http/Controllers/{$modelName}Controller.php");

        if (!is_dir(dirname($controllerPath))) {
            mkdir(dirname($controllerPath), 0755, true);
        }

        file_put_contents($controllerPath, $controllerContent);
    }

    protected function generateControllerTemplate($modelName)
    {
        $pluralModelName = Str::plural(strtolower($modelName));
        $collectionName = "{$modelName}Collection";
        $resourceName = "{$modelName}Resource";
        $lowerModelName = strtolower($modelName);
    
        return <<<EOD
    <?php
    
    namespace App\Http\Controllers;
    
    use App\Http\Resources\\{$collectionName};
    use App\Http\Resources\\{$resourceName};
    use App\Models\\{$modelName};
    use App\Http\Requests\\{$modelName}Request;
    use Illuminate\Support\Facades\Log;
    use Illuminate\Http\Response;
    use DB;
    use Exception;
    
    class {$modelName}Controller extends Controller
    {
        public function index()
        {
            Log::info("====== Entering {$modelName}Controller::index Method ======");
            \${$pluralModelName} = {$modelName}::get();
    
            if (\${$pluralModelName}->isEmpty()) {
                Log::error("====== {$modelName}Controller::index => Empty response. ======");
                return response(null, Response::HTTP_NO_CONTENT);
            }
            Log::info("====== {$modelName}Controller::index Completed Successfully ======");
    
            return new {$collectionName}(\${$pluralModelName});
        }
    
        public function store({$modelName}Request \${$lowerModelName}Request)
        {
            try {
                Log::info("====== Entering {$modelName}Controller::store Method ======");
                DB::beginTransaction();
                \${$lowerModelName} = {$modelName}::create(\${$lowerModelName}Request->all());
                DB::commit();
                Log::info("====== {$modelName}Controller::store Completed Successfully ======");
    
                return response()->json([
                    'message' => trans("{$lowerModelName}Const.const.{$lowerModelName}Created"),
                    'object' => new {$resourceName}(\${$lowerModelName})
                ], Response::HTTP_CREATED);
            } catch (Exception \$e) {
                Log::error("====== {$modelName}Controller::store => Error: {\$e} ======");
                DB::rollBack();
                return response()->json([
                    'error' => trans("{$lowerModelName}Const.const.error_create_{$lowerModelName}")
                ], Response::HTTP_INTERNAL_SERVER_ERROR);
            }
        }
    
        public function show(\$id)
        {
            Log::info("====== Entering {$modelName}Controller::show Method ======");
            \${$lowerModelName} = {$modelName}::where('id', \$id)->first();
    
            if (!\${$lowerModelName}) {
                Log::error("====== {$modelName}Controller::show => Empty response. ======");
                return response()->json([
                    'error' => trans("{$lowerModelName}Const.const.{$lowerModelName}_not_found")
                ], Response::HTTP_NOT_FOUND);
            }
            Log::info("====== {$modelName}Controller::show Completed Successfully ======");
    
            return new {$resourceName}(\${$lowerModelName});
        }
    
        public function update({$modelName}Request \${$lowerModelName}Request, \$id)
        {
            try {
                Log::info("====== Entering {$modelName}Controller::update Method ======");
                DB::beginTransaction();
                \${$lowerModelName} = {$modelName}::find(\$id);
    
                if (!\${$lowerModelName}) {
                    Log::error("====== {$modelName}Controller::update => Empty response. ======");
                    return response()->json([
                        'error' => trans("{$lowerModelName}Const.const.{$lowerModelName}_not_found")
                    ], Response::HTTP_NOT_FOUND);
                }
    
                \$data = array_filter(\${$lowerModelName}Request->only(['name']));
                if (\${$lowerModelName}->update(\$data)) {
                    DB::commit();
                    Log::info("====== {$modelName}Controller::update Completed Successfully ======");
    
                    return response()->json([
                        'message' => trans("{$lowerModelName}Const.const.{$lowerModelName}Updated"),
                        'object' => new {$resourceName}(\${$lowerModelName})
                    ], Response::HTTP_ACCEPTED);
                }
            } catch (Exception \$e) {
                Log::error("====== {$modelName}Controller::update => Error: {\$e} ======");
                DB::rollBack();
                return response()->json([
                    'error' => trans("{$lowerModelName}Const.const.error_update_{$lowerModelName}")
                ], Response::HTTP_INTERNAL_SERVER_ERROR);
            }
        }
    
        public function destroy(\$id)
        {
            try {
                Log::info("====== Entering {$modelName}Controller::destroy Method ======");
                DB::beginTransaction();
                \${$lowerModelName} = {$modelName}::find(\$id);
    
                if (!\${$lowerModelName}) {
                    return response()->json([
                        'error' => trans("{$lowerModelName}Const.const.{$lowerModelName}_not_found")
                    ], Response::HTTP_NOT_FOUND);
                }
    
                if (\${$lowerModelName}->delete()) {
                    DB::commit();
                    Log::info("====== {$modelName}Controller::destroy Completed Successfully ======");
    
                    return response()->json([
                        'message' => trans("{$lowerModelName}Const.const.delete_{$lowerModelName}")
                    ], Response::HTTP_ACCEPTED);
                }
            } catch (Exception \$e) {
                Log::error("====== {$modelName}Controller::destroy => Error: {\$e} ======");
                DB::rollBack();
                return response()->json([
                    'error' => trans("{$lowerModelName}Const.const.error_delete_{$lowerModelName}")
                ], Response::HTTP_BAD_REQUEST);
            }
        }
    }
    EOD;
    }
    
    





    protected function generateRequest($modelName)
    {
        $requestContent = $this->generateRequestTemplate($modelName);
        $requestPath = app_path("Http/Requests/{$modelName}Request.php");

        file_put_contents($requestPath, $requestContent);
    }

    protected function generateRequestTemplate($modelName)
    {
        return <<<EOD
    <?php
    
    namespace App\Http\Requests;
    
    use Illuminate\Foundation\Http\FormRequest;
    use Illuminate\Contracts\Validation\Validator;
    use Illuminate\Http\Exceptions\HttpResponseException;
    use Illuminate\Support\Facades\Log;
    
    class {$modelName}Request extends FormRequest
    {
        public function authorize()
        {
            return true;
        }
    
        public function rules()
        {
            Log::info("====== {$modelName}Request => Payload Before Validation ======\\n" . json_encode(\$this->all(), JSON_PRETTY_PRINT));
            return [
                // Add validation rules here...
                // e.g., "name" => 'required|string',
            ];
        }
    
        /**
         * Get the custom validation error messages.
         *
         * @return array<string, string>
         */
        public function messages(): array
        {
            return [
                // Add custom messages here...
                // e.g., 'name.required' => trans('{$modelName}Const.const.required_name'),
            ];
        }
    
        /**
         * Handle a failed validation attempt.
         *
         * @param Validator \$validator
         * @throws HttpResponseException
         */
        protected function failedValidation(Validator \$validator): void
        {
            Log::error("====== {$modelName}Request => Validation Errors ======\\n" . json_encode(\$validator->errors()->toArray(), JSON_PRETTY_PRINT));
            throw new HttpResponseException(response()->json(\$validator->errors(), 400));
        }
    }
    EOD;
    }

    protected function generateResource($modelName)
    {
        $resourceName = "{$modelName}Resource";
        $resourcePath = app_path("Http/Resources/{$resourceName}.php");

        if (file_exists($resourcePath)) {
            $this->warn("Resource file already exists: {$resourcePath}. Skipping resource generation.");
            return;
        }

        $content = <<<EOD
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class {$resourceName} extends JsonResource
{
    public static \$wrap = null;

    public function toArray(Request \$request): array
    {
        return array_filter(
            [
                // Your model's attributes and relationships here, like:
                // trans("databaseConst.column.id") => \$this->id,
                // ... add more fields
            ],
        );
    }
}
EOD;

        file_put_contents($resourcePath, $content);
    }


    protected function generateCollection($modelName)
    {
        $collectionName = "{$modelName}Collection";
        $resourceName = "{$modelName}Resource";
        $collectionPath = app_path("Http/Resources/{$collectionName}.php");

        if (file_exists($collectionPath)) {
            $this->warn("Collection file already exists: {$collectionPath}. Skipping collection generation.");
            return;
        }

        $content = <<<EOD
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class {$collectionName} extends ResourceCollection
{
    public \$collects = {$resourceName}::class;


    /**
     * The wrap value to be used to wrap the paginated response.
     *
     * @var null
     */


    public static \$wrap = null;

    public function toArray(Request \$request): array
    {
        return parent::toArray(\$request);
    }
}
EOD;

        file_put_contents($collectionPath, $content);
    }

    protected function generateTranslationFile($modelName)
    {
        $lowerModelName = strtolower($modelName);
        $translationPath = resource_path("lang/fr/{$lowerModelName}Const.php");

        if (file_exists($translationPath)) {
            $this->warn("Translation file already exists: {$translationPath}. Skipping translation generation.");
            return;
        }

        $translationContent = <<<EOD
<?php
return [
    "const" => [
        '{$lowerModelName}Created' => "Le {$lowerModelName} a été créé avec succès.",
        'error_create_{$lowerModelName}' => "Erreur lors de la création du {$lowerModelName}.",
        '{$lowerModelName}_not_found' => "{$modelName} introuvable.",
        '{$lowerModelName}Updated' => "Le {$lowerModelName} a été mis à jour avec succès.",
        'error_update_{$lowerModelName}' => "Erreur lors de la mise à jour du {$lowerModelName}.",
        'delete_{$lowerModelName}' => "{$modelName} supprimé.",
        'error_delete_{$lowerModelName}' => "Erreur lors de la suppression du {$lowerModelName}.",
        'required_name' => "Le nom est requis.",
    ],
];
EOD;

        file_put_contents($translationPath, $translationContent);
    }
}
