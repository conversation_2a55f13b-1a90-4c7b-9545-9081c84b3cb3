<?php

namespace App\Http\Resources\MindBridge;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\MindBridge\Test;
class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'enabled' => ($this->id === 3 && Test::where('category_id', $this->id)->exists()) ? false : (bool) $this->is_active,
            'children' => CategoryResource::collection($this->whenLoaded('subcategories')),
            'code' => $this->code,
        ];
        if (!is_null($this->parent_id)) {
            $data['description'] = $this->description;
            $data['button_text'] = $this->button_text;
            $data['gradient_background'] = $this->gradient_background;
            $data['gradient_border'] = $this->gradient_border;
            $data['icon'] = asset('storage' . $this->icon);
            $data['image_url'] = asset('storage' . $this->image_url);
            $data['action_type'] = $this->action_type;
            $data['is_mobile'] = $this->is_mobile;
            $data['is_bo'] = $this->is_bo;
            $data['position'] = $this->position;
            $data['count'] = Test::where('category_id', $this->id)->count() ?: 0;
        }
        return $data;
    }
}
