import {
  Component,
  inject,
  input,
  InputSignal,
  OnInit,
  signal,
  WritableSignal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { SanteMentaleService } from '../../sante-mentale.service';
import { StudentTestHistory, StudentTestHistoryResponse } from '../../models/student-test-history';
import { SetLoading } from '../../../shared/store/shared.actions';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../core/app.state';

@Component({
  selector: 'app-student-test-history',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './student-test-history.component.html',
  styleUrl: './student-test-history.component.scss',
})
export class StudentTestHistoryComponent implements OnInit {
  etudiantId: InputSignal<number> = input.required<number>();

  #santeMentaleService = inject(SanteMentaleService);
  #store = inject<Store<AppState>>(Store);

  testHistory: WritableSignal<StudentTestHistory[]> = signal([]);
  selectedTest: WritableSignal<StudentTestHistory | null> = signal(null);
  activeTab: WritableSignal<'details' | 'scoring' | 'observations'> = signal('details');
  isLoading: WritableSignal<boolean> = signal(false);

  ngOnInit() {
    this.#store.dispatch(SetLoading({ isAppLoading: true }));
    this.loadTestHistory();
  }

  loadTestHistory() {
    this.isLoading.set(true);
    this.#santeMentaleService.getStudentTestHistory(this.etudiantId()).subscribe({
      next: (response: StudentTestHistoryResponse) => {
        this.testHistory.set(response.data);
        if (response.data.length > 0) {
          this.selectedTest.set(response.data[0]);
        }
        this.isLoading.set(false);
        this.#store.dispatch(SetLoading({ isAppLoading: false }));
      },
      error: (error) => {
        console.error('Error loading test history:', error);
        this.isLoading.set(false);
        this.#store.dispatch(SetLoading({ isAppLoading: false }));
      },
    });
  }

  selectTest(test: StudentTestHistory) {
    this.selectedTest.set(test);
    this.activeTab.set('details');
  }

  setActiveTab(tab: 'details' | 'scoring' | 'observations') {
    this.activeTab.set(tab);
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'TERMINE':
        return '#10B981';
      case 'EN COURS':
        return '#F59E0B';
      case 'ABANDON':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'TERMINE':
        return 'Complété';
      case 'EN COURS':
        return 'En cours';
      case 'ABANDON':
        return 'Abandonné';
      default:
        return status;
    }
  }

  formatDate(date: string): string {
    if (!date) return '-';
    return new Date(date).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }
}

