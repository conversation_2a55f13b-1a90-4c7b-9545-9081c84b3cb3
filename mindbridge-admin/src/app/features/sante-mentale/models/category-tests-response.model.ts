export type CategoryTestsResponse = {
  message: string;
  data: CategoryTest[];
};

export type CategoryTest = {
  id: number;
  title: string;
  description: string;
  target: number;
  type: string;
  matiere_id: number;
  niveau_id: number;
  created_by: number;
  category_id: number;
  created_at: string;
  updated_at: string;
  steps: Step[];
};

export type Step = {
  id: number;
  test_id: number;
  question_id: number;
  required: number;
  type: string;
  condition: number;
  order: number;
  created_at: string;
  updated_at: string;
  question: Question;
};

export type Question = {
  id: number;
  type: string;
  content: string;
  description: string;
  question_type: string;
  category_id: number;
  created_at: string;
  updated_at: string;
  is_required: number;
  image_path: string;
  options: Option[];
};

export type Option = {
  id: number;
  name: string;
  icon: string;
  isCorrect: number;
  question_id: number;
  created_at: string;
  updated_at: string;
};
