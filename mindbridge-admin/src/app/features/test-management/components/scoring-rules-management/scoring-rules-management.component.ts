import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { TestScoringRuleService } from '../../services/test-scoring-rule.service';
import { TestManagementService } from '../../test-management.service';
import { TestScoringRule, TestScoringRuleRequest } from '../../models/test-scoring-rule';
import { TestResponse } from '../../models/test-response';
import { CommonService } from '../../../shared/common.service';
import { ITestSearchForm, TestSearchForm } from '../../models/test-search-request';

@Component({
  selector: 'app-scoring-rules-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    MatPaginatorModule,
  ],
  templateUrl: './scoring-rules-management.component.html',
  styleUrls: ['./scoring-rules-management.component.scss'],
})
export class ScoringRulesManagementComponent implements OnInit {
  #fb = inject(FormBuilder);
  #scoringRuleService = inject(TestScoringRuleService);
  #testManagementService = inject(TestManagementService);
  #commonService = inject(CommonService);

  // Data
  tests: TestResponse[] = [];
  scoringRules: TestScoringRule[] = [];
  selectedTest: TestResponse | null = null;

  // UI State
  isLoadingTests = false;
  isLoadingRules = false;
  showForm = false;
  editingRuleId: number | null = null;

  // Pagination
  pageSize = 1000;
  currentPage = 1;
  totalTests = 0;

  // Form
  testFilterForm: FormGroup;
  scoringRuleForm: FormGroup;

  // Table columns
  displayedColumns: string[] = ['min_percentage', 'max_percentage', 'interpretation', 'feedback', 'recommendation', 'actions'];

  ngOnInit(): void {
    this.initializeForms();
    this.loadTests();
  }

  initializeForms(): void {
    this.testFilterForm = this.#fb.group({
      test_id: [null, Validators.required],
    });

    this.scoringRuleForm = this.#fb.group({
      min_percentage: [null, [Validators.required, Validators.min(0), Validators.max(100)]],
      max_percentage: [null, [Validators.required, Validators.min(0), Validators.max(100)]],
      interpretation: ['', [Validators.required, Validators.maxLength(255)]],
      feedback: [''],
      recommendation: [''],
    });
  }

  loadTests(page: number = 1): void {
    this.isLoadingTests = true;
    const searchForm: ITestSearchForm = new TestSearchForm();
    this.#testManagementService.getListeTests(searchForm, { current_page: page, per_page: this.pageSize }).subscribe({
      next: (response: any) => {
        this.tests = response.data;
        this.totalTests = response.total_items;
        this.currentPage = page;
        this.isLoadingTests = false;
      },
      error: (error) => {
        console.error('Error loading tests:', error);
        this.#commonService.showToast('Erreur lors du chargement des tests', 'ERROR');
        this.isLoadingTests = false;
      },
    });
  }

  onTestSelected(): void {
    const testId = this.testFilterForm.get('test_id')?.value;
    if (testId) {
      this.selectedTest = this.tests.find((t) => t.id === testId) || null;
      this.loadScoringRules(testId);
    }
  }

  loadScoringRules(testId: number): void {
    this.isLoadingRules = true;
    this.#scoringRuleService.getScoringRules(testId).subscribe({
      next: (response) => {
        this.scoringRules = response.data;
        this.isLoadingRules = false;
      },
      error: (error) => {
        console.error('Error loading scoring rules:', error);
        this.#commonService.showToast('Erreur lors du chargement des règles', 'ERROR');
        this.isLoadingRules = false;
      },
    });
  }

  openForm(rule?: TestScoringRule): void {
    this.showForm = true;
    if (rule) {
      this.editingRuleId = rule.id;
      this.scoringRuleForm.patchValue({
        min_percentage: rule.min_percentage,
        max_percentage: rule.max_percentage,
        interpretation: rule.interpretation,
        feedback: rule.feedback,
        recommendation: rule.recommendation,
      });
    } else {
      this.editingRuleId = null;
      this.scoringRuleForm.reset();
    }
  }

  closeForm(): void {
    this.showForm = false;
    this.editingRuleId = null;
    this.scoringRuleForm.reset();
  }

  saveRule(): void {
    if (this.scoringRuleForm.invalid || !this.selectedTest) {
      this.#commonService.showToast('Veuillez remplir tous les champs requis', 'ERROR');
      return;
    }

    const formValue = this.scoringRuleForm.getRawValue();
    const request: TestScoringRuleRequest = {
      min_percentage: formValue.min_percentage,
      max_percentage: formValue.max_percentage,
      interpretation: formValue.interpretation,
      feedback: formValue.feedback,
      recommendation: formValue.recommendation,
    };

    if (this.editingRuleId) {
      this.#scoringRuleService.updateScoringRule(this.selectedTest.id, this.editingRuleId, request).subscribe({
        next: () => {
          this.#commonService.showToast('Règle mise à jour avec succès', 'SUCCESS');
          this.closeForm();
          this.loadScoringRules(this.selectedTest!.id);
        },
        error: (error) => {
          console.error('Error updating rule:', error);
          this.#commonService.showToast(error.error?.error || 'Erreur lors de la mise à jour', 'ERROR');
        },
      });
    } else {
      this.#scoringRuleService.createScoringRule(this.selectedTest.id, request).subscribe({
        next: () => {
          this.#commonService.showToast('Règle créée avec succès', 'SUCCESS');
          this.closeForm();
          this.loadScoringRules(this.selectedTest!.id);
        },
        error: (error) => {
          console.error('Error creating rule:', error);
          this.#commonService.showToast(error.error?.error || 'Erreur lors de la création', 'ERROR');
        },
      });
    }
  }

  deleteRule(ruleId: number): void {
    if (!this.selectedTest) return;
    if (confirm('Êtes-vous sûr de vouloir supprimer cette règle ?')) {
      this.#scoringRuleService.deleteScoringRule(this.selectedTest.id, ruleId).subscribe({
        next: () => {
          this.#commonService.showToast('Règle supprimée avec succès', 'SUCCESS');
          this.loadScoringRules(this.selectedTest!.id);
        },
        error: (error) => {
          console.error('Error deleting rule:', error);
          this.#commonService.showToast('Erreur lors de la suppression', 'ERROR');
        },
      });
    }
  }

  onPageChange(event: PageEvent): void {
    this.loadTests(event.pageIndex + 1);
  }
}

