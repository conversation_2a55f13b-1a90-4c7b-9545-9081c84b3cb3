<?php

namespace Database\Seeders\mindBridge;

use App\Models\MindBridge\MindBridgeEtudiant;
use App\Models\School;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class   MindBridgeUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            [
                'first_name' => 'admin',
                'email' => '<EMAIL>',
                'phone' => '27088197',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'admin',
            ],
            [
                'first_name' => 'Hiba',
                'last_name' => 'Mebrouc',
                'email' => '<EMAIL>',
                'phone' => '00212665465674',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '3',
            ],
            [
                'first_name' => '<PERSON>',
                'last_name' => 'Ben<PERSON>lloun',
                'email' => '<EMAIL>',
                'phone' => '0654237890',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '4',
            ],
            [
                'first_name' => 'Fatima',
                'last_name' => 'El Idrissi',
                'email' => '<EMAIL>',
                'phone' => '0661549873',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '5',
            ],
            [
                'first_name' => 'Youssef',
                'last_name' => 'Amrani',
                'email' => '<EMAIL>',
                'phone' => '0678452367',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '6',
            ],
            [
                'first_name' => 'Sara',
                'last_name' => 'Bourkadi',
                'email' => '<EMAIL>',
                'phone' => '0687541230',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '7',
            ],
            [
                'first_name' => 'Hassan',
                'last_name' => 'Taoufik',
                'email' => '<EMAIL>',
                'phone' => '0669874523',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '8',
            ],
            [
                'first_name' => 'Meryem',
                'last_name' => 'El Khalfi',
                'email' => '<EMAIL>',
                'phone' => '0674598231',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '9',
            ],
            [
                'first_name' => 'Omar',
                'last_name' => 'Belhaj',
                'email' => '<EMAIL>',
                'phone' => '0657849213',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '10',
            ],
            [
                'first_name' => 'Nada',
                'last_name' => 'Ait Ali',
                'email' => '<EMAIL>',
                'phone' => '0642589631',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '11',
            ],
            [
                'first_name' => 'Zakaria',
                'last_name' => 'Bouziane',
                'email' => '<EMAIL>',
                'phone' => '0621459873',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '12',
            ],
            [
                'first_name' => 'Imane',
                'last_name' => 'Rachidi',
                'email' => '<EMAIL>',
                'phone' => '0664789152',
                'avatar' => '/users/profiles/dev-user-avatar.png',
                'password' => Hash::make('password'),
                "is_mind_bridge_user" => true,
                'type' => 'etudiant',
                'school_id' => '12',
            ],
        ];

        foreach ($users as $userData) {
            $school = School::where('database', 'mind_bridge')->first();
            if (!$school) {
                throw new Exception("School not found for the current database.");
            }

            $user = User::updateOrCreate(
                ['email' => $userData['email']],
                [
                    'name' => $userData['first_name'],
                    'email' => $userData['email'],
                    'phone' => $userData['phone'],
                    'avatar' => $userData['avatar'],
                    'password' => $userData['password'],
                    "is_mind_bridge_user" => $userData['is_mind_bridge_user'],
                    'type' => $userData['type'],
                ],
            
            );

            if ($user->type == 'etudiant') {
                MindBridgeEtudiant::create([
                    'user_id' => $user->id,
                    'niveau_id' => 1,
                    "school_id" => $userData['school_id'],
                    "avatar" => $userData['avatar'],
                    "test_profiling_completed" => false,
                    "email" => $userData['email'],
                    "first_name" => $userData['first_name'],
                    "last_name" => $userData['last_name'],

                ]);
            }
            \DB::connection('centrale')->table('user_school')->insert([
                'user_id' => $user->id,
                'school_id' => $school->id,
            ]);            
        }
    }
}
