import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../data/providers/remote/dio_client.dart';

class LessonController extends GetxController {
  final DioClient _dioClient;

  LessonController({required DioClient dioClient}) : _dioClient = dioClient;

  RxString localPath = ''.obs;
  RxInt currentPage = 0.obs;
  RxInt totalPages = 0.obs;
  RxBool isLoading = false.obs;
  RxBool isError = false.obs;

  Future<void> loadPDF(String pdfUrl) async {
    isLoading.value = true;
    isError.value = false;

    try {
      final dir = await getApplicationDocumentsDirectory();
      final fileName = pdfUrl.split('/').last;
      final file = File("${dir.path}/$fileName");

      if (!file.existsSync()) {
        final response = await _dioClient.dio.get<List<int>>(
          pdfUrl,
          options: Options(responseType: ResponseType.bytes),
        );

        if (response.statusCode == 200) {
          await file.writeAsBytes(response.data!);
          localPath.value = file.path;
        } else {
          throw Exception("Failed to download PDF");
        }
      } else {
        localPath.value = file.path;
      }
    } catch (e) {
      debugPrint("Error loading PDF: $e");
      isError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  void updateCurrentPage(int page) {
    currentPage.value = page;
  }

  void updateTotalPages(int pages) {
    totalPages.value = pages;
  }
}
