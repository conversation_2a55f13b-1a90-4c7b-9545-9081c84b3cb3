export interface QuestionResponse {
  id: number;
  type: string;
  content: string;
  answer: boolean;
  description: string;
  options: Option[];
  categories: Category[];
  matieres: Matiere[];
  niveaux: Niveaux[];
  is_required: boolean;
  is_true?: boolean;
  is_false?: boolean;
  image_path?: string;
}

interface Option {
  id: number;
  name: string;
  icon: string;
  isCorrect: boolean;
  question_id: number;
  created_at: Date;
  updated_at: Date;
}

export interface Category {
  id: number;
  parent_id: number;
  name: string;
  description: string;
  button_text: string;
  gradient_background: string;
  gradient_border: string;
  icon: string;
  image_url: any;
  action_type: string;
  is_mobile: boolean;
  is_bo: boolean;
  is_active: boolean;
  code: string;
  position: number;
  count: number;
  created_at: Date;
  updated_at: Date;
}

export interface Matiere {
  id: number;
  name_fr: string;
  name_ar: string;
  title_color: string;
  description: string;
  image_url: string;
  gradient_background: string;
  gradient_border: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
}

export interface Niveaux {
  id: number;
  name: string;
  color: string;
  background: string;
  description: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
}
