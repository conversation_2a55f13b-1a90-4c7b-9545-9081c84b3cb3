#!/bin/bash

# MindBridge Setup Script
# This script sets up the central database and synchronizes MindBridge data

echo "🚀 MindBridge Complete Setup Script"
echo "==================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    print_error "Please run this script from the mindbridge-backend directory"
    exit 1
fi

# Step 1: Check database connections
print_status "Step 1: Checking database connections..."
php artisan tinker --execute="
try {
    DB::connection('centrale')->getPdo();
    echo 'Central database: Connected\n';
} catch (Exception \$e) {
    echo 'Central database: Failed - ' . \$e->getMessage() . '\n';
    exit(1);
}

try {
    DB::connection('mind_bridge')->getPdo();
    echo 'MindBridge database: Connected\n';
} catch (Exception \$e) {
    echo 'MindBridge database: Failed - ' . \$e->getMessage() . '\n';
    exit(1);
}
"

if [ $? -ne 0 ]; then
    print_error "Database connection failed. Please check your .env configuration."
    exit 1
fi

print_success "Database connections verified"
echo ""

# Step 2: Setup central database
print_status "Step 2: Setting up central database..."
read -p "Do you want to setup the central database? (y/n): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Setting up central database tables and initial data..."
    php artisan mindbridge:setup-central-db --force
    
    if [ $? -eq 0 ]; then
        print_success "Central database setup completed"
    else
        print_error "Central database setup failed"
        exit 1
    fi
else
    print_warning "Skipping central database setup"
fi

echo ""

# Step 3: Dry run synchronization
print_status "Step 3: Analyzing MindBridge data for synchronization..."
php artisan mindbridge:sync-data --dry-run

echo ""

# Step 4: Run synchronization
print_status "Step 4: Data synchronization"
read -p "Do you want to proceed with data synchronization? (y/n): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Running data synchronization..."
    php artisan mindbridge:sync-data --force
    
    if [ $? -eq 0 ]; then
        print_success "Data synchronization completed successfully!"
    else
        print_error "Data synchronization failed"
        exit 1
    fi
else
    print_warning "Skipping data synchronization"
    echo ""
    print_status "You can run synchronization later with:"
    echo "php artisan mindbridge:sync-data"
fi

echo ""

# Step 5: Verification
print_status "Step 5: Verification"
print_status "Running basic verification checks..."

php artisan tinker --execute="
echo 'Central Database Records:\n';
echo '- Schools: ' . DB::connection('centrale')->table('schools')->count() . '\n';
echo '- Users: ' . DB::connection('centrale')->table('users')->count() . '\n';
echo '- Niveaux: ' . DB::connection('centrale')->table('niveaux')->count() . '\n';
echo '- Matieres: ' . DB::connection('centrale')->table('matieres')->count() . '\n';
echo '\n';

echo 'MindBridge Database Records:\n';
echo '- Students: ' . DB::connection('mind_bridge')->table('etudiants')->count() . '\n';
echo '- Tests: ' . DB::connection('mind_bridge')->table('tests')->count() . '\n';
echo '- Questions: ' . DB::connection('mind_bridge')->table('questions')->count() . '\n';
echo '- Contents: ' . DB::connection('mind_bridge')->table('contents')->count() . '\n';
"

echo ""
print_success "🎉 MindBridge setup completed!"
echo ""
print_status "Next steps:"
echo "1. Test your MindBridge application"
echo "2. Verify that students can login"
echo "3. Check that tests and content are accessible"
echo "4. Monitor logs for any issues"
echo ""
print_status "Useful commands:"
echo "- Check logs: tail -f storage/logs/laravel.log"
echo "- Test database: php artisan tinker"
echo "- Re-sync data: php artisan mindbridge:sync-data"
echo ""
