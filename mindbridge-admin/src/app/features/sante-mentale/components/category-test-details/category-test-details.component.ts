import { Component, Input } from '@angular/core';
import { CategoryTest } from '../../models/category-tests-response.model';
import { MatIcon } from '@angular/material/icon';

@Component({
  selector: 'app-category-test-details',
  imports: [MatIcon],
  templateUrl: './category-test-details.component.html',
  styleUrl: './category-test-details.component.scss',
})
export class CategoryTestDetailsComponent {
  @Input({ required: true }) test!: CategoryTest;

  getIconTransformed(icon: string): string {
    const hexCode = icon.replace('U+', '');
    return String.fromCodePoint(parseInt(hexCode, 16));
  }
}
