<?php

namespace App\Http\Controllers;

use App\Models\Etudiant;
use App\Models\EtudiantParrent;
use App\Models\Role;
use App\Models\School;
use Exception;
use App\Models\User;
use Illuminate\Http\Response;
use App\Http\Requests\UserRequest;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\UserResource;
use App\Http\Resources\UserResourceCollection;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use DB;

class UserController extends Controller
{

    public function index()
    {
        Log::info("====== Entering UserController::all Method ======");
        $originalDatabase = config('database.connections.tenant.database');
        $currentSchool = School::where("database", $originalDatabase)->first();

        if (!$currentSchool) {
            Log::error("====== UserController::all => School not found for the current database. ======");
            return response()->json(['error' => 'School not found.'], Response::HTTP_NOT_FOUND);
        }

        $users = User::where('type', '!=', 'parrent')->where('type', '!=', 'enseignant')
            ->whereHas('schools', function ($query) use ($currentSchool) {
                $query->where('school_id', $currentSchool->id);
            })
            ->paginate(10);
        if ($users->isEmpty()) {
            Log::error("====== UserController::all => Empty responce. ======");
            return response(null, Response::HTTP_NO_CONTENT);
        }
        Log::info("====== UserController::all Completed Successfully ======");

        return new UserResourceCollection($users);
    }



    public function update(UserRequest $userRequest, $id)
    {
        \Log::info("update");
        \Log::info($userRequest);
        try {
            DB::beginTransaction();
            $user = User::find($id);
            if (!$user) {
                return response()->json([
                    'error' => trans("globalConst.const.users_not_found")
                ], Response::HTTP_NOT_FOUND);
            }
            \Log::info($user);

            $data = array_filter($userRequest->only('name', 'email', 'avatar', 'active'), function ($value) {
                return $value !== null;
            });

            $oldAvatarPath = $user->avatar;

            if ($userRequest->hasFile('avatar') && $userRequest->file('avatar')->isValid()) {
                $extension = $userRequest->avatar->getClientOriginalExtension();
                $uid = (string) Str::uuid();
                $filename = time() . $uid . '.' . $extension;
                $relativePath = 'users/profiles/' . $filename;
            
                
                if (isset($data['avatar']) && Storage::disk('public')->exists($oldAvatarPath) && $oldAvatarPath !== 'users/profiles/dev-user-avatar.png') {
                    // Storage::disk('public')->delete($oldAvatarPath);
                }
                $userRequest->file('avatar')->storeAs('users/profiles', $filename, 'public');
            
                
                $data['avatar'] = '/storage/' . $relativePath; 
            }
            

            if ($userRequest->has('role_id')) {
                $user->roles()->detach();
                $user->roles()->attach($userRequest->role_id);
            }

            if ($userRequest->has("password")) {
                $newPassword = Hash::make($userRequest->password);
                $data['password'] = $newPassword;
            }

            if ($user->update($data)) {
                DB::commit();
                $user->load("roles.permissions");
                return response()->json([
                    'message' => trans("globalConst.const.UserUpdated"),
                    'object' => new UserResource($user)
                ], Response::HTTP_ACCEPTED);
            }
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            return response()->json([
                'error' => trans("globalConst.const.error_update_user")
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            $user = User::find($id);
            if (!$user) {
                return response()->json([
                    'error' => trans("globalConst.const.users_not_found")
                ], Response::HTTP_NOT_FOUND);
            }
            if (file_exists(public_path($user->avatar)) && $user->avatar !== "users/profiles/dev-user-avatar.png") {
                unlink(public_path($user->avatar));
            }

            if ($user->delete()) {
                DB::commit();

                return response()->json([
                    'message' => trans("globalConst.const.delete_user")
                ], Response::HTTP_ACCEPTED);
            }
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            return response()->json([
                'error' => trans("globalConst.const.error_delete_user")
            ], Response::HTTP_BAD_REQUEST);
        }
    }


    public function store(UserRequest $userRequest)
    {
        try {
            DB::connection('centrale')->beginTransaction();
            $database = config('database.connections.tenant.database');

            $school = School::where('database', $database)->first();

            if (!$school) {
                throw new Exception("School not found for the current database.");
            }
            $data = $userRequest->all();

            if ($userRequest->hasFile('avatar') && $userRequest->file('avatar')->isValid()) {
                $extension = $userRequest->avatar->getClientOriginalExtension();
                $uid = (string) Str::uuid();
                $filename = time() . $uid . '.' . $extension;
                $relativePath = 'users/profiles/' . $filename;

                $userRequest->avatar->move(public_path('users/profiles'), $filename);
                $data['avatar'] = '/' . $relativePath;
            } else {
                $data['avatar'] = 'users/profiles/dev-user-avatar.png';
            }
            if ($userRequest->has("password")) {
                $newPassword = Hash::make($userRequest->password);
                $data['password'] = $newPassword;
            }

            $data['type'] = 'admin';
            $user = User::create($data);

            if (!$user->schools()->where('school_id', $school->id)->exists()) {
                $user->schools()->attach($school->id);
            }
            if ($userRequest->role_id) {
                $role = Role::on('centrale')->find($userRequest->role_id);


                if (!$role || !$user->roles()->where('roles.id', $role->id)->exists()) {
                    $user->roles()->sync([$role->id]);
                }

                if (!$user->hasRole($role->name)) {
                    Log::info("Problem lors de l'ajout du role': " . $userRequest->role_id);
                    DB::connection('centrale')->rollBack();
                    return response(['data' => null, 'message' => 'Le role n\'existe pas'], 400);
                }

            }
            DB::connection('centrale')->commit();
            // SendPasswordCreation::dispatch($user, $school->domain);
            $user->load("roles.permissions");

            return response()->json([
                trans("globalConst.const.message") => trans("globalConst.auth.user_create_success"),
                'object' => new UserResource($user),
            ], Response::HTTP_CREATED);
        } catch (Exception $e) {
            DB::connection('centrale')->rollBack();
            Log::error($e);
            return response()->json([
                'error' => trans("globalConst.const.error_create_user")
            ], Response::HTTP_BAD_REQUEST);
        }
    }
}
