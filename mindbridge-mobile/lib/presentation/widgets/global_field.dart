import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../app/theme/colors.dart';

class GlobalTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final bool obscureText;

  const GlobalTextField({
    super.key,
    required this.controller,
    required this.hintText,
    this.obscureText = false,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      obscureText: obscureText,
      decoration: InputDecoration(
        hintText: hintText,
        filled: true,
        fillColor: AppColors.white,
        contentPadding: EdgeInsets.symmetric(vertical: 1.5.h, horizontal: 3.w),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(1.5.h),
          borderSide:
              BorderSide(color: AppColors.fieldBorderColor, width: 0.2.h),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(1.5.h),
          borderSide: BorderSide(color: AppColors.primary, width: 0.3.h),
        ),
      ),
    );
  }
}
