<!DOCTYPE html>
<html>

<head>
    <style>
        .calendar {
            display: grid;
            grid-template-columns: 100px repeat(7, 1fr);
            text-align: center;
        }

        .day-header,
        .time-label,
        .time-slot {
            border: 1px solid #ddd;
            padding: 5px;
        }

        .day-header {
            background-color: #f3f3f3;
        }

        .time-slot {
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .booked {
            background-color: #e5b5b5;
        }

        .free {
            background-color: #dff0d8;
        }
    </style>
</head>

<body>

    <div id="currentWeekDisplay">Current Week: </div>
    <div id="navigatedWeekDisplay">Navigated Week: </div>

    <div id="calendar"></div>

    <button id="prevWeek">Previous Week</button>
    <button id="nextWeek">Next Week</button>

    <script>


        // var currentDate = new Date();

        var currentDate = new Date('2023-10-16');

        async function loadCalendarData() {
            try {
                const payload = {
                    "profId": 1,
                    "startDate": "2023-10-10",
                    "endDate": "2023-12-25"
                };

                const calendarData = await callServer('http://localhost:7562/api/calendar/professeur?tenant=tahssil_maarif.ndb.awlyg.local', 'POST', payload);

                populateCalendar(currentDate, calendarData);
            } catch (error) {
                console.error('Failed to load calendar data:', error);
            }
        }




        function formatDate(date) {
            return date.toISOString().split('T')[0];
        }

        function getWeekRange(date) {
            let start = new Date(date);
            start.setDate(start.getDate() - start.getDay() + (start.getDay() === 0 ? -6 : 1));
            let end = new Date(start);
            end.setDate(end.getDate() + 6);
            return `${formatDate(start)} to ${formatDate(end)}`;
        }

        function populateCalendar(weekStartDate, data) {

            var calendar = document.getElementById('calendar');
            calendar.innerHTML = '';

            document.getElementById('currentWeekDisplay').textContent = 'Current Week: ' + getWeekRange(new Date());
            document.getElementById('navigatedWeekDisplay').textContent = 'Navigated Week: ' + getWeekRange(weekStartDate);

            var daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

            var headerRow = document.createElement('div');
            headerRow.style.display = 'grid';
            headerRow.style.gridTemplateColumns = '100px repeat(7, 1fr)';

            var emptyHeader = document.createElement('div');
            headerRow.appendChild(emptyHeader);

            for (let i = 0; i < 7; i++) {
                let dayDate = new Date(weekStartDate);
                dayDate.setDate(dayDate.getDate() + i);

                var dayHeader = document.createElement('div');
                dayHeader.className = 'day-header';
                dayHeader.textContent = daysOfWeek[dayDate.getDay()] + ' (' + formatDate(dayDate) + ')';
                headerRow.appendChild(dayHeader);
            }

            calendar.appendChild(headerRow);

            var timeSlots = ['8-10', '10-12', '12-14'];
            timeSlots.forEach(timeSlot => {
                var timeSlotRow = document.createElement('div');
                timeSlotRow.style.display = 'grid';
                timeSlotRow.style.gridTemplateColumns = '100px repeat(7, 1fr)';

                var timeLabel = document.createElement('div');
                timeLabel.className = 'time-label';
                timeLabel.textContent = timeSlot;
                timeSlotRow.appendChild(timeLabel);

                for (let i = 0; i < 7; i++) {
                    var slotCell = document.createElement('div');
                    slotCell.className = 'time-slot';

                    let dayDate = new Date(weekStartDate);
                    dayDate.setDate(dayDate.getDate() + i);
                    let formattedDate = formatDate(dayDate);

                    let slotData = data.find(entry => {
                        let entryDate = entry.date;
                        return entryDate === formattedDate && isInTimeSlot(entry.heure_debut, timeSlot);
                    });

                    if (slotData) {
                        slotCell.className += ' booked';
                        slotCell.textContent = `${slotData.groupe}`;
                    } else {
                        slotCell.className += ' free';
                        slotCell.textContent = 'Free';
                    }
                    timeSlotRow.appendChild(slotCell);
                }
                calendar.appendChild(timeSlotRow);
            });

        }

        function adjustDateToMonday(date) {
            var day = date.getDay();
            var difference = day === 0 ? -6 : 1 - day;
            date.setDate(date.getDate() + difference);
            return new Date(date);
        }

        document.getElementById('prevWeek').addEventListener('click', async function () {
            currentDate = adjustDateToMonday(new Date(currentDate.setDate(currentDate.getDate() - 7)));
            await loadCalendarData();
        });

        document.getElementById('nextWeek').addEventListener('click', async function () {
            currentDate = adjustDateToMonday(new Date(currentDate.setDate(currentDate.getDate() + 7)));
            await loadCalendarData();
        });
        function isInTimeSlot(heure_debut, timeSlot) {
            let [slotStart, slotEnd] = timeSlot.split('-').map(Number);
            let heure = parseInt(heure_debut.split(':')[0], 10);

            return heure >= slotStart && heure < slotEnd;
        }


        async function callServer(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        "Content-Type": "application/json",
                    },
                };

                if (method === 'POST' && data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(url, options);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('Error during fetch operation:', error);
                throw error;
            }
        }

        loadCalendarData();

    </script>

</body>

</html>
