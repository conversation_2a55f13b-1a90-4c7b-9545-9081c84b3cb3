<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تأكيد إيصال الدفع</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f0f4f7;
            color: #333;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            direction: rtl; /* To display text from right to left */
        }

        .container {
            max-width: 700px;
            margin: 40px auto;
            padding: 30px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        h1 {
            color: #2c7be5;
            text-align: center;
            margin-bottom: 25px;
            font-size: 28px;
        }

        p {
            text-align: center;
            font-size: 20px;
            margin-bottom: 25px;
            color: #555;
        }

        ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        ul li {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 12px 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        ul li:last-child {
            margin-bottom: 0;
        }

        ul li strong {
            color: #444;
            font-size: 18px;
        }

        ul li span {
            color: #555;
            font-size: 16px;
        }

        .months-list {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .months-list li {
            margin-bottom: 5px;
            color: #444;
            font-size: 16px;
            background: #eaf3fc;
            padding: 10px;
            border-radius: 6px;
            display: inline-block;
            margin-right: 5px;
        }

        .qr-code-container {
            text-align: center;
            margin-top: 30px;
        }

        .qr-code-container img {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background-color: #fff;
        }

        @media (max-width: 768px) {
            .container {
                width: 90%;
                padding: 20px;
            }

            h1 {
                font-size: 24px;
            }

            ul li {
                padding: 10px 15px;
            }

            ul li strong {
                font-size: 16px;
            }

            ul li span {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تأكيد إيصال الدفع</h1>
        <p>الإيصال أصلي وصادر عن {{ $tenant->name }}.</p>
        <ul>
            <li><strong>رقم الإيصال:</strong> <span>{{ $receipt->recuNumber }}</span></li>
            <li><strong>تاريخ الدفع:</strong> <span>{{ $receipt->created_at->format('d/m/Y H:i') }}</span></li>
            <li><strong>اسم الطالب:</strong> <span>{{ $receipt->studentName }}</span></li>
            <li><strong>الفصل:</strong> <span>{{ $receipt->classe }}</span></li>
            <li><strong>المبلغ المدفوع:</strong> <span>{{ $receipt->amount }} د.م</span></li>
            <li><strong>الباقي:</strong> <span>{{ $receipt->reliquat }} د.م</span></li>

            <li><strong>الشهور المدفوعة:</strong>
                <ul class="months-list">
                    @foreach (json_decode($receipt->months, true) as $month)
                        <li>{{ $month['month_ar'] }}</li>
                    @endforeach
                </ul>
            </li>
        </ul>

        <div class="qr-code-container">
            <h2>رمز الاستجابة السريعة (QR Code)</h2>
            <img src="{{ asset('storage/' . $receipt->qrCode) }}" alt="QR Code">
        </div>
    </div>
</body>
</html>
