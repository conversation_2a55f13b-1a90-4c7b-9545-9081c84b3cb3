import { inject, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from '../../core/app.state';
import {
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from '../shared/global.config';
import { patchState, signalState } from '@ngrx/signals';
import { EtudiantResponse } from './models/etudiant-response';
import {
  EtudiantSearchForm,
  IEtudiantSearchForm,
} from './models/etudiant-search-request';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, take } from 'rxjs';
import { finalize, map, switchMap, tap } from 'rxjs/operators';
import { Pagination } from '../shared/helpers/query.helper';
import {
  ApiResponse,
  ApiResponseNoPagination,
} from '../../core/models/api-response';
import { SetLoading } from '../shared/store/shared.actions';
import { EtudiantsService } from './etudiants.service';
import { SchoolResponse } from './models/school-response';

export type EtudiantsState = {
  etudiants: EtudiantResponse[];
  etudiantsSearchForm: IEtudiantSearchForm;
  current_page: number;
  total_pages: number;
  per_page: number;
  total_items: number;
  schools: SchoolResponse[];
  etudiant: EtudiantResponse;
};

export const initialEtudiantsState: EtudiantsState = {
  etudiants: [],
  etudiantsSearchForm: new EtudiantSearchForm(),
  current_page: DEFAULT_CURRENT_PAGE,
  total_pages: 1,
  per_page: DEFAULT_PAGE_SIZE,
  total_items: 0,
  schools: [],
  etudiant: null,
};

@Injectable()
export class EtudiantsStore {
  store = inject<Store<AppState>>(Store);

  readonly state = signalState<EtudiantsState>(initialEtudiantsState);

  #etudiantsService = inject(EtudiantsService);

  setSearchForm = (etudiantsSearchForm: IEtudiantSearchForm) =>
    patchState(this.state, { etudiantsSearchForm });

  setCurrentPage = (current_page: number) =>
    patchState(this.state, { current_page });

  getEtudiants = rxMethod<void>(
    pipe(
      switchMap(() => {
        const pagination: Pagination = {
          current_page: this.state.current_page(),
          per_page: this.state.per_page(),
        };
        return this.#etudiantsService
          .getListeEtudiants(this.state.etudiantsSearchForm(), pagination)
          .pipe(
            tap((response: ApiResponse<EtudiantResponse>) => {
              patchState(this.state, {
                etudiants: response.data,

                current_page: response.current_page,
                per_page: response.per_page,
                total_pages: response.total_pages,
                total_items: response.total_items,
              });
            }),
            finalize(() => {
              this.store.dispatch(SetLoading({ isAppLoading: false }));
            })
          );
      }),
      finalize(() => {})
    )
  );

  getAllSchools = rxMethod<void>(
    pipe(
      take(1),
      switchMap(() =>
        this.#etudiantsService.getListeSchools().pipe(
          map(
            (response: ApiResponseNoPagination<SchoolResponse>) => response.data
          ),
          tap((schools: SchoolResponse[]) => {
            patchState(this.state, { schools });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      ),
      finalize(() => {})
    )
  );

  getDetailsEtudiant = rxMethod<number>(
    pipe(
      switchMap((id: number) =>
        this.#etudiantsService.detailsEtudiant(id).pipe(
          map((response: EtudiantResponse) => response),
          tap((etudiant: EtudiantResponse) => {
            patchState(this.state, { etudiant });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      ),
      finalize(() => {})
    )
  );
}
