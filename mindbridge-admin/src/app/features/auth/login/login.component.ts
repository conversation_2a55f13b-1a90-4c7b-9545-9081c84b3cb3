import { Component, HostListener, inject } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Store } from '@ngrx/store';
import { AppState } from '../../../core/app.state';
import { LoginAction } from '../store/auth.actions';
import { SetLoading } from '../../shared/store/shared.actions';


@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styleUrl: './login.component.scss',
    imports: [ReactiveFormsModule]
})
export class LoginComponent {
  private formBuilder = inject(FormBuilder);
  private store = inject<Store<AppState>>(Store);

  loginForm!: FormGroup;

  ngOnInit(): void {
    this.loginForm = this.formBuilder.group({
      email: new FormControl(null, Validators.required),
      password: new FormControl(null, Validators.required)
    });
  }

  login(): boolean {
    if (!this.loginForm.valid) {
      this.loginForm.markAllAsTouched()
      return false;
    }

    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.store.dispatch(LoginAction({ payload: this.loginForm.value }));

    return true;
  }

  isScreenSizeValid(): boolean {
    return window.innerWidth >= 790;
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isScreenSizeValid();
  }
}
