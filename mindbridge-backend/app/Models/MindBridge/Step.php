<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Step extends Model
{
    use HasFactory;

    protected $connection = 'mind_bridge';

    protected $table = 'steps';
    
    protected $fillable = [
        'order',
        'required',
        'condition',
        'type',
        'test_id',
        'question_id',
        'points_value',
    ];

    protected $casts = [
        'required' => 'boolean',
        'points_value' => 'integer',
    ];

    public function test()
    {
        return $this->belongsTo(Test::class);
    }

    public function question()
    {
        return $this->belongsTo(Question::class, 'question_id');
    }



    public static function getNextStep(int $currentStepId, array $selectedOptionIds)
    {
        $currentStep = Step::find($currentStepId);

        if (!$currentStep) {
            return null;
        }

        if (is_null($currentStep->condition)) {
            return Step::with('question', 'question.options')->where('test_id', $currentStep->test_id)
                ->where('order', '>', $currentStep->order)
                ->orderBy('order', 'asc')
                ->first();
        }

        $rules = explode('|', $currentStep->condition);
        foreach ($rules as $rule) {
            $parts = explode('_', $rule);

            $previousStepId = (int) array_shift($parts);
            $nextStepId = (int) array_pop($parts);
            $requiredOptionIds = array_map('intval', $parts);
            if ($previousStepId === $currentStepId && array_intersect($selectedOptionIds, $requiredOptionIds) === $requiredOptionIds) {
                return Step::with('question', 'question.options')->find($nextStepId);
            }
        }
        return null;
    }
}
