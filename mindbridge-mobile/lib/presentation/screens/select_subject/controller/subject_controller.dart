import 'package:get/get.dart';
import 'package:mindbridge/app/routes.dart';
import 'package:mindbridge/core/constants/app_strings.dart';
import 'package:mindbridge/core/utilities/app_images.dart';
import 'package:mindbridge/presentation/widgets/global_dialog.dart';

import '../../../../core/models/subject.dart';
import '../../../../data/repositories/subject_repository.dart';

class SubjectController extends GetxController {
  final SubjectRepository _subjectRepository;

  SubjectController({required SubjectRepository subjectRepository})
      : _subjectRepository = subjectRepository;

  var subjects = <Matier>[].obs;
  var isLoading = false.obs;
  var isEmptyData = false.obs;
  var isError = false.obs;

  Future<void> fetchSubjects(int categoryId) async {
    isLoading.value = true;
    isEmptyData.value = false;
    isError.value = false;

    try {
      final response = await _subjectRepository.fetchSubjects(categoryId);

      if (response.isError) {
        isError.value = true;
      } else if (response.isEmpty) {
        GlobalDialog.showTheDialog(
          iconTap: () => Get.offAllNamed(Routes.btmNavigationBar),
          imagePath: AppImages.nothingFound,
          title: "Aucune recommandation disponible!",
          subtitle: '',
          buttonText: AppStrings.continuer,
          onButtonTap: () => Get.offAllNamed(Routes.btmNavigationBar),
          barrierDismissible: true,
        );
        isEmptyData.value = true;
      } else {
        subjects.assignAll(response.data!.matiers!);
      }
    } catch (e) {
      print("Error: $e");
      isError.value = true;
    } finally {
      isLoading.value = false;
    }
  }
}
