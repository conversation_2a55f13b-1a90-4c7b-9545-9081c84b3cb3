import {Pipe, PipeTransform} from "@angular/core";
import {BadgeNiveauEnum} from "../enums/badge-niveau.enum";

@Pipe({
  name: 'badgeNiveau',
  standalone: true,
})
export class BadgeNiveauPipe implements PipeTransform {
  // @ts-ignore
  transform(niveau: string): BadgeNiveauEnum {
    switch (niveau) {
      case '7ème':
        return BadgeNiveauEnum.septieme;
      case '8ème':
        return BadgeNiveauEnum.huitieme;
      case '9ème':
        return BadgeNiveauEnum.neuvieme;
    }
  }
}
