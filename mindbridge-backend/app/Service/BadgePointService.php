<?php
// File: app/Services/BadgePointService.php

namespace App\Service;

use App\Models\MindBridge\MindBridgeEtudiant;
use App\Models\MindBridge\Badges;
use App\Models\MindBridge\EtudaintBadges;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BadgePointService
{
    // Action keys and corresponding XP
    private const ACTION_XP = [
        'microlearning_session_completed'        => 10,
        'sondage_completed'                      => 15,
        'todo_completed'                         => 15,
        'quiz_easy_passed'                       => 50,
        'quiz_hard_passed'                       => 100,
        'weekly_test_completed'                  => 50,
        'weekly_test_score_over_80'              => 20,
        'three_tests_series_completed'           => 30,
        'lesson_viewed'                          => 10,
        'mental_health_self_test_participated'   => 70,
        'all_quizzes_subject_completed'          => 300,
        'daily_connection'                       => 5,
        '7_day_streak'                           => 50,
        '30_day_streak'                          => 200,
    ];

    protected MindBridgeEtudiant $etudiant;

    public function __construct(MindBridgeEtudiant $etudiant)
    {
        $this->etudiant = $etudiant;
    }

    /**
     * Process a given action: award XP and attempt badge unlock
     * @param string $actionKey one of the keys in ACTION_XP
     * @return int|null XP awarded or null if action not found
     */
    public function processAction(string $actionKey): int|null
    {
        Log::info("Processing action: $actionKey for student ID: {$this->etudiant->id}");
        
        if (!isset(self::ACTION_XP[$actionKey])) {
            Log::error("Invalid action key: $actionKey");
            return null;
        }

        $xp = self::ACTION_XP[$actionKey];
        Log::info("XP for action $actionKey: $xp");
        
        $this->addPoints($xp);
        return $xp;
    }

    /**
     * Add XP to the student's point total
     * @param int $xp Points to add
     */
    public function addPoints(int $xp): void
    {
        $points = $this->etudiant->refresh()->points;
        
        if (!$points) {
            $points = $this->etudiant->points()->create([
                'ep_number_points' => $xp,
                'ep_etudiant_id' => $this->etudiant->id
            ]);
        } else {
            $points->increment('ep_number_points', $xp);
        }

        $this->etudiant->refresh();
        
        $this->unlockBadges();
    }

    /**
     * Iterate all badges, unlock those for which student meets the required points
     */
    public function unlockBadges(): void
    {
        $currentXp = $this->etudiant->points?->ep_number_points ?? 0;
        
        $ownedBadgeIds = EtudaintBadges::where('eb_etudiant_id', $this->etudiant->id)
            ->pluck('eb_badge_id')
            ->toArray();
        
        Log::info("Student ID {$this->etudiant->id} has $currentXp points, owned badges: " . json_encode($ownedBadgeIds));
        
        $eligibleBadges = Badges::whereNotIn('id', $ownedBadgeIds)
            ->where('bdg_required_points', '<=', $currentXp)
            ->orderBy('bdg_required_points', 'asc')  
            ->get();
        
        if ($eligibleBadges->isEmpty()) {
            Log::info("No new badges to unlock for student ID: {$this->etudiant->id}");
            return;
        }
        
        Log::info("Found " . $eligibleBadges->count() . " eligible badges for student ID: {$this->etudiant->id}");
        
        foreach ($eligibleBadges as $badge) {
            $exists = EtudaintBadges::where([
                'eb_etudiant_id' => $this->etudiant->id,
                'eb_badge_id' => $badge->id
            ])->exists();
            
            if (!$exists) {
                Log::info("Unlocking badge ID {$badge->id} ({$badge->bdg_name}) for student ID: {$this->etudiant->id}");
                DB::transaction(function () use ($badge) {
                    EtudaintBadges::create([
                        'eb_etudiant_id' => $this->etudiant->id,
                        'eb_badge_id' => $badge->id,
                    ]);
                });
            } else {
                Log::info("Badge ID {$badge->id} already owned by student ID: {$this->etudiant->id}");
            }
        }
        $this->etudiant->refresh();
    }
}