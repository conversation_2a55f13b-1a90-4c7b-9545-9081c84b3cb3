import 'dart:async';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'package:mindbridge/core/models/test_action_type.dart';
import 'package:mindbridge/core/utilities/app_images.dart';
import 'package:mindbridge/data/providers/local/secure_storage_service.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';

import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/category.dart';
import '../../../../core/models/profile.dart';
import '../../../../data/repositories/auth_repository.dart';
import '../../../../data/repositories/home_repository.dart';
import '../../../widgets/global_dialog.dart';
import '../../../widgets/test_btm_sheet.dart';

class HomeController extends GetxController {
  final HomeRepository _homeRepository;

  Rx<Profile?> profile = Rx<Profile?>(null);

  HomeController({
    required HomeRepository homeRepository,
  }) : _homeRepository = homeRepository;

  var categories = <Child>[].obs;
  var profileLoading = false.obs;

  Future<void> loadProfile() async {
    profileLoading.value = true;
    try {
      final authRepository = Get.find<AuthRepository>();
      final storedUser = await authRepository.getStoredUser();

      final userId = storedUser?.user?.id ?? 0;
      if (userId > 0) {
        final fetchedProfile = await _homeRepository.fetchStudentInfo(userId);

        if (fetchedProfile != null) {
          profile.value = fetchedProfile;
        }
      }
    } catch (e) {
      print("Error loading profile: $e");
    } finally {
      profileLoading.value = false;
    }
  }

  var isLoading = false.obs;
  var isError = false.obs;
  var isEmptyData = false.obs;

  Future<void> fetchCategories() async {
    isLoading.value = true;
    isError.value = false;
    isEmptyData.value = false;
    final secureStorage = Get.find<SecureStorageService>();

    try {
      final String? testId = await secureStorage.read('has_mental_health_test');
      final response = await _homeRepository.fetchCategories(testId);
      if (response.isError) {
        isError.value = true;
      } else if (response.isEmpty) {
        isEmptyData.value = true;
      } else {
        final categoryData = response.data!;
        categories.assignAll(categoryData.testModels?.children ?? []);


        if (categoryData.mentalHealthToDo != null) {
          TestBtmSheet.show(categoryData.mentalHealthToDo!, type: ActionTestTypes.mentalHealthSelfTestParticipated.value);
        }else if (categoryData.testToDo != null) {
          TestBtmSheet.show(categoryData.testToDo!, type: ActionTestTypes.todoComplated.value);
        } else if (categoryData.sondagesToDo != null) {
          TestBtmSheet.show(categoryData.sondagesToDo!, type: ActionTestTypes.sondageCompleted.value);
        } else if (categoryData.welcomeMessage != null &&
            categoryData.welcomeMessage!.isNotEmpty) {
          GlobalDialog.showTheDialog(
            iconTap: () => Get.back(),
            imagePath: AppImages.bigOwl,
            title: categoryData.welcomeMessage!,
            subtitle: '',
            buttonText: AppStrings.continuer,
            onButtonTap: () => Get.back(),
            barrierDismissible: true,
          );
        }
      }
    } catch (e) {
      print("Error fetching categories: $e");
      isError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  void resetCarousel() {
    if (categories.isNotEmpty) {
      currentPage.value = 2;
    }
  }

  var currentPage = 2.obs;
  var selectedAvatarId = 'unique_user_0'.obs;

  var selectedAiButton = ''.obs;

  var isFileUploaded = false.obs;
  var selectedFile = Rxn<PlatformFile>();

  final AudioRecorder audioRecorder = AudioRecorder();
  final recorderController = RecorderController();

  final playerController = PlayerController();

  var isRecording = false.obs;
  var isPaused = false.obs;
  var recordedFilePath = ''.obs;

  var isPlaying = false.obs;
  final RxString currentPositionText = '0:00'.obs;
  final RxString totalDurationText = '0:00'.obs;
  Future<void> callAPIs() async {
    if (profileLoading.value || isLoading.value) {
      return;
    }
    await Future.wait([loadProfile(), fetchCategories()]);
  }

  @override
  void onInit() {
    super.onInit();
    callAPIs();
    playerController.onCurrentDurationChanged.listen((position) {
      final duration = playerController.maxDuration;
      totalDurationText.value =
          formatDuration(Duration(milliseconds: duration));
      currentPositionText.value =
          formatDuration(Duration(milliseconds: position));
    });

    playerController.onCompletion.listen((_) async {
      isPlaying.value = false;
      await resetAudioForReplay();
    });
  }

  String formatDuration(Duration duration) {
    final minutes = duration.inMinutes.toString().padLeft(1, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return "$minutes:$seconds";
  }

  void updateCurrentPage(int index) {
    currentPage.value = index;
  }

  void updateAvatarId(String newAvatarId) {
    selectedAvatarId.value = newAvatarId;
  }

  void updateSelectedAiButton(String button) {
    if (selectedAiButton.value == button) {
      selectedAiButton.value = '';
    } else {
      selectedAiButton.value = button;
    }
  }

  Future<void> pickFile() async {
    final result = await FilePicker.platform.pickFiles();
    if (result != null && result.files.isNotEmpty) {
      selectedFile.value = result.files.first;
      isFileUploaded.value = true;
    }
  }

  void clearSelectedFile() {
    selectedFile.value = null;
    isFileUploaded.value = false;
  }

  Future<void> startRecording() async {
    if (isRecording.value) return;

    final hasPerm = await audioRecorder.hasPermission();
    if (!hasPerm) {
      print("No recording permission granted!");
      return;
    }

    final directory = await getApplicationDocumentsDirectory();
    final path =
        '${directory.path}/my_audio_${DateTime.now().millisecondsSinceEpoch}.m4a';

    await audioRecorder.start(
      const RecordConfig(
        encoder: AudioEncoder.aacLc,
        bitRate: 128000,
        sampleRate: 44100,
      ),
      path: path,
    );

    recorderController.refresh();
    recorderController.record();

    isRecording.value = true;
    isPaused.value = false;
    recordedFilePath.value = path;
  }

  Future<void> pauseRecording() async {
    await audioRecorder.pause();
    recorderController.pause();
    isPaused.value = true;
  }

  Future<void> resumeRecording() async {
    await audioRecorder.resume();
    recorderController.record();
    isPaused.value = false;
  }

  Future<void> stopRecording() async {
    final path = await audioRecorder.stop();
    recorderController.stop();
    isRecording.value = false;
    isPaused.value = false;

    if (path != null) {
      recordedFilePath.value = path;
      print("Audio saved at: $path");

      await loadData(path);
    }
  }

  Future<void> loadData(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await playerController.preparePlayer(
          path: path,
          shouldExtractWaveform: true,
          volume: 1.0,
        );
      } else {
        print("File not found at path: $path");
      }
    } catch (e) {
      print("Error extracting waveform data: $e");
    }
  }

  Future<void> cancelRecording() async {
    await audioRecorder.cancel();
    recorderController.stop();
    isRecording.value = false;
    isPaused.value = false;
    recordedFilePath.value = '';
  }

  Future<void> playRecording() async {
    if (recordedFilePath.value.isNotEmpty) {
      try {
        if (!isPlaying.value) {
          await playerController.startPlayer();
          playerController.setFinishMode(finishMode: FinishMode.pause);
          isPlaying.value = true;
        } else {
          playerController.pausePlayer();
        }
      } catch (e) {
        print("Error playing audio: $e");
      }
    }
  }

  Future<void> pausePlayback() async {
    try {
      await playerController.pausePlayer();
      isPlaying.value = false;
    } catch (e) {
      print("Error pausing audio: $e");
    }
  }

  Future<void> stopPlayback() async {
    try {
      await playerController.stopPlayer();
      isPlaying.value = false;
    } catch (e) {
      print("Error stopping audio: $e");
    }
  }

  Future<void> resetAudioForReplay() async {
    try {
      await playerController.seekTo(0);
      await loadData(recordedFilePath.value);
    } catch (e) {
      print("Error resetting audio: $e");
    }
  }

  @override
  void onClose() {
    audioRecorder.dispose();
    recorderController.dispose();
    playerController.dispose();
    super.onClose();
  }
}
