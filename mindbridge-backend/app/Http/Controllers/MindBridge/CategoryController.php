<?php

namespace App\Http\Controllers\MindBridge;
use App\Http\Controllers\Controller;
use App\Models\MindBridge\Category;

class CategoryController extends Controller
{
    
  public function index()
  {
      //
  }

  public function getSubcategories(){
    try{
        $categories = Category::with('subcategories')
        ->where('parent_id', 2)
        ->whereHas('subcategories') // Ensures only categories that have subcategories are fetched
        ->get();        
        if (!$categories){
            return response()->json([
                'error' => trans("categorieConst.const.cannot_delete_categorie")
            ], Response::HTTP_BAD_REQUEST);
        }

          return response()->json([
            'data' => $categories
        ], 200);
    } catch (Exception $e) {
        Log::error("====== CategorieController::getSubcategories => Error: {$e} ======");
        DB::rollBack();
        return response()->json([
            'error' => trans("categorieConst.const.unexpected_error")
        ], Response::HTTP_BAD_REQUEST);
    }
  }

}
