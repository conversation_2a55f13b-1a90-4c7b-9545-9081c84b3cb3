import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ObservationsService } from '../../services/observations.service';
import { CommonService } from '../../../shared/common.service';
import { SanteMentaleService } from '../../../sante-mentale/sante-mentale.service';

@Component({
  selector: 'app-observation-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatDialogModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './observation-form.component.html',
  styleUrl: './observation-form.component.scss',
})
export class ObservationFormComponent implements OnInit {
  private observationsService = inject(ObservationsService);
  private commonService = inject(CommonService);
  private santeMentaleService = inject(SanteMentaleService);
  private dialogRef = inject(MatDialogRef<ObservationFormComponent>);
  private fb = inject(FormBuilder);
  private data = inject(MAT_DIALOG_DATA);

  form: FormGroup;
  isLoading = false;
  isSubmitting = false;
  isEditMode = false;
  availableTests: any[] = [];

  constructor() {
    this.form = this.fb.group({
      label: ['', [Validators.required, Validators.minLength(3)]],
      category: [''],
      visible_to: ['both', Validators.required],
      description: [''],
      active: [true],
      test_id: ['', Validators.required], // Make test_id required
      trigger_type: ['parent', Validators.required], // Add trigger_type field
    });
  }

  ngOnInit(): void {
    if (this.data) {
      this.isEditMode = true;
      this.form.patchValue(this.data);
    }
    this.loadAvailableTests();
  }

  loadAvailableTests(): void {
    this.isLoading = true;
    // Load all tests from Sante Mentale category (ID: 2) and all its children
    this.santeMentaleService.getAllTestsByParentCategory(2).subscribe({
      next: (response: any) => {
        this.availableTests = response.data || [];
        this.isLoading = false;
      },
      error: () => {
        this.commonService.showToast('Erreur lors du chargement des tests', 'ERROR');
        this.isLoading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.form.invalid) {
      this.commonService.showToast('Veuillez remplir tous les champs obligatoires', 'ERROR');
      return;
    }

    this.isSubmitting = true;
    const formValue = this.form.value;

    if (this.isEditMode) {
      this.observationsService.updateObservation(this.data.id, formValue).subscribe({
        next: () => {
          this.commonService.showToast('Observation mise à jour avec succès', 'SUCCESS');
          this.isSubmitting = false;
          this.dialogRef.close(true);
        },
        error: () => {
          this.commonService.showToast('Erreur lors de la mise à jour de l\'observation', 'ERROR');
          this.isSubmitting = false;
        },
      });
    } else {
      this.observationsService.createObservation(formValue).subscribe({
        next: () => {
          this.commonService.showToast('Observation créée avec succès', 'SUCCESS');
          this.isSubmitting = false;
          this.dialogRef.close(true);
        },
        error: () => {
          this.commonService.showToast('Erreur lors de la création de l\'observation', 'ERROR');
          this.isSubmitting = false;
        },
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }


}

