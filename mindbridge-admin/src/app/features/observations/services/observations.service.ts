import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface Observation {
  id?: number;
  label: string;
  category?: string;
  visible_to: 'teacher' | 'parent' | 'both';
  description?: string;
  active: boolean;
  test_id?: number;
  test?: any;
  trigger_type?: 'parent' | 'teacher' | 'simulated_grade_shutdown';
}

export interface ObservationResponse {
  success: boolean;
  data: Observation[];
  current_page: number;
  per_page: number;
  total_pages: number;
  total_items: number;
}

@Injectable({
  providedIn: 'root'
})
export class ObservationsService {
  constructor(private http: HttpClient) {}

  getObservations(
    page: number = 1,
    limit: number = 10,
    search?: string,
    category?: string,
    visibleTo?: string,
    active?: boolean
  ): Observable<ObservationResponse> {
    let url = `${environment.BASE_URL_API}mind_bridge/observations?page=${page}&limit=${limit}`;

    if (search) {
      url += `&search=${encodeURIComponent(search)}`;
    }
    if (category) {
      url += `&category=${encodeURIComponent(category)}`;
    }
    if (visibleTo) {
      url += `&visible_to=${encodeURIComponent(visibleTo)}`;
    }
    if (active !== undefined) {
      url += `&active=${active ? 1 : 0}`;
    }

    return this.http.get<ObservationResponse>(url);
  }

  getObservation(id: number): Observable<any> {
    return this.http.get<any>(
      `${environment.BASE_URL_API}mind_bridge/observations/${id}`
    );
  }

  createObservation(observation: Observation): Observable<any> {
    return this.http.post<any>(
      `${environment.BASE_URL_API}mind_bridge/observations`,
      observation
    );
  }

  updateObservation(id: number, observation: Observation): Observable<any> {
    return this.http.put<any>(
      `${environment.BASE_URL_API}mind_bridge/observations/${id}`,
      observation
    );
  }

  deleteObservation(id: number): Observable<any> {
    return this.http.delete<any>(
      `${environment.BASE_URL_API}mind_bridge/observations/${id}`
    );
  }

  getMappedTest(id: number): Observable<any> {
    return this.http.get<any>(
      `${environment.BASE_URL_API}mind_bridge/observations/${id}/test`
    );
  }
}

