import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import 'return_back_button.dart';

class GlobalWhiteCard extends StatelessWidget {
  final String title;
  final VoidCallback? onBackTap;
  final Widget child;
  final Widget? footer;

  const GlobalWhiteCard({
    super.key,
    required this.title,
    this.onBackTap,
    required this.child,
    this.footer,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(2.h),
      ),
      color: Colors.white,
      elevation: 1,
      child: Padding(
        padding: EdgeInsets.all(3.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                ReturnBackButton(onTap: onBackTap),
                SizedBox(width: 2.w),
                Flexible(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Expanded(child: child),
            if (footer != null) ...[
              SizedBox(height: 2.h),
              footer!,
            ],
          ],
        ),
      ),
    );
  }
}
