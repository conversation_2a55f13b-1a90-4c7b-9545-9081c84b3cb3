<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TestScoringRule extends Model
{
    use HasFactory;

    protected $connection = 'mind_bridge';

    protected $table = 'test_scoring_rules';

    protected $fillable = [
        'test_id',
        'min_percentage',
        'max_percentage',
        'interpretation',
        'feedback',
        'recommendation',
    ];

    protected $casts = [
        'min_percentage' => 'decimal:2',
        'max_percentage' => 'decimal:2',
    ];

    /**
     * Get the test that owns this rule
     */
    public function test()
    {
        return $this->belongsTo(Test::class);
    }
}

