<?php

namespace App\Http\Requests\MindBridge;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Log;

class TestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
    public $testMentalCategories = [11, 12, 13, 14, 15, 16, 17, 18];

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        Log::info("====== EtudiantRequest => Payload Before Validation ======\n" . json_encode($this->all(), JSON_PRETTY_PRINT));
        return [
            'test' => 'required|array',
            'test.title' =>  'required|string',
            'test.description' => 'string',
            'test.category_id' => 'required|not_in:1,2',
            'test.difficulty_level' => [
                'in:tres_facile,facile,intermediaire,difficile,tres_difficile',
                function ($attribute, $value, $fail) {
                    $requiredCategories = [4, 5, 6, 8];
                    if (in_array(request('test.category_id'), $requiredCategories) && is_null($value)) {
                        $fail("The $attribute is required for categories 4, 5, 6, and 8.");
                    }
                },
            ],
            'test.niveau_id' => [
                'required_if:test.category_id,6,5,4',
                'integer',
                function ($attribute, $value, $fail) {
                    if (request('test.category_id') == 3 || request('test.category_id') == 7 || in_array(request('test.category_id'), $this->testMentalCategories)) {
                        $fail("The $attribute must be blocked for this category of test.");
                    }
                },
            ],
            'test.timer' => [
                'nullable',
                function ($attribute, $value, $fail) {
                    $categoryId = request('test.category_id');
                    if (!in_array($categoryId, [5]) && !in_array($categoryId, $this->testMentalCategories)) {
                        $fail("The $attribute must be blocked for this category of test.");
                    }
                },
            ],
            'test.challenge_date_start' => [
                'required_if:test.category_id,8',
                function ($attribute, $value, $fail) {
                    if (request('test.category_id') != 8 && !in_array(request('test.category_id'), $this->testMentalCategories)) {
                        $fail("The $attribute must be blocked for this category of test.");
                    }
                },

            ],
            'test.challenge_date_end' => [
                'required_if:test.category_id,8',
                function ($attribute, $value, $fail) {
                    if (request('test.category_id') != 8 && !in_array(request('test.category_id'), $this->testMentalCategories)) {
                        $fail("The $attribute must be blocked for this category of test.");
                    }
                },

            ],
            'test.chapters' => [
                'required_if:test.category_id,8|array',
                function ($attribute, $value, $fail) {
                    if (request('test.category_id') != 8 && !in_array(request('test.category_id'), $this->testMentalCategories)) {
                        $fail("The $attribute must be blocked for this category of test.");
                    }
                },

            ],
            'test.content_id' => [
                'required_if:test.category_id,4',
                'integer',
                function ($attribute, $value, $fail) {
                    if (request('test.category_id') == 3 || request('test.category_id') == 6 || request('test.category_id') == 5 || request('test.category_id') == 8 || request('test.category_id') == 7 || in_array(request('test.category_id'), $this->testMentalCategories)) {
                        $fail("The $attribute must be blocked for this category of test.");
                    }
                },
            ],
            'test.matiere_id' => [
                'required_if:test.category_id,5',
                'integer',
                function ($attribute, $value, $fail) {
                    if (request('test.category_id') == 3 || request('test.category_id') == 7 || in_array(request('test.category_id'), $this->testMentalCategories)) {
                        $fail("The $attribute must be blocked for this category of test.");
                    }
                },
            ],

            'steps' =>  'required|array',
            'steps.*.order' => 'required|integer|min:1',
            'steps.*.type' => 'required|string|in:one,many,true_false',
            'steps.*.required' => 'nullable',

            'steps.*.question_id' => 'nullable|integer',
            'steps.*.question' => 'required_without:steps.*.question_id|array',
            'steps.*.question.type' => 'required_if:steps.*.question_id,null|string|in:text,image,video,audio',
            'steps.*.question.is_true' => [
                'nullable',
                function ($attribute, $value, $fail) {
                    $isFalse = request(str_replace('is_true', 'is_false', $attribute));
                    if (is_null($value) && is_null($isFalse)) {
                        $fail("At least one of 'is_true' or 'is_false' must be set.");
                    }
                },
                'boolean',
            ],
            'steps.*.question.is_false' => [
                'nullable',
                function ($attribute, $value, $fail) {
                    $isTrue = request(str_replace('is_false', 'is_true', $attribute));
                    if (is_null($value) && is_null($isTrue)) {
                        $fail("At least one of 'is_true' or 'is_false' must be set.");
                    }
                },
                'boolean',
            ],

            'steps.*.question.description' => 'nullable|string',
            'steps.*.question.image_path' => 'nullable|file',

            // Allow content to be either a file or a string
            'steps.*.question.content' => [
                'required_without:steps.*.question_id',
                function ($attribute, $value, $fail) {
                    if (!is_string($value) && !is_file($value)) {
                        $fail("The $attribute must be either a string or a valid file.");
                    }
                }
            ],
        ];
    }

    protected function prepareForValidation()
    {
        $steps = $this->input('steps', []);

        foreach ($steps as $index => &$step) {
            if (isset($step['question']['is_false'])) {
                $step['question']['is_false'] = filter_var($step['question']['is_false'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            }
            if (isset($step['question']['is_true'])) {
                $step['question']['is_true'] = filter_var($step['question']['is_true'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            }

            if (isset($step['question']['required'])) {
                $step['question']['required'] = filter_var($step['question']['required'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            }
            if (isset($step['question']['options']) && is_array($step['question']['options'])) {
                foreach ($step['question']['options'] as &$option) {
                    if (isset($option['isCorrect'])) {
                        $option['isCorrect'] = filter_var($option['isCorrect'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                    }
                }
            }
        }

        $this->merge(['steps' => $steps]);
    }


    public function messages(): array
    {
        return [
            'test.required' => 'The test object is required.',
            'test.title.required' => 'The test title is required.',
            'test.type.in' => 'The test type must be test_profiling, culture_general, test_content or exam_simulation.',
            'test.difficulty_level.in' => 'The difficulty level must be one of: tres_facile, facile, intermediaire, difficile, tres_difficile.',

            'steps.required' => 'The steps array is required.',
            'steps.*.order.required' => 'Each step must have an order.',
            'steps.*.type.in' => 'The step type must be either one, many, or true_false.',

            'steps.*.question.required' => 'Each step must have a question.',
            'steps.*.question.type.in' => 'The question type must be text, image, video, or audio.',
            'steps.*.question.content.required' => 'The question content is required.',
            'steps.*.question.is_true.required_if' => 'The is_true field is required for Vrai/Faux questions.',
            'steps.*.question.is_false.required_if' => 'The is_false field is required for Vrai/Faux questions.',

            'steps.*.question.options.required' => 'Each question must have options.',
            'steps.*.question.options.*.name.required' => 'Each option must have a name.',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param Validator $validator
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator): void
    {
        Log::error("====== TestRequest => Validation Errors ======\n" . json_encode($validator->errors()->toArray(), JSON_PRETTY_PRINT));
        throw new HttpResponseException(response()->json($validator->errors(), 400));
    }

    protected function isUpdating(): bool
    {
        return $this->isMethod('put');
    }
}
