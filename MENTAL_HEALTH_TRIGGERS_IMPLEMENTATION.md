# Mental Health Test Triggers Implementation

## Overview
Implemented three types of triggers for mental health tests in the student list component (liste-etudiants). Users can now trigger mental health tests through:
1. **Parent Trigger** - Select from a list of parent observations
2. **Teacher Trigger** - Direct trigger by teacher
3. **Simulated Grade Shutdown** - Trigger based on simulated grade shutdown

## Backend Implementation

### 1. Database Migration
**File:** `mindbridge-backend/database/migrations/mindBridge/2025_11_04_000001_add_trigger_type_to_observations_table.php`

Added `trigger_type` enum field to observations table with values:
- `parent`
- `teacher`
- `simulated_grade_shutdown`

### 2. Model Updates
**File:** `mindbridge-backend/app/Models/MindBridge/Observation.php`

Updated fillable array to include `trigger_type` field.

### 3. API Endpoints

#### Updated Endpoints
- **GET/POST/PUT observations** - Now support `trigger_type` parameter
- Validation added for trigger_type in store and update methods

#### New Endpoint
- **GET `/api/mind_bridge/observations/by-trigger-type`**
  - Query Parameters:
    - `trigger_type` (required): parent|teacher|simulated_grade_shutdown
    - `visible_to` (optional): teacher|parent|both
  - Returns: List of active observations filtered by trigger type and visibility

**File:** `mindbridge-backend/app/Http/Controllers/MindBridge/ObservationController.php`

Added `getByTriggerType()` method to fetch observations by trigger type.

### 4. Routes
**File:** `mindbridge-backend/routes/api.php`

Added route:
```php
Route::get('observations/by-trigger-type', [ObservationController::class, 'getByTriggerType']);
```

## Frontend Implementation

### 1. Mental Health Trigger Modal Component
**Files:**
- `mindbridge-admin/src/app/features/etudiants/components/mental-health-trigger-modal/mental-health-trigger-modal.component.ts`
- `mindbridge-admin/src/app/features/etudiants/components/mental-health-trigger-modal/mental-health-trigger-modal.component.html`
- `mindbridge-admin/src/app/features/etudiants/components/mental-health-trigger-modal/mental-health-trigger-modal.component.scss`

**Features:**
- Three trigger type buttons (Parent, Teacher, Simulated Grade Shutdown)
- Dynamic observation list loading for parent trigger
- Observation selection with visual feedback
- Responsive design with Material Design styling

### 2. Liste-Etudiants Component Updates
**File:** `mindbridge-admin/src/app/features/etudiants/components/liste-etudiants/liste-etudiants.component.ts`

**New Methods:**
- `openMentalHealthTrigger(etudiant)` - Opens trigger modal
- `handleTriggerResult(result, etudiant)` - Processes trigger selection
- `runMentalHealthTest(subcategoryId, etudiantId)` - Calls backend API

**New Imports:**
- MatDialog for modal functionality
- HttpClient for API calls
- MentalHealthTriggerModalComponent

### 3. Liste-Etudiants Template Updates
**File:** `mindbridge-admin/src/app/features/etudiants/components/liste-etudiants/liste-etudiants.component.html`

Added trigger action button next to the visibility button in the student list table.

### 4. Observations Service Updates
**File:** `mindbridge-admin/src/app/features/observations/services/observations.service.ts`

Updated Observation interface to include `trigger_type` field.

### 5. Observation Form Component Updates
**Files:**
- `mindbridge-admin/src/app/features/observations/components/observation-form/observation-form.component.ts`
- `mindbridge-admin/src/app/features/observations/components/observation-form/observation-form.component.html`

Added `trigger_type` form control with dropdown selection for:
- Parent
- Teacher
- Simulated Grade Shutdown

## Workflow

### For Parent Trigger:
1. Admin clicks trigger button on student row
2. Modal opens with three trigger options
3. Admin selects "Parent" option
4. Observation list loads from backend
5. Admin selects an observation
6. System calls `runMentalHealthTestBySubCategory` with observation's test_id
7. Test is assigned to student

### For Teacher Trigger:
1. Admin clicks trigger button on student row
2. Modal opens with three trigger options
3. Admin selects "Teacher" option
4. System calls `runMentalHealthTestBySubCategory` with default test_id
5. Test is assigned to student

### For Simulated Grade Shutdown:
1. Admin clicks trigger button on student row
2. Modal opens with three trigger options
3. Admin selects "Simulated Grade Shutdown" option
4. System logs the trigger (can be extended with additional logic)

## API Integration

### Backend Call
```
POST /api/mind_bridge/run_test
Body: {
  "subcategory_id": <test_id>,
  "etudiant_id": <student_id>
}
```

### Observation Fetching
```
GET /api/mind_bridge/observations/by-trigger-type?trigger_type=parent&visible_to=parent
```

## Database Schema

### Observations Table
```sql
ALTER TABLE observations ADD COLUMN trigger_type ENUM('parent', 'teacher', 'simulated_grade_shutdown') DEFAULT 'parent' AFTER test_id;
```

## Testing Checklist

- [ ] Run database migration
- [ ] Test parent trigger with observation selection
- [ ] Test teacher trigger
- [ ] Test simulated grade shutdown trigger
- [ ] Verify observations are filtered by trigger_type
- [ ] Verify observations are filtered by visible_to role
- [ ] Test observation form with trigger_type field
- [ ] Verify API calls are made correctly
- [ ] Test error handling and user feedback

## Files Modified/Created

### Backend
- ✅ Created: `2025_11_04_000001_add_trigger_type_to_observations_table.php`
- ✅ Modified: `Observation.php`
- ✅ Modified: `ObservationController.php`
- ✅ Modified: `routes/api.php`

### Frontend
- ✅ Created: `mental-health-trigger-modal.component.ts`
- ✅ Created: `mental-health-trigger-modal.component.html`
- ✅ Created: `mental-health-trigger-modal.component.scss`
- ✅ Modified: `liste-etudiants.component.ts`
- ✅ Modified: `liste-etudiants.component.html`
- ✅ Modified: `liste-etudiants.component.scss`
- ✅ Modified: `observations.service.ts`
- ✅ Modified: `observation-form.component.ts`
- ✅ Modified: `observation-form.component.html`

## Next Steps

1. Run database migration: `php artisan migrate --path=database/migrations/mindBridge/2025_11_04_000001_add_trigger_type_to_observations_table.php`
2. Test the complete flow in development environment
3. Update observations in database with appropriate trigger_type values
4. Deploy to production

