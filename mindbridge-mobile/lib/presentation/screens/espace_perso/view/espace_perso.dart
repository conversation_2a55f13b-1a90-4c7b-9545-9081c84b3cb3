import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/presentation/screens/espace_perso/widgets/badges_section.dart';
import 'package:mindbridge/presentation/screens/espace_perso/widgets/espace_header.dart';
import 'package:sizer/sizer.dart';

import '../../home/<USER>/home_controller.dart';
import '../widgets/documents_section.dart';

class EspacePersonnel extends StatelessWidget {
  const EspacePersonnel({super.key});

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find<HomeController>();

    

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        body: Safe<PERSON>rea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                Obx(
                  () => EspaceHeader(
                    avatarSize: 12.h,
                    avatarId: controller.selectedAvatarId.value,
                    onAvatarChange: controller.updateAvatarId,
                  ),
                ),
                Obx(
                  () {
                    final badges = controller.profile.value?.badges ?? [];
                    return BadgesSection(badges: badges);
                  },
                ),
                const DocumentsSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
