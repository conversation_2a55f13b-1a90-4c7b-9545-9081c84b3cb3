<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class UserResourceCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public $collects = UserResource::class;

    public static $wrap = null;
    public function toArray($request)
    {
        return parent::toArray($request);
    }

}
