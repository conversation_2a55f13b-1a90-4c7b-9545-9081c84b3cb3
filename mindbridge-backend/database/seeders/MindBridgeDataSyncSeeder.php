<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\School;
use App\Models\NiveauCentrale;
use App\Models\MatiereCentrale;
use App\Models\MindBridge\MindBridgeEtudiant;
use App\Models\MindBridge\Test;
use App\Models\MindBridge\Question;
use App\Models\MindBridge\Content;
use App\Models\MindBridge\Chapter;
use Exception;

class MindBridgeDataSyncSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * This seeder synchronizes existing MindBridge database data with the central database
     */
    public function run(): void
    {
        Log::info('Starting MindBridge data synchronization...');

        try {
            // Step 1: Ensure MindBridge school exists in central database
            $this->ensureMindBridgeSchoolExists();

            // Step 2: Sync Niveaux (Educational Levels) from MindBridge to Central
            $this->syncNiveaux();

            // Step 3: Sync Matieres (Subjects) from MindBridge to Central
            $this->syncMatieres();

            // Step 4: Sync Users from MindBridge to Central
            $this->syncUsers();

            // Step 5: Sync Niveaux-Matiere relationships
            $this->syncNiveauxMatiereRelationships();

            // Step 6: Update existing MindBridge data to reference central IDs
            $this->updateMindBridgeReferences();

            Log::info('MindBridge data synchronization completed successfully!');

        } catch (Exception $e) {
            Log::error('Error during MindBridge data synchronization: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Ensure MindBridge school exists in central database
     */
    private function ensureMindBridgeSchoolExists(): void
    {
        Log::info('Ensuring MindBridge school exists in central database...');

        $school = School::where('database', 'mind_bridge')->first();
        
        if (!$school) {
            School::create([
                'name' => 'Mind Bridge',
                'domain' => 'mindbridge-admin.awlyg.tech',
                'database' => 'mind_bridge',
                'migrations_path' => 'database/migrations/mindBridge/',
                'organization_id' => null,
            ]);
            Log::info('MindBridge school created in central database');
        } else {
            Log::info('MindBridge school already exists in central database');
        }
    }

    /**
     * Sync educational levels from MindBridge to Central database
     */
    private function syncNiveaux(): void
    {
        Log::info('Syncing Niveaux from MindBridge to Central...');

        // Get unique niveau_ids referenced in MindBridge database
        $mindBridgeNiveaux = collect();

        // Check etudiants table for niveau_ids
        $etudiantNiveaux = DB::connection('mind_bridge')
            ->table('etudiants')
            ->whereNotNull('niveau_id')
            ->distinct()
            ->pluck('niveau_id');

        // Check tests table for niveau_ids
        $testNiveaux = DB::connection('mind_bridge')
            ->table('tests')
            ->whereNotNull('niveau_id')
            ->distinct()
            ->pluck('niveau_id');

        // Check questions table for niveau_ids
        // Note: questions table doesn't have niveau_id column in actual database
        $questionNiveaux = collect();

        // Check contents table for niveau_ids
        $contentNiveaux = DB::connection('mind_bridge')
            ->table('contents')
            ->whereNotNull('niveau_id')
            ->distinct()
            ->pluck('niveau_id');

        // Merge all niveau IDs
        $allNiveauxIds = $etudiantNiveaux
            ->merge($testNiveaux)
            ->merge($questionNiveaux)
            ->merge($contentNiveaux)
            ->unique()
            ->filter();

        Log::info('Found niveau IDs in MindBridge: ' . $allNiveauxIds->implode(', '));

        // Create default niveaux if they don't exist in central database
        foreach ($allNiveauxIds as $niveauId) {
            $existingNiveau = NiveauCentrale::find($niveauId);
            
            if (!$existingNiveau) {
                // Create a default niveau with the same ID
                $niveauName = $this->generateNiveauName($niveauId);
                
                NiveauCentrale::create([
                    'id' => $niveauId,
                    'name' => $niveauName,
                    'description' => "Niveau synchronisé depuis MindBridge (ID: {$niveauId})",
                    'color' => $this->generateNiveauColor($niveauId),
                    'background' => $this->generateNiveauBackground($niveauId),
                ]);
                
                Log::info("Created niveau: {$niveauName} (ID: {$niveauId})");
            }
        }
    }

    /**
     * Sync subjects from MindBridge to Central database
     */
    private function syncMatieres(): void
    {
        Log::info('Syncing Matieres from MindBridge to Central...');

        // Get unique matiere_ids referenced in MindBridge database
        $testMatieres = DB::connection('mind_bridge')
            ->table('tests')
            ->whereNotNull('matiere_id')
            ->distinct()
            ->pluck('matiere_id');

        // Note: questions table doesn't have matier_id column in actual database
        $questionMatieres = collect();

        $contentMatieres = DB::connection('mind_bridge')
            ->table('contents')
            ->whereNotNull('matiere_id')
            ->distinct()
            ->pluck('matiere_id');

        $chapterMatieres = DB::connection('mind_bridge')
            ->table('chapters')
            ->whereNotNull('matiere_id')
            ->distinct()
            ->pluck('matiere_id');

        // Merge all matiere IDs
        $allMatieresIds = $testMatieres
            ->merge($questionMatieres)
            ->merge($contentMatieres)
            ->merge($chapterMatieres)
            ->unique()
            ->filter();

        Log::info('Found matiere IDs in MindBridge: ' . $allMatieresIds->implode(', '));

        // Create default matieres if they don't exist in central database
        foreach ($allMatieresIds as $matiereId) {
            $existingMatiere = MatiereCentrale::find($matiereId);
            
            if (!$existingMatiere) {
                // Create a default matiere with the same ID
                $matiereName = $this->generateMatiereName($matiereId);
                
                MatiereCentrale::create([
                    'id' => $matiereId,
                    'name_fr' => $matiereName,
                    'name_ar' => $this->generateMatiereNameAr($matiereName),
                    'description' => "Matière synchronisée depuis MindBridge (ID: {$matiereId})",
                    'image_url' => 'mindbridge/default_matiere.png',
                    'gradient_background' => $this->generateMatiereGradient($matiereId),
                    'gradient_border' => $this->generateMatiereGradient($matiereId),
                    'title_color' => $this->generateMatiereTitleColor($matiereId),
                ]);
                
                Log::info("Created matiere: {$matiereName} (ID: {$matiereId})");
            }
        }
    }

    /**
     * Sync users from MindBridge to Central database
     */
    private function syncUsers(): void
    {
        Log::info('Syncing Users from MindBridge to Central...');

        // Get all users referenced in MindBridge etudiants table ONLY
        // Note: testCreators are not actual users, they are just IDs that don't correspond to real users
        $etudiantUsers = DB::connection('mind_bridge')
            ->table('etudiants')
            ->whereNotNull('user_id')
            ->distinct()
            ->pluck('user_id');

        Log::info('Found etudiant user IDs in MindBridge: ' . $etudiantUsers->implode(', '));

        $school = School::where('database', 'mind_bridge')->first();

        foreach ($etudiantUsers as $userId) {
            // Check if user already exists in central database
            $existingUser = User::find($userId);

            if (!$existingUser) {
                // Get etudiant data from MindBridge
                $etudiantData = DB::connection('mind_bridge')
                    ->table('etudiants')
                    ->where('user_id', $userId)
                    ->first();

                if ($etudiantData) {
                    // Create user based on etudiant data without forcing specific ID
                    $user = User::updateOrCreate(['email' => $etudiantData->email], [
                        'name' => trim(($etudiantData->first_name ?? '') . ' ' . ($etudiantData->last_name ?? '')) ?: "User {$userId}",
                        'email' => $etudiantData->email,
                        'phone' => $etudiantData->phone ?? "phone_{$userId}",
                        'password' => bcrypt('password'), // Default password
                        'avatar' => $etudiantData->avatar ?? 'users/profiles/dev-user-avatar.png',
                        'is_mind_bridge_user' => true,
                        'type' => 'etudiant',
                        'active' => true,
                        'status' => 'active',
                    ]);

                    // Update the etudiant record to use the new user ID
                    DB::connection('mind_bridge')
                        ->table('etudiants')
                        ->where('user_id', $userId)
                        ->update(['user_id' => $user->id]);

                    // Link user to school
                    if ($school) {
                        DB::connection('centrale')->table('user_school')->updateOrInsert([
                            'user_id' => $user->id,
                            'school_id' => $school->id,
                        ]);
                    }

                    Log::info("Created user from etudiant data: {$user->name} (Old ID: {$userId}, New ID: {$user->id})");
                } else {
                    Log::warning("No etudiant data found for user_id: {$userId}");
                }
            } else {
                Log::info("User already exists: {$existingUser->name} (ID: {$userId})");
            }
        }
    }

    /**
     * Sync niveau-matiere relationships
     */
    private function syncNiveauxMatiereRelationships(): void
    {
        Log::info('Syncing Niveau-Matiere relationships...');

        // Get all niveau-matiere combinations from MindBridge data
        $relationships = collect();

        // From tests table
        $testRelations = DB::connection('mind_bridge')
            ->table('tests')
            ->whereNotNull('niveau_id')
            ->whereNotNull('matiere_id')
            ->select('niveau_id', 'matiere_id')
            ->distinct()
            ->get();

        // Note: questions table doesn't have niveau_id or matier_id columns in actual database
        $questionRelations = collect();

        // From contents table
        $contentRelations = DB::connection('mind_bridge')
            ->table('contents')
            ->whereNotNull('niveau_id')
            ->whereNotNull('matiere_id')
            ->select('niveau_id', 'matiere_id')
            ->distinct()
            ->get();

        // Merge all relationships
        $allRelations = $testRelations
            ->merge($questionRelations)
            ->merge($contentRelations)
            ->unique(function ($item) {
                return $item->niveau_id . '-' . $item->matiere_id;
            });

        foreach ($allRelations as $relation) {
            // Check if relationship already exists
            $exists = DB::connection('centrale')
                ->table('niveaux_matiere')
                ->where('niveau_id', $relation->niveau_id)
                ->where('matiere_id', $relation->matiere_id)
                ->exists();

            if (!$exists) {
                DB::connection('centrale')->table('niveaux_matiere')->insert([
                    'niveau_id' => $relation->niveau_id,
                    'matiere_id' => $relation->matiere_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                Log::info("Created niveau-matiere relationship: {$relation->niveau_id}-{$relation->matiere_id}");
            }
        }
    }

    /**
     * Update MindBridge references to ensure consistency
     */
    private function updateMindBridgeReferences(): void
    {
        Log::info('Updating MindBridge references for consistency...');

        $school = School::where('database', 'mind_bridge')->first();

        if ($school) {
            // Update etudiants school_id if needed
            DB::connection('mind_bridge')
                ->table('etudiants')
                ->whereNull('school_id')
                ->update(['school_id' => $school->id]);

            Log::info('Updated etudiant school references');
        }
    }

    // Helper methods for generating default data
    private function generateNiveauName($id): string
    {
        $names = [
            1 => 'Seconde',
            2 => 'Première',
            3 => 'Terminale',
            4 => 'Licence 1',
            5 => 'Licence 2',
            6 => 'Licence 3',
            7 => 'Master 1',
            8 => 'Master 2',
        ];

        return $names[$id] ?? "Niveau {$id}";
    }

    private function generateNiveauColor($id): string
    {
        $colors = ['#1E90FF', '#32CD32', '#FF69B4', '#6A5ACD', '#20B2AA', '#DC143C', '#2E8B57', '#4B0082'];
        return $colors[($id - 1) % count($colors)];
    }

    private function generateNiveauBackground($id): string
    {
        $backgrounds = ['#B0E0E6', '#98FB98', '#FFB6C1', '#D8BFD8', '#AFEEEE', '#F08080', '#90EE90', '#9370DB'];
        return $backgrounds[($id - 1) % count($backgrounds)];
    }

    private function generateMatiereName($id): string
    {
        $names = [
            1 => 'Français',
            2 => 'Mathématiques',
            3 => 'Sciences Naturelles',
            4 => 'Finance',
            5 => 'Histoire-Géographie',
            6 => 'Anglais',
            7 => 'Physique',
            8 => 'Chimie',
        ];

        return $names[$id] ?? "Matière {$id}";
    }

    private function generateMatiereNameAr($frenchName): string
    {
        $translations = [
            'Français' => 'اللغة الفرنسية',
            'Mathématiques' => 'الرياضيات',
            'Sciences Naturelles' => 'العلوم الطبيعية',
            'Finance' => 'تمويل',
            'Histoire-Géographie' => 'التاريخ والجغرافيا',
            'Anglais' => 'اللغة الإنجليزية',
            'Physique' => 'الفيزياء',
            'Chimie' => 'الكيمياء',
        ];

        return $translations[$frenchName] ?? $frenchName;
    }

    private function generateMatiereGradient($id): string
    {
        $gradients = [
            json_encode(["0xffF9E8F3", "0xffECBADB", "0xffCB489F"]),
            json_encode(["0xffC4DBE5", "0xffA6C8D7", "0xff6BA4BD"]),
            json_encode(["0xffE0E8F0", "0xff9CAEBE", "0xff58738B"]),
            json_encode(["0xffBCC3D3", "0xff9FA5B5", "0xff464D5C"]),
        ];

        return $gradients[($id - 1) % count($gradients)];
    }

    private function generateMatiereTitleColor($id): string
    {
        $colors = ['#CB489F', '#6BA4BD', '#58738B', '#464D5C', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];
        return $colors[($id - 1) % count($colors)];
    }
}
