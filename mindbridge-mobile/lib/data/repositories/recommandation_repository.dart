import 'package:flutter/material.dart';
import 'package:mindbridge/core/models/recommandation.dart';

import '../../../core/constants/api_routes.dart';
import '../providers/remote/dio_client.dart';

class RecommandationResponse {
  final Recommandation? data;
  final bool isEmpty;
  final bool isError;

  RecommandationResponse({
    this.data,
    this.isEmpty = false,
    this.isError = false,
  });
}

class RecommandationRepository {
  final DioClient _dioClient;

  RecommandationRepository({required DioClient dioClient}) : _dioClient = dioClient;

  Future<RecommandationResponse> fetchRecommandation() async {
    try {
      final response = await _dioClient.dio.get(ApiRoutes.recommandation);

      if (response.statusCode == 200) {
        if (response.data == null ||
            response.data['test'] == null ||
            (response.data['test']['steps'] as List).isEmpty) {
          return RecommandationResponse(isEmpty: true);
        }
        return RecommandationResponse(data: Recommandation.fromJson(response.data['test']));
      }else if (response.statusCode == 404 || response.statusCode == 403) {
        return RecommandationResponse(isEmpty: true);
      }
    } catch (e) {
      print("Error fetching ApiRoutes.recommandation: $e");
      return RecommandationResponse(isError: true);
    }
    return RecommandationResponse(isError: true);
  }


  Future<Map<String, dynamic>?> submitRecommandationResult(
      Map<String, dynamic> payload) async {
    try {
      final response =
          await _dioClient.dio.post(ApiRoutes.submitRecommandationResult, data: payload);

      if (response.statusCode == 200 && response.data != null) {
        return response.data;
      }
    } catch (e) {
      debugPrint("Error submitting evaluation: $e");
    }
    return null;
  }
}
