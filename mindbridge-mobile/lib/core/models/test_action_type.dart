enum ActionTestTypes {
  microlearningSessionCompleted('microlearning_session_completed'),
  quizEasyPassed('quiz_easy_passed'),
  quizHardPassed('quiz_hard_passed'),
  weeklyTestCompleted('weekly_test_completed'),
  weeklyTestScoreOver80('weekly_test_score_over_80'),
  threeTestsSeriesCompleted('three_tests_series_completed'),
  lessonViewed('lesson_viewed'),
  mentalHealthSelfTestParticipated('mental_health_self_test_participated'),
  allQuizzesSubjectCompleted('all_quizzes_subject_completed'),
  todoComplated('todo_completed'),
  sondageCompleted('sondage_completed');
  final String value;

  const ActionTestTypes(this.value);
  static ActionTestTypes fromValue(String raw) {
    return values.firstWhere(
      (e) => e.value == raw,
      orElse: () => throw ArgumentError('Unknown action type: $raw'),
    );
  }
}
