<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Observation extends Model
{
    use HasFactory;

    protected $connection = 'mind_bridge';

    protected $table = 'observations';

    protected $fillable = [
        'label',
        'category',
        'visible_to',
        'description',
        'active',
        'test_id',
        'trigger_type',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    public function test()
    {
        return $this->belongsTo(Test::class, 'test_id');
    }
}

