# MindBridge Central Database Setup Guide

## Quick Setup (Recommended)

### Option 1: Automated Setup Script
```bash
cd mindbridge-backend
./setup_mindbridge.sh
```

This script will:
1. ✅ Check database connections
2. ✅ Setup central database tables
3. ✅ Show what data will be synchronized
4. ✅ Run data synchronization
5. ✅ Verify the setup

### Option 2: Manual Step-by-Step Setup

#### Step 1: Setup Central Database Tables
```bash
cd mindbridge-backend

# Setup central database with tables and initial data
php artisan mindbridge:setup-central-db

# Or for fresh setup (drops existing tables)
php artisan mindbridge:setup-central-db --fresh
```

#### Step 2: Check What Will Be Synchronized
```bash
# Dry run to see what data will be synchronized
php artisan mindbridge:sync-data --dry-run
```

#### Step 3: Run Data Synchronization
```bash
# Synchronize MindBridge data with central database
php artisan mindbridge:sync-data
```

## What Each Command Does

### `mindbridge:setup-central-db`
- ✅ Creates all central database tables
- ✅ Runs migrations for: users, schools, niveaux, matieres, permissions
- ✅ Seeds initial data: schools, educational levels, subjects
- ✅ Creates relationships between levels and subjects

### `mindbridge:sync-data`
- ✅ Analyzes MindBridge database for missing references
- ✅ Creates missing users in central database
- ✅ Creates missing educational levels (niveaux)
- ✅ Creates missing subjects (matieres)
- ✅ Links users to MindBridge school
- ✅ Updates all foreign key relationships

## Troubleshooting

### Error: "Table doesn't exist"
**Solution**: Run the setup command first
```bash
php artisan mindbridge:setup-central-db
```

### Error: "Connection refused"
**Problem**: Database connection issues
**Solution**: Check your `.env` file:
```env
DB_CONNECTION=centrale
DB_HOST=db
DB_DATABASE=centrale
DB_DATABASE_MINDBRIDGE=mind_bridge
DB_USERNAME=root
DB_PASSWORD=password
```

### Error: "Foreign key constraint fails"
**Problem**: Referenced records don't exist in central database
**Solution**: Run the synchronization:
```bash
php artisan mindbridge:sync-data
```

### Error: "Access denied"
**Problem**: Database permissions
**Solution**: Ensure your database user has CREATE, INSERT, UPDATE permissions

## Verification Commands

### Check Database Connections
```bash
php artisan tinker
>>> DB::connection('centrale')->select('SELECT 1');
>>> DB::connection('mind_bridge')->select('SELECT 1');
>>> exit
```

### Check Created Tables
```bash
php artisan tinker
>>> DB::connection('centrale')->select('SHOW TABLES');
>>> exit
```

### Check Synchronized Data
```bash
php artisan tinker
>>> DB::connection('centrale')->table('users')->where('is_mind_bridge_user', true)->count();
>>> DB::connection('centrale')->table('schools')->where('database', 'mind_bridge')->first();
>>> exit
```

## Expected Results

After successful setup, you should have:

### Central Database Tables:
- ✅ `users` - User accounts
- ✅ `schools` - Educational institutions  
- ✅ `niveaux` - Educational levels
- ✅ `matieres` - Subjects
- ✅ `niveaux_matiere` - Level-subject relationships
- ✅ `user_school` - User-school relationships
- ✅ `permissions` & `roles` - Access control

### Synchronized Data:
- ✅ MindBridge users created in central database
- ✅ Educational levels referenced by MindBridge
- ✅ Subjects referenced by MindBridge
- ✅ All foreign key relationships working
- ✅ MindBridge school properly configured

## Next Steps

1. **Test Login**: Try logging into MindBridge admin panel
2. **Verify Students**: Check that student data displays correctly
3. **Test Features**: Create tests, view content, check relationships
4. **Monitor Logs**: Watch for any foreign key errors

## Backup & Recovery

### Before Setup (Recommended)
```bash
# Backup existing databases
mysqldump -u root -p centrale > centrale_backup.sql
mysqldump -u root -p mind_bridge > mind_bridge_backup.sql
```

### Restore if Needed
```bash
# Restore from backup
mysql -u root -p centrale < centrale_backup.sql
mysql -u root -p mind_bridge < mind_bridge_backup.sql
```

## Support

If you encounter issues:

1. Check Laravel logs: `tail -f storage/logs/laravel.log`
2. Verify database connections
3. Ensure all migrations ran successfully
4. Check that seeders completed without errors

The setup process is designed to be safe and idempotent - you can run it multiple times without causing issues.
