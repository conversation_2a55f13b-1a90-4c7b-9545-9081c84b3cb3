import { HostListener, Injectable, inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { catchError, finalize, map, switchMap, tap } from 'rxjs/operators';
import { of } from 'rxjs';
import { AppState } from '../../../core/app.state';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { CommonService } from '../../shared/common.service';
import { SetLoading } from '../../shared/store/shared.actions';
import * as QuestionRepository from './questions-repository.actions';
import { HttpErrorResponse } from '@angular/common/http';
import { QuestionRepositoryService } from '../services/questions-repository.services';
/*
  TODO : delete all of this, move to signal store
 */
@Injectable()
export class QuestionRepositoryEffects {
  private actions$ = inject(Actions);
  store = inject<Store<AppState>>(Store);
  private questionRepositoryService = inject(QuestionRepositoryService);
  private commonService = inject(CommonService);

  isScreenSizeValid(): boolean {
    return window.innerWidth >= 600;
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isScreenSizeValid();
  }

  // tests effects
  AllTestsEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType(QuestionRepository.AllQuestionsRepository),
      switchMap((action) =>
        this.questionRepositoryService.allQuestionsRepository().pipe(
          map((questionsRepository: any) =>
            QuestionRepository.AllQuestionsRepositorySuccess({
              questionsRepository,
            })
          ),
          catchError(() =>
            of(QuestionRepository.AllQuestionsRepositoryError()).pipe(
              tap(() =>
                this.commonService.showToast('Credentials error!', 'ERROR')
              )
            )
          ),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      ),
      finalize(() => {})
    )
  );

  AddTestsEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType(QuestionRepository.AddQuestionRepository),
      switchMap((action) =>
        this.questionRepositoryService
          .addQuestionRepository(action.payload)
          .pipe(
            map((questionRepository: any) =>
              QuestionRepository.AddQuestionRepositorySuccess({
                questionRepository,
              })
            ),
            tap(() => {
              this.commonService.showToast(
                'Question ajoutée avec succès',
                'SUCCESS'
              );
              this.store.dispatch(QuestionRepository.AllQuestionsRepository());
            }),
            catchError((err: HttpErrorResponse) =>
              of(QuestionRepository.AddQuestionRepositoryError()).pipe(
                tap(() => {
                  this.commonService.showToast(err.error, 'ERROR');
                })
              )
            ),
            finalize(() => {
              this.store.dispatch(SetLoading({ isAppLoading: false }));
            })
          )
      ),
      finalize(() => {})
    )
  );

  UpdateTestsEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType(QuestionRepository.UpdateQuestionRepository),
      switchMap((action) =>
        this.questionRepositoryService
          .updateQuestionRepository(action.payload, action.id)
          .pipe(
            map((questionRepository: any) =>
              QuestionRepository.UpdateQuestionRepositorySuccess({
                questionRepository,
              })
            ),
            tap(() => {
              this.commonService.showToast(
                'Question mise à jour avec succès',
                'SUCCESS'
              );
              this.store.dispatch(QuestionRepository.AllQuestionsRepository());
            }),
            catchError((err: HttpErrorResponse) =>
              of(QuestionRepository.UpdateQuestionRepositoryError()).pipe(
                tap(() => {
                  this.commonService.showToast(err.error, 'ERROR');
                })
              )
            ),
            finalize(() => {
              this.store.dispatch(SetLoading({ isAppLoading: false }));
            })
          )
      ),
      finalize(() => {})
    )
  );

  DeleteTestsEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType(QuestionRepository.DeleteQuestionRepository),
      switchMap((action) =>
        this.questionRepositoryService.deleteQuestionRepository(action.id).pipe(
          map((message: string) =>
            QuestionRepository.DeleteQuestionRepositorySuccess({ message })
          ),
          tap((res: any) => {
            this.commonService.showToast(res.message.message, 'SUCCESS');
            this.store.dispatch(QuestionRepository.AllQuestionsRepository());
          }),
          catchError((err: HttpErrorResponse) =>
            of(QuestionRepository.DeleteQuestionRepositoryError()).pipe(
              tap(() => {
                this.commonService.showToast(err.error.error, 'ERROR');
              })
            )
          ),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      ),
      finalize(() => {})
    )
  );
}
