import {
  <PERSON>tt<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { LocalStorageService } from '../../features/shared/localstorage.service';

@Injectable()
export class TokenInterceptor implements HttpInterceptor {
  private router = inject(Router);

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    const currentUser = LocalStorageService.get('currentUser');
    let headers = req.headers;
    if (currentUser) {
      headers = headers.append(
        'Authorization',
        'Bearer ' + currentUser.token
      );
    }

    const requestWithBareer = req.clone({ headers });
    // Log out when not authorized
    return next.handle(requestWithBareer).pipe(
      catchError((error) => {
        if (error?.status === 401) {
          localStorage.clear();
          this.router.navigate(['login']);
        }
        throw error;
      })
    );
  }
}
