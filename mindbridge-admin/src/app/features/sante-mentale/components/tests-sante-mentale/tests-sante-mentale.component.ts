import {
  Component,
  Signal,
  inject,
  computed,
  OnInit,
  effect,
  untracked,
} from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../core/app.state';
import { SetLoading } from '../../../shared/store/shared.actions';
import { TestManagementStore } from '../../../test-management/test-management.store';
import { IMentalHealth } from '../../../test-management/models/test-categories-board';
import { NgClass, SlicePipe } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import { MatieresStore } from '../../../matieres/matieres.store';
import { OPTION_ALL } from '../../../shared/global.config';
import { NiveauMini } from '../../../matieres/models/matiere-response.model';
import {
  IParticipantSearchForm,
  IParticipantSearchFormGroup,
  ParticipantSearchFormGroup,
} from '../../models/particicpant-search-request';
import { SanteMentaleStore } from '../../sante-mentale.store';
import { ParticipantResponse } from '../../models/participant-response';
import { EtudiantsStore } from '../../../etudiants/etudiants.store';

@Component({
  selector: 'app-tests-sante-mentale',
  templateUrl: './tests-sante-mentale.component.html',
  styleUrl: './tests-sante-mentale.component.scss',
  imports: [ReactiveFormsModule, SlicePipe, MatIcon, NgClass],
  providers: [
    TestManagementStore,
    MatieresStore,
    SanteMentaleStore,
    EtudiantsStore,
  ],
  standalone: true,
})
export class TestsSanteMentaleComponent implements OnInit {
  private fb = inject(FormBuilder);

  form: FormGroup<IParticipantSearchFormGroup> = this.fb.group(
    new ParticipantSearchFormGroup()
  );

  private store = inject<Store<AppState>>(Store);
  #testManagementStore = inject(TestManagementStore);
  #santeMentaleStore = inject(SanteMentaleStore);
  #matieresStore = inject(MatieresStore);
  #etudiantsStore = inject(EtudiantsStore);

  testCategoriesHealthMental: Signal<IMentalHealth> = computed(
    () => this.#testManagementStore.state.testCategoriesBoard().mental_health
  );

  participantsSearchForm: Signal<IParticipantSearchForm> =
    this.#santeMentaleStore.state.participantsSearchForm;

  allParticipants: Signal<ParticipantResponse[]> =
    this.#santeMentaleStore.state.participants;

  allNiveaux: Signal<NiveauMini[]> = this.#matieresStore.state.niveaux;

  current_page: Signal<number> = this.#santeMentaleStore.state.current_page;
  total_pages: Signal<number> = computed(() =>
    Math.max(this.#santeMentaleStore.state.total_pages(), 1)
  );

  constructor() {
    effect(() => {
      const searchFormValue = this.participantsSearchForm();
      untracked(() => this.form.patchValue(searchFormValue));
    });
  }

  ngOnInit() {
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.#testManagementStore.getTestCategoriesBoard();
    this.searchParticipants();
    this.#matieresStore.getAllNiveaux();
    this.#etudiantsStore.getAllSchools();
  }

  searchParticipants() {
    this.#santeMentaleStore.setSearchForm(this.form.getRawValue());
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.#santeMentaleStore.getParticipants();
  }

  goToPage(page: number) {
    this.#santeMentaleStore.setCurrentPage(page);
    this.searchParticipants();
  }

  protected readonly OPTION_ALL = OPTION_ALL;
  protected readonly Number = Number;
}
