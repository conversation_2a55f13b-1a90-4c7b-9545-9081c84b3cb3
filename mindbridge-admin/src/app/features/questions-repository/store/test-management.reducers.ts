import { Action, createReducer, on } from '@ngrx/store';
import * as QuestionRepository from './questions-repository.actions';
/*
  TODO : delete all of this, move to signal store
 */
export interface QuestionRepositoryState {
  questionsRepository: any;
  questionRepository: any;
  message: string;
}
export const initialQuestionRepositoryState: QuestionRepositoryState = {
  questionsRepository: null,
  questionRepository: null,
  message: '',
};

const featureReducer = createReducer(
  initialQuestionRepositoryState,
  on(
    QuestionRepository.AllQuestionsRepositorySuccess,
    QuestionRepository.AddQuestionRepositorySuccess,
    QuestionRepository.UpdateQuestionRepositorySuccess,
    QuestionRepository.DeleteQuestionRepositorySuccess,
    (state, action) => ({ ...state, ...action })
  )
);

export function questionRepositoryStateReducer(
  state: QuestionRepositoryState | undefined,
  action: Action
) {
  return featureReducer(state, action);
}
