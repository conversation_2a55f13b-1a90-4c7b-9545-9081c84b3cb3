<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        \Log::info("====== UserRequest => Payload Before Validation ======\n" . json_encode($this->all(), JSON_PRETTY_PRINT));
        if ($this->avatar == 'null' || $this->avatar == null) {
            $this->request->remove('avatar');
        }
        if ($this->isUpdating()) {
            return $this->updateRules();
        }
        return $this->storeRules();
    }

    public function messages(): array
    {
        return [
            'email.unique' => trans("globalConst.auth.email_already_taken"),
            'email.required' => trans("globalConst.auth.required_email"),
            'phone.unique' => trans("globalConst.auth.phone_already_taken"),
            'phone.required' => trans("globalConst.auth.required_phone"),
            'name.required' => trans("globalConst.auth.required_name"),
            'role_id.required' => trans("globalConst.auth.required_role"),
            'avatar.image' => trans("globalConst.auth.image_avatar"),
            'avatar.mimes' => trans("globalConst.auth.mimes_avatar"),
            'avatar.max' => trans("globalConst.auth.max_avatar")
        ];
    }

    protected function updateRules(): array
    {
        return [
            'name' => ['sometimes', 'string'],
            'email' => ['sometimes', 'email'],
            'phone' => ['sometimes', 'digits:8', 'regex:/^[2-4][0-9]{7}$/'],
            'role_id' => ['sometimes', 'integer'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:2048'],
            'active' => ['sometimes'],
            'password' => ['nullable'],
        ];
    }

    protected function storeRules(): array
    {
        return [
            'name' => ['required', 'string'],
            'email' => ['required', 'email'],
            'phone' => ['required', 'digits:8', 'regex:/^[2-4][0-9]{7}$/'],
            'role_id' => ['required', 'integer'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:2048'],
            'password' => ['nullable'],
        ];
    }

    protected function isUpdating(): bool
    {
        return $this->isMethod('patch') || $this->isMethod('put') || ($this->isMethod('post') && $this->id);
    }

    protected function failedValidation(Validator $validator): void
    {
        \Log::error("====== UserRequest => Validation Errors ======\n" . json_encode($validator->errors()->toArray(), JSON_PRETTY_PRINT));
        $formattedErrors = [];
        foreach ($validator->errors()->toArray() as $field => $errors) {
            $formattedErrors[$field] = $errors[0];
        }

        throw new HttpResponseException(
            response()->json(
                $formattedErrors,
                400,
                ['Content-Type' => 'application/json;charset=UTF-8'],
                JSON_UNESCAPED_UNICODE
            )
        );
    }
}
