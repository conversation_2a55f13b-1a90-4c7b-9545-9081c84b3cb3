<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class School extends Model
{
    use HasFactory;

    protected $connection = 'centrale';
    protected $table = 'schools';
    protected $fillable = ['name', 'domain', 'database', 'organization_id', 'migrations_path'];

    public function users()
    {
        return $this->belongsToMany(User::class, 'user_school');
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function parents()
    {
        return $this->hasManyThrough(Parent::class, User::class, 'school_id', 'user_id', 'id', 'id');
    }
}
