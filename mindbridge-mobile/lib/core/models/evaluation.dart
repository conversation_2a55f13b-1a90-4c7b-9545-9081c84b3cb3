// To parse this JSON data, do
//
//     final evaluation = evaluationFromJson(jsonString);

import 'dart:convert';

Evaluation evaluationFromJson(String str) => Evaluation.fromJson(json.decode(str));

String evaluationToJson(Evaluation data) => json.encode(data.toJson());

class Evaluation {
    int? id;
    String? title;
    dynamic description;
    dynamic target;
    dynamic type;
    dynamic timer;
    DateTime? challengeDateStart;
    DateTime? challengeDateEnd;
    dynamic difficultyLevel;
    int? matiereId;
    int? categoryId;
    int? createdBy;
    int? niveauId;
    dynamic contentId;
    DateTime? createdAt;
    DateTime? updatedAt;
    dynamic content;
    List<Step>? steps;
    Matiere? matiere;

    Evaluation({
        this.id,
        this.title,
        this.description,
        this.target,
        this.type,
        this.timer,
        this.challengeDateStart,
        this.challengeDateEnd,
        this.difficultyLevel,
        this.matiereId,
        this.categoryId,
        this.createdBy,
        this.niveauId,
        this.contentId,
        this.createdAt,
        this.updatedAt,
        this.content,
        this.steps,
        this.matiere,
    });

    factory Evaluation.fromJson(Map<String, dynamic> json) => Evaluation(
        id: json["id"],
        title: json["title"],
        description: json["description"],
        target: json["target"],
        type: json["type"],
        timer: json["timer"],
        challengeDateStart: json["challenge_date_start"] == null ? null : DateTime.parse(json["challenge_date_start"]),
        challengeDateEnd: json["challenge_date_end"] == null ? null : DateTime.parse(json["challenge_date_end"]),
        difficultyLevel: json["difficulty_level"],
        matiereId: json["matiere_id"],
        categoryId: json["category_id"],
        createdBy: json["created_by"],
        niveauId: json["niveau_id"],
        contentId: json["content_id"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        content: json["content"],
        steps: json["steps"] == null ? [] : List<Step>.from(json["steps"]!.map((x) => Step.fromJson(x))),
        matiere: json["matiere"] == null ? null : Matiere.fromJson(json["matiere"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "target": target,
        "type": type,
        "timer": timer,
        "challenge_date_start": "${challengeDateStart!.year.toString().padLeft(4, '0')}-${challengeDateStart!.month.toString().padLeft(2, '0')}-${challengeDateStart!.day.toString().padLeft(2, '0')}",
        "challenge_date_end": "${challengeDateEnd!.year.toString().padLeft(4, '0')}-${challengeDateEnd!.month.toString().padLeft(2, '0')}-${challengeDateEnd!.day.toString().padLeft(2, '0')}",
        "difficulty_level": difficultyLevel,
        "matiere_id": matiereId,
        "category_id": categoryId,
        "created_by": createdBy,
        "niveau_id": niveauId,
        "content_id": contentId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "content": content,
        "steps": steps == null ? [] : List<dynamic>.from(steps!.map((x) => x.toJson())),
        "matiere": matiere?.toJson(),
    };
}

class Matiere {
    int? id;
    String? nameFr;
    String? nameAr;
    String? titleColor;
    dynamic description;
    String? imageUrl;
    String? gradientBackground;
    String? gradientBorder;
    dynamic createdAt;
    dynamic updatedAt;
    dynamic deletedAt;

    Matiere({
        this.id,
        this.nameFr,
        this.nameAr,
        this.titleColor,
        this.description,
        this.imageUrl,
        this.gradientBackground,
        this.gradientBorder,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
    });

    factory Matiere.fromJson(Map<String, dynamic> json) => Matiere(
        id: json["id"],
        nameFr: json["name_fr"],
        nameAr: json["name_ar"],
        titleColor: json["title_color"],
        description: json["description"],
        imageUrl: json["image_url"],
        gradientBackground: json["gradient_background"],
        gradientBorder: json["gradient_border"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        deletedAt: json["deleted_at"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name_fr": nameFr,
        "name_ar": nameAr,
        "title_color": titleColor,
        "description": description,
        "image_url": imageUrl,
        "gradient_background": gradientBackground,
        "gradient_border": gradientBorder,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "deleted_at": deletedAt,
    };
}

class Step {
    int? id;
    int? testId;
    int? questionId;
    bool? required;
    String? type;
    dynamic condition;
    int? order;
    DateTime? createdAt;
    DateTime? updatedAt;
    Question? question;

    Step({
        this.id,
        this.testId,
        this.questionId,
        this.required,
        this.type,
        this.condition,
        this.order,
        this.createdAt,
        this.updatedAt,
        this.question,
    });

    factory Step.fromJson(Map<String, dynamic> json) => Step(
        id: json["id"],
        testId: json["test_id"],
        questionId: json["question_id"],
        required: json["required"],
        type: json["type"],
        condition: json["condition"],
        order: json["order"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        question: json["question"] == null ? null : Question.fromJson(json["question"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "test_id": testId,
        "question_id": questionId,
        "required": required,
        "type": type,
        "condition": condition,
        "order": order,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "question": question?.toJson(),
    };
}

class Question {
    int? id;
    String? type;
    String? content;
    dynamic description;
    dynamic isTrue;
    dynamic isFalse;
    dynamic answer;
    bool? isRequired;
    dynamic imagePath;
    DateTime? createdAt;
    DateTime? updatedAt;
    List<Option>? options;

    Question({
        this.id,
        this.type,
        this.content,
        this.description,
        this.isTrue,
        this.isFalse,
        this.answer,
        this.isRequired,
        this.imagePath,
        this.createdAt,
        this.updatedAt,
        this.options,
    });

    factory Question.fromJson(Map<String, dynamic> json) => Question(
        id: json["id"],
        type: json["type"],
        content: json["content"],
        description: json["description"],
        isTrue: json["is_true"],
        isFalse: json["is_false"],
        answer: json["answer"],
        isRequired: json["is_required"],
        imagePath: json["image_path"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        options: json["options"] == null ? [] : List<Option>.from(json["options"]!.map((x) => Option.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "type": type,
        "content": content,
        "description": description,
        "is_true": isTrue,
        "is_false": isFalse,
        "answer": answer,
        "is_required": isRequired,
        "image_path": imagePath,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "options": options == null ? [] : List<dynamic>.from(options!.map((x) => x.toJson())),
    };
}

class Option {
    int? id;
    String? name;
    String? icon;
    int? isCorrect;
    int? questionId;
    DateTime? createdAt;
    DateTime? updatedAt;

    Option({
        this.id,
        this.name,
        this.icon,
        this.isCorrect,
        this.questionId,
        this.createdAt,
        this.updatedAt,
    });

    factory Option.fromJson(Map<String, dynamic> json) => Option(
        id: json["id"],
        name: json["name"],
        icon: json["icon"],
        isCorrect: json["isCorrect"],
        questionId: json["question_id"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "icon": icon,
        "isCorrect": isCorrect,
        "question_id": questionId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
    };
}
