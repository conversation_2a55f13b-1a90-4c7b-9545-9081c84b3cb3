version: '3'
services:
  mindbridge_api:
    build: .
    container_name: mindbridge_api
    ports:
      - "7562:80"
    volumes:
      - ./app:/var/www/html/app/
      - ./storage:/var/www/html/storage/
      - ./database:/var/www/html/database/
      - ./routes:/var/www/html/routes/
      - ./resources:/var/www/html/resources/
    restart: always

  db:
    image: mysql:8.0.27
    platform: linux/amd64
    command: --default-authentication-plugin=mysql_native_password --init-file /data/application/init.sql
    restart: always
    volumes:
      - mindbridge_db_volume:/var/lib/mysql
      - ./mysql-initdb.d:/data/application
    environment:
      MYSQL_DATABASE: mind_bridge
      MYSQL_ROOT_PASSWORD: password
      UPLOAD_LIMIT: 1G
      MEMORY_LIMIT: 3000M
  phpmyadmin:
    image: phpmyadmin
    restart: always
    ports:
      - 8080:80

volumes:
  mindbridge_db_volume:
