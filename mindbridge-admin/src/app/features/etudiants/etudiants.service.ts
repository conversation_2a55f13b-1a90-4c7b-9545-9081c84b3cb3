import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { getQuery, Pagination } from '../shared/helpers/query.helper';
import { Observable } from 'rxjs';
import { ApiResponse } from '../../core/models/api-response';
import { environment } from '../../../environments/environment';
import { IEtudiantSearchForm } from './models/etudiant-search-request';
import { EtudiantResponse } from './models/etudiant-response';
import { MatiereResponse } from '../matieres/models/matiere-response.model';
import { SchoolResponse } from './models/school-response';

@Injectable({
  providedIn: 'root',
})
export class EtudiantsService {
  private http = inject(HttpClient);

  getListeEtudiants(
    searchForm: IEtudiantSearchForm,
    pagination: Pagination
  ): Observable<ApiResponse<EtudiantResponse>> {
    const params = getQuery(searchForm, pagination);

    return this.http.get<ApiResponse<EtudiantResponse>>(
      environment.BASE_URL_API + 'mind_bridge/etudiants/list',
      { params }
    );
  }

  getListeSchools(): Observable<ApiResponse<SchoolResponse>> {
    return this.http.get<ApiResponse<SchoolResponse>>(
      environment.BASE_URL_API + 'mind_bridge/school/list'
    );
  }

  detailsEtudiant(id: number): Observable<EtudiantResponse> {
    return this.http.get<EtudiantResponse>(
      environment.BASE_URL_API + 'mind_bridge/etudiant/detail/' + id
    );
  }
}
