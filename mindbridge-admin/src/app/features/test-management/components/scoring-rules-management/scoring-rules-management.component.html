<div class="scoring-management-container">
  <div class="page-header">
    <h1>Gestion des Règles de Notation</h1>
    <p class="subtitle">Configurez les seuils de notation et les recommandations pour chaque test</p>
  </div>

  <div class="test-selection-card">
    <h2>Sélectionner un Test</h2>
    <form [formGroup]="testFilterForm" class="filter-form">
      <mat-form-field class="full-width">
        <mat-label>Choisir un test</mat-label>
        <mat-select formControlName="test_id" (selectionChange)="onTestSelected()">
          <mat-option *ngFor="let test of tests" [value]="test.id">
            {{ test.title }} ({{ test.category?.name }})
          </mat-option>
        </mat-select>
      </mat-form-field>
    </form>

    @if (isLoadingTests) {
      <p class="loading">Chargement des tests...</p>
    }

    <!-- Pagination for tests -->
    @if (tests.length > 0) {
      <mat-paginator
        [length]="totalTests"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 25]"
        (page)="onPageChange($event)"
      ></mat-paginator>
    }
  </div>

  <!-- Scoring Rules Section -->
  @if (selectedTest) {
    <div class="scoring-rules-card">
      <div class="card-header">
        <div>
          <h2>Règles de Notation</h2>
          <p class="test-info">Test: <strong>{{ selectedTest.title }}</strong></p>
        </div>
        <button
          type="button"
          class="btn btn-primary"
          (click)="openForm()"
          [disabled]="showForm"
        >
          <mat-icon>add</mat-icon>
          Ajouter une Règle
        </button>
      </div>

      <!-- Form Section -->
      @if (showForm) {
        <div class="scoring-rule-form">
          <h3>{{ editingRuleId ? 'Modifier' : 'Ajouter' }} une Règle de Notation</h3>
          <form [formGroup]="scoringRuleForm" class="form-grid">
            <mat-form-field class="full-width">
              <mat-label>Pourcentage Minimum (%)</mat-label>
              <input
                matInput
                type="number"
                formControlName="min_percentage"
                min="0"
                max="100"
              />
              @if (scoringRuleForm.get('min_percentage')?.hasError('required')) {
                <mat-error>Ce champ est requis</mat-error>
              }
            </mat-form-field>

            <mat-form-field class="full-width">
              <mat-label>Pourcentage Maximum (%)</mat-label>
              <input
                matInput
                type="number"
                formControlName="max_percentage"
                min="0"
                max="100"
              />
              @if (scoringRuleForm.get('max_percentage')?.hasError('required')) {
                <mat-error>Ce champ est requis</mat-error>
              }
            </mat-form-field>

            <mat-form-field class="full-width">
              <mat-label>Interprétation</mat-label>
              <input
                matInput
                type="text"
                formControlName="interpretation"
                placeholder="Ex: Excellent, Bon, Moyen, Faible"
              />
              @if (scoringRuleForm.get('interpretation')?.hasError('required')) {
                <mat-error>Ce champ est requis</mat-error>
              }
            </mat-form-field>

            <mat-form-field class="full-width">
              <mat-label>Feedback (Optionnel)</mat-label>
              <textarea
                matInput
                formControlName="feedback"
                rows="3"
                placeholder="Message de feedback pour l'étudiant"
              ></textarea>
            </mat-form-field>

            <mat-form-field class="full-width">
              <mat-label>Recommandation (Optionnel)</mat-label>
              <textarea
                matInput
                formControlName="recommendation"
                rows="3"
                placeholder="Recommandation professionnelle"
              ></textarea>
            </mat-form-field>

            <div class="form-actions">
              <button
                type="button"
                class="btn btn-secondary"
                (click)="closeForm()"
              >
                Annuler
              </button>
              <button
                type="button"
                class="btn btn-primary"
                (click)="saveRule()"
                [disabled]="scoringRuleForm.invalid"
              >
                {{ editingRuleId ? 'Mettre à jour' : 'Créer' }}
              </button>
            </div>
          </form>
        </div>
      }

      <!-- Rules Table -->
      <div class="scoring-rules-table">
        @if (isLoadingRules) {
          <p class="loading">Chargement des règles...</p>
        } @else if (scoringRules.length === 0) {
          <p class="no-data">Aucune règle de notation définie pour ce test</p>
        } @else {
          <table mat-table [dataSource]="scoringRules" class="rules-table">
            <!-- Min Percentage Column -->
            <ng-container matColumnDef="min_percentage">
              <th mat-header-cell *matHeaderCellDef>Min %</th>
              <td mat-cell *matCellDef="let element">{{ element.min_percentage }}%</td>
            </ng-container>

            <!-- Max Percentage Column -->
            <ng-container matColumnDef="max_percentage">
              <th mat-header-cell *matHeaderCellDef>Max %</th>
              <td mat-cell *matCellDef="let element">{{ element.max_percentage }}%</td>
            </ng-container>

            <!-- Interpretation Column -->
            <ng-container matColumnDef="interpretation">
              <th mat-header-cell *matHeaderCellDef>Interprétation</th>
              <td mat-cell *matCellDef="let element">{{ element.interpretation }}</td>
            </ng-container>

            <!-- Feedback Column -->
            <ng-container matColumnDef="feedback">
              <th mat-header-cell *matHeaderCellDef>Feedback</th>
              <td mat-cell *matCellDef="let element">
                {{ element.feedback ? (element.feedback | slice: 0:50) + '...' : '-' }}
              </td>
            </ng-container>

            <!-- Recommendation Column -->
            <ng-container matColumnDef="recommendation">
              <th mat-header-cell *matHeaderCellDef>Recommandation</th>
              <td mat-cell *matCellDef="let element">
                {{ element.recommendation ? (element.recommendation | slice: 0:50) + '...' : '-' }}
              </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let element">
                <button
                  type="button"
                  class="btn-icon btn-edit"
                  (click)="openForm(element)"
                  title="Modifier"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                <button
                  type="button"
                  class="btn-icon btn-delete"
                  (click)="deleteRule(element.id)"
                  title="Supprimer"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        }
      </div>
    </div>
  } @else {
    <div class="empty-state">
      <mat-icon>assignment</mat-icon>
      <p>Sélectionnez un test pour gérer ses règles de notation</p>
    </div>
  }
</div>

