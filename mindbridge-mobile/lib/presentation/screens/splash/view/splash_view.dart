import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/routes.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/utilities/app_images.dart';
import '../../../widgets/flat_button.dart';
import '../../../widgets/global_spots.dart';
import '../../../widgets/gradient_button.dart';

class SplashView extends StatelessWidget {
  final PageController pageController = PageController();

  SplashView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Stack(
        children: [
          const GlobalSpots(),
          PageView(
            controller: pageController,
            children: [
              _buildSplashPage(
                image: AppImages.owlWithBooks,
                title: AppStrings.splashTitle1,
                subtitle: AppStrings.splashSubtitle1,
                buttonText: AppStrings.splashNext,
                isLastPage: false,
                pageIndex: 0,
              ),
              _buildSplashPage(
                image: AppImages.owlWithChalkboard,
                title: AppStrings.splashTitle2,
                subtitle: AppStrings.splashSubtitle2,
                buttonText: AppStrings.splashNext,
                isLastPage: false,
                pageIndex: 1,
              ),
              _buildSplashPage(
                image: AppImages.owlWithBook,
                title: AppStrings.splashTitle3,
                subtitle: AppStrings.splashSubtitle3,
                buttonText: AppStrings.splashTest,
                isLastPage: true,
                pageIndex: 2,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSplashPage({
    required String image,
    required String title,
    required String subtitle,
    required String buttonText,
    required bool isLastPage,
    required int pageIndex,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 5.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            image,
            height: 40.h,
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.black,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 2.h),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.black.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              for (int i = 0; i < 3; i++)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 0.5.w),
                  child: i == pageIndex
                      ? Container(
                          width: 5.w,
                          height: 1.h,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        )
                      : Icon(
                          Icons.circle,
                          size: 1.5.h,
                          color: AppColors.primary.withOpacity(0.3),
                        ),
                ),
            ],
          ),
          SizedBox(height: 4.h),
          Row(
            children: [
              Expanded(
                child: FlatButtonGlobal(
                  text: AppStrings.splashSkip,
                  onPressed: () {
                    if (pageController.hasClients) {
                      pageController.jumpToPage(2);
                    }
                  },
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: GradientButton(
                  text: buttonText,
                  onPressed: () {
                    if (!isLastPage) {
                      if (pageController.hasClients) {
                        pageController.nextPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      }
                    } else {
                      Get.toNamed(Routes.completeProfile);
                    }
                  },
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
