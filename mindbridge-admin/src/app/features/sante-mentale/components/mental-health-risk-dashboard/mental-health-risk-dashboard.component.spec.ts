import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { signal } from '@angular/core';

import { MentalHealthRiskDashboardComponent } from './mental-health-risk-dashboard.component';
import { SanteMentaleStore } from '../../sante-mentale.store';
import { 
  MentalHealthRiskDashboardData, 
  DashboardFiltersForm 
} from '../../models/mental-health-risk-dashboard';

// Mock data for testing
const mockDashboardData: MentalHealthRiskDashboardData = {
  summary: {
    total_at_risk_students: 15,
    average_score: 45.2,
    total_tests_completed: 120,
    students_needing_attention: 8,
    risk_distribution: {
      high_risk: 8,
      medium_risk: 4,
      low_risk: 3
    }
  },
  students: [
    {
      etudiant_id: 1,
      student_name: 'Test Student',
      niveau: 'Terminale',
      test_count: 3,
      average_score: 25.5,
      lowest_score: 15.0,
      risk_level: 'high',
      last_test_date: '2024-01-15',
      trigger_info: {
        parent_triggers: 1,
        teacher_triggers: 1,
        grade_triggers: 1
      }
    }
  ],
  charts: {
    students_by_niveau: [
      {
        niveau: 'Terminale',
        high_risk: 5,
        medium_risk: 2,
        low_risk: 1
      }
    ],
    tests_by_category: [
      {
        category: 'Anxiété',
        count: 45
      }
    ],
    trigger_distribution: [
      {
        trigger_type: 'parent',
        count: 30
      }
    ],
    score_trends: [
      {
        month: 'Jan 2024',
        average_score: 45.2,
        test_count: 25
      }
    ]
  }
};

// Mock store
const mockStore = {
  state: {
    dashboardData: signal(mockDashboardData),
    dashboardLoading: signal(false),
    dashboardError: signal(null),
    dashboardFilters: signal(new DashboardFiltersForm())
  },
  getDashboardData: jasmine.createSpy('getDashboardData'),
  setDashboardFilters: jasmine.createSpy('setDashboardFilters')
};

describe('MentalHealthRiskDashboardComponent', () => {
  let component: MentalHealthRiskDashboardComponent;
  let fixture: ComponentFixture<MentalHealthRiskDashboardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        MentalHealthRiskDashboardComponent,
        NoopAnimationsModule
      ],
      providers: [
        provideRouter([]),
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: SanteMentaleStore, useValue: mockStore }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(MentalHealthRiskDashboardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display summary cards with correct data', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    
    // Check if summary cards are displayed
    const summaryCards = compiled.querySelectorAll('.summary-card');
    expect(summaryCards.length).toBe(4);
    
    // Check if the total at-risk students is displayed
    expect(compiled.textContent).toContain('15');
    expect(compiled.textContent).toContain('45.2%');
    expect(compiled.textContent).toContain('120');
    expect(compiled.textContent).toContain('8');
  });

  it('should display risk distribution cards', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    
    // Check if risk cards are displayed
    const riskCards = compiled.querySelectorAll('.risk-card');
    expect(riskCards.length).toBe(3);
    
    // Check if risk numbers are displayed
    expect(compiled.textContent).toContain('Risque Élevé');
    expect(compiled.textContent).toContain('Risque Moyen');
    expect(compiled.textContent).toContain('Risque Faible');
  });

  it('should display student table with data', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    
    // Check if table is displayed
    const table = compiled.querySelector('table');
    expect(table).toBeTruthy();
    
    // Check if student data is displayed
    expect(compiled.textContent).toContain('Test Student');
    expect(compiled.textContent).toContain('Terminale');
  });

  it('should call getDashboardData on init', () => {
    expect(mockStore.getDashboardData).toHaveBeenCalled();
  });

  it('should handle risk filter changes', () => {
    component.onRiskFilterChange('high');
    expect(component.selectedRiskFilter()).toBe('high');
    
    component.onRiskFilterChange(undefined);
    expect(component.selectedRiskFilter()).toBeUndefined();
  });

  it('should format dates correctly', () => {
    const formattedDate = component.formatDate('2024-01-15');
    expect(formattedDate).toBe('15/01/2024');
  });

  it('should return correct risk level classes', () => {
    expect(component.getRiskLevelClass('high')).toBe('risk-high');
    expect(component.getRiskLevelClass('medium')).toBe('risk-medium');
    expect(component.getRiskLevelClass('low')).toBe('risk-low');
    expect(component.getRiskLevelClass('unknown')).toBe('');
  });

  it('should refresh data when refresh button is clicked', () => {
    const refreshButton = fixture.nativeElement.querySelector('.refresh-btn');
    refreshButton?.click();
    
    expect(mockStore.getDashboardData).toHaveBeenCalledTimes(2); // Once on init, once on refresh
  });

  it('should display loading state', () => {
    // Update mock to show loading state
    mockStore.state.dashboardLoading = signal(true);
    mockStore.state.dashboardData = signal(null);
    
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent).toContain('Chargement des données...');
  });

  it('should display error state', () => {
    // Update mock to show error state
    mockStore.state.dashboardLoading = signal(false);
    mockStore.state.dashboardError = signal('Test error message');
    mockStore.state.dashboardData = signal(null);
    
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent).toContain('Erreur de chargement');
    expect(compiled.textContent).toContain('Test error message');
  });
});
