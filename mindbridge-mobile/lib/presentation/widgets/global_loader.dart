import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:sizer/sizer.dart';

class GlobalLoader extends StatelessWidget {
  const GlobalLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 40.w,
        height: 40.w,
        child: Lottie.asset(
          'assets/json/loader.json',
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
