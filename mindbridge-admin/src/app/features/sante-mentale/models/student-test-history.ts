import { MentalHealthTestsCategory } from './participant-response';

export interface StudentTestHistory {
  id: number;
  test: TestHistoryDetail;
  status: string;
  score: number;
  started_at: string;
  completed_at: string;
  answers: TestAnswer[];
  scoring: TestScoring;
  observations: TestObservation[];
}

export interface TestHistoryDetail {
  id: number;
  title: string;
  description: string;
  type: string;
  category: MentalHealthTestsCategory;
  timer: number;
  difficulty_level: string;
}

export interface TestAnswer {
  id: number;
  test_id: number;
  etudiant_id: number;
  question_id: number;
  selected_options: any[];
  comment: string;
  score: number;
  question: QuestionDetail;
}

export interface QuestionDetail {
  id: number;
  type: string;
  content: string;
  description: string;
  is_required: boolean;
  image_path: string;
  options: OptionDetail[];
}

export interface OptionDetail {
  id: number;
  question_id: number;
  option_text: string;
  is_correct: boolean;
}

export interface TestScoring {
  total_questions: number;
  score: number;
  percentage: number;
  interpretation: string;
  feedback: string;
  recommendation: string;
}

export interface TestObservation {
  id: number;
  label: string;
  category: string;
  visible_to: 'teacher' | 'parent' | 'both';
  description: string;
  active: boolean;
  test_id: number;
  trigger_type: 'parent' | 'teacher' | 'simulated_grade_shutdown';
}

export interface StudentTestHistoryResponse {
  success: boolean;
  data: StudentTestHistory[];
  total_tests: number;
}

