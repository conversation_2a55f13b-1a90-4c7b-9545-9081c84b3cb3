<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Option extends Model
{
    use HasFactory;
    protected $connection = 'mind_bridge';
    protected $table = 'options';
    
    protected $fillable = [
        'name',
        'icon',
        "isCorrect",
        'question_id',
    ];


    public function question()
    {
        return $this->belongsTo(Question::class);
    }
}
