<div class="bg-gray-50 px-6 pb-4 pt-5">
  <div
    class="flex-center grid w-full grid-cols-4 justify-start gap-4 max-md:grid-cols-2 max-md:gap-2"
  >
    @for (
      category of testCategoriesHealthMental().children | slice: 0 : 4;
      track category.id
    ) {
      <div
        class="flex h-20 w-full items-center justify-start rounded-lg border border-gray-200 bg-white max-md:h-16"
      >
        <img
          class="mx-3 h-12 w-12 rounded-full object-cover max-md:mx-2 max-md:h-8 max-md:w-8"
          [src]="category.image_url"
          alt=""
        />
        <div class="ml-0 mr-3 flex flex-col items-start justify-start gap-y-0">
          <p class="text-lg font-semibold text-[#475467] max-md:text-sm">
            {{ category.count }}
          </p>
          <p class="font-medium text-[#475467] max-md:text-xs">
            {{ category.name }}
          </p>
        </div>
      </div>
    }
  </div>

  <div
    class="flex-center mt-4 grid w-full grid-cols-4 justify-start gap-4 max-md:mt-2 max-md:grid-cols-2 max-md:gap-2"
  >
    @for (
      category of testCategoriesHealthMental().children | slice: 4 : 8;
      track category.id
    ) {
      <div
        class="flex h-20 w-full items-center justify-start rounded-lg border border-gray-200 bg-white max-md:h-16"
      >
        <img
          class="mx-3 h-12 w-12 rounded-full object-cover max-md:mx-2 max-md:h-8 max-md:w-8"
          [src]="category.image_url"
          alt=""
        />
        <div class="ml-0 mr-3 flex flex-col items-start justify-start gap-y-0">
          <p class="text-lg font-semibold text-[#475467] max-md:text-sm">
            {{ category.count }}
          </p>
          <p class="font-medium text-[#475467] max-md:text-xs">
            {{ category.name }}
          </p>
        </div>
      </div>
    }
  </div>
  @if (testCategoriesHealthMental().children.length > 8) {
    <div
      class="flex-center mt-4 grid w-full grid-cols-4 justify-start gap-4 max-md:mt-2 max-md:grid-cols-2 max-md:gap-2"
    >
      @for (
        category of testCategoriesHealthMental().children | slice: 8 : 12;
        track category.id
      ) {
        <div
          class="flex h-20 w-full items-center justify-start rounded-lg border border-gray-200 bg-white max-md:h-16"
        >
          <img
            class="mx-3 h-12 w-12 rounded-full object-cover max-md:mx-2 max-md:h-8 max-md:w-8"
            [src]="category.image_url"
            alt=""
          />
          <div
            class="ml-0 mr-3 flex flex-col items-start justify-start gap-y-0"
          >
            <p class="text-lg font-semibold text-[#475467] max-md:text-sm">
              {{ category.count }}
            </p>
            <p class="font-medium text-[#475467] max-md:text-xs">
              {{ category.name }}
            </p>
          </div>
        </div>
      }
    </div>
  }

  <div class="mt-4 flex flex-col items-center">
    <form
      class="flex w-full flex-col justify-end md:flex-row"
      [formGroup]="form"
      (ngSubmit)="searchParticipants()"
    >
      <h2 class="flex-1 text-left">Tests</h2>
      <div
        class="my-4 flex flex-col items-end justify-end max-md:items-center max-md:justify-center max-md:gap-2 md:flex-row"
      >
        <div
          class="mr-2 flex h-10 flex-row items-center justify-center rounded-lg border-2 border-gray-200 bg-white p-2"
        >
          <mat-icon class="mb-1.5 text-2xl">search</mat-icon>
          <input
            class="w-full"
            type="text"
            placeholder="Nom de l'étudiant(e)"
            formControlName="etudiant"
            matTooltip="Search with name of Etudiant"
            (change)="searchParticipants()"
          />
        </div>
        <div class="mr-2 rounded-lg border-2 border-gray-200 bg-white p-2">
          <select
            class="mr-8 w-full"
            formControlName="category_id"
            (change)="searchParticipants()"
          >
            <option [value]="OPTION_ALL" selected>Toutes les catégories</option>
            @for (
              category of testCategoriesHealthMental().children;
              track category.id
            ) {
              <option [value]="category.id">{{ category.name }}</option>
            }
          </select>
        </div>
        <div class="mr-2 rounded-lg border-2 border-gray-200 bg-white p-2">
          <select
            class="mr-8 w-full"
            formControlName="niveau"
            (change)="searchParticipants()"
          >
            <option [value]="OPTION_ALL" selected>Tous les niveaux</option>
            @for (niveau of allNiveaux(); track niveau.id) {
              <option [value]="niveau.id">{{ niveau.name }}</option>
            }
          </select>
        </div>
      </div>
    </form>
    <div class="container mx-auto mt-2">
      <div class="overflow-x-auto rounded-lg border shadow-md">
        <table class="min-w-full divide-y divide-gray-200 bg-white">
          <tr class="flex w-full items-start justify-start text-left">
            <th class="w-52 px-2 py-3 text-sm font-medium text-[#475467]">
              Titre
            </th>
            <th class="w-20 px-2 py-3 text-sm font-medium text-[#475467]">
              Niveau
            </th>
            <th class="w-44 px-2 py-3 text-sm font-medium text-[#475467]">
              Etudiant
            </th>
            <th class="w-20 px-2 py-3 text-sm font-medium text-[#475467]">
              Score
            </th>
            <th class="w-48 px-2 py-3 text-sm font-medium text-[#475467]">
              Remarques
            </th>
            <th class="w-28 px-2 py-3 text-sm font-medium text-[#475467]">
              Statut
            </th>
            <th class="w-36 px-2 py-3 text-sm font-medium text-[#475467]">
              Catégorie
            </th>
            <th
              class="w-16 flex-1 py-3 pr-6 text-right text-sm font-medium text-[#475467]"
            ></th>
          </tr>
          @for (participant of allParticipants(); track participant.id) {
            <tr class="flex w-full items-center justify-start">
              <td class="w-52 px-2 py-3 font-normal">
                {{ participant.test_title }}
              </td>
              <td class="w-20 px-2 py-3 font-normal text-[#475467]">
                {{ participant.etudiant.niveau.name }}
              </td>
              <td
                class="flex w-44 items-center justify-start py-3 pl-2 font-normal"
              >
                <img
                  class="mr-1 h-12 w-12 rounded-full object-cover"
                  [src]="
                    participant.etudiant.avatar ||
                    'assets/icones/etudiant-avatar.png'
                  "
                />
                <span class="mx-1">
                  {{ participant.etudiant.first_name }}
                  {{ participant.etudiant.last_name }}
                </span>
              </td>
              <td class="w-20 px-2 py-3 font-normal text-[#475467]">
                {{ participant.etudiant.scores }}/20
              </td>
              <td class="w-48 px-2 py-3 font-normal text-[#475467]">
                {{ participant.comment }}
              </td>
              <td class="w-28 px-2 py-3 font-normal">
                <span
                  class="rounded-lg px-2 py-1 text-xs font-medium"
                  [ngClass]="{
                    'bg-[#ECFDF3] text-[#027A48]':
                      participant.test_status === 'TERMINE',
                    'bg-[#FFFAEB] text-[#B54708]':
                      participant.test_status === 'ABANDON',
                    'bg-[#F5F5F5] text-[#666666]':
                      participant.test_status === 'EN COURS',
                  }"
                >
                  {{ participant.test_status }}
                </span>
              </td>
              <td class="w-36 px-2 py-3 font-normal text-[#475467]">
                {{ participant.category.name }}
              </td>
              <td
                class="flex w-16 flex-1 items-end justify-end gap-2 py-3 pr-4 text-right font-normal"
              >
                <button>
                  <img src="assets/icones/visibility.png" alt="" />
                </button>
                <button>
                  <img src="assets/icones/doc.png" alt="" />
                </button>
              </td>
            </tr>
          }
        </table>
        <hr />
        <div class="flex items-start justify-start gap-2 bg-white px-4 py-4">
          <div class="rounded-lg border border-gray-300 bg-white p-1.5">
            <button
              class="w-16 text-center font-medium"
              [disabled]="current_page() == 1"
              (click)="goToPage(Number(current_page()) - 1)"
            >
              Précédent
            </button>
          </div>
          <div class="rounded-lg border border-gray-300 bg-white p-1.5">
            <button
              class="w-14 text-center font-medium"
              [disabled]="current_page() == total_pages()"
              (click)="goToPage(Number(current_page()) + 1)"
            >
              Suivant
            </button>
          </div>
          <p class="flex-1 p-2 text-right font-medium">
            Page {{ current_page() }} de {{ total_pages() }}
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
