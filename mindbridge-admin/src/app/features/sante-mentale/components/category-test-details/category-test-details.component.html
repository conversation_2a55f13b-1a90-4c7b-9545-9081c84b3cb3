<div class="h-full w-full px-2 py-4">
  <div class="pb-4 text-base font-semibold">{{ test?.title }}</div>

  <div class="py-2 text-base">Description</div>
  <p class="text-[#475467]">{{ test?.description }}</p>

  <div class="pb-4 pt-8 text-base">Questions</div>
  @for (step of test?.steps; track step.id) {
    <div class="mb-4 w-full rounded-lg border border-[#F2F4F7] p-4">
      <div class="tex-[#344054] font-semibold">{{ step.question.content }}</div>
      <p class="pt-3">{{ step?.question?.description }}</p>

      <div class="flex flex-col gap-2 pt-2">
        @for (option of step.question.options; track option.id) {
          <div class="flex gap-2">
            <span class="pt-1">
              {{ option.icon !== null ? getIconTransformed(option.icon) : '-' }}
            </span>
            <p class="pt-1">{{ option.name }}</p>
            @if (option.isCorrect) {
              <div
                class="mt-1 flex rounded-lg bg-[#ECFDF3] pl-1 pr-2 text-[#027A48]"
              >
                <mat-icon class="text-sm">check</mat-icon>
                <span>Réponse correcte</span>
              </div>
            }
          </div>
        }
      </div>
    </div>
  }
</div>
