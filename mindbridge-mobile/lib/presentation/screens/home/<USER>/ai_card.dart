import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:mindbridge/app/theme/colors.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/constants/app_strings.dart';
import '../../../../core/utilities/app_images.dart';
import '../../../widgets/flat_button.dart';
import '../../../widgets/global_dialog.dart';
import '../../../widgets/gradient_button.dart';
import '../controller/home_controller.dart';

class AiCard extends StatelessWidget {
  final HomeController homeController = Get.find<HomeController>();

  AiCard({super.key});
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        border: GradientBoxBorder(
          gradient: AppColors.borderAiGradient,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Container(
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          gradient: AppColors.backgroundAiGradient,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Obx(() {
          final isFileUploaded = homeController.isFileUploaded.value;
          final selectedFile = homeController.selectedFile.value;
          final selectedButton = homeController.selectedAiButton.value;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              IconButton(
                padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                constraints: const BoxConstraints(),
                style: const ButtonStyle(
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                onPressed: () {
                  GlobalInfoDialog.showInfoDialog(
                      imagePath: AppImages.litOwl,
                      description: AppStrings.hootyIADescription,
                      isNetworkImage: false);
                },
                icon: const Icon(
                  Icons.info,
                  color: AppColors.secondary,
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Image.asset(
                          AppImages.houtyAi,
                          height: 10.w,
                          width: 10.w,
                        ),
                        SizedBox(width: 2.w),
                        Flexible(
                          child: Text(
                            AppStrings.aiTitle,
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      Tooltip(
                        message: AppStrings.micIconTooltip,
                        child: InkWell(
                          onTap: () {
                            homeController.updateSelectedAiButton('mic');
                          },
                          child: Container(
                            padding: EdgeInsets.all(2.w),
                            decoration: BoxDecoration(
                              color: selectedButton == 'mic'
                                  ? AppColors.lightPink
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Image.asset(
                              AppImages.mic,
                              height: 8.w,
                              width: 8.w,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Tooltip(
                        message: AppStrings.attachFileTooltip,
                        child: InkWell(
                          onTap: () {
                            homeController.updateSelectedAiButton('attachFile');
                          },
                          child: Container(
                            padding: EdgeInsets.all(2.w),
                            decoration: BoxDecoration(
                              color: selectedButton == 'attachFile'
                                  ? AppColors.lightPink
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Image.asset(
                              AppImages.attachFile,
                              height: 8.w,
                              width: 8.w,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 2.h),
              if (selectedButton == 'attachFile') ...[
                if (!isFileUploaded)
                  GestureDetector(
                    onTap: homeController.pickFile,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(4.w),
                      margin: EdgeInsets.only(top: 2.h),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        border: Border.all(
                          color: AppColors.lightPinkBorderColor,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        children: [
                          Container(
                            padding: EdgeInsetsDirectional.all(1.h),
                            decoration: BoxDecoration(
                              color: AppColors.secondary.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.cloud_upload,
                              size: 7.w,
                              color: AppColors.secondary,
                            ),
                          ),
                          SizedBox(height: 1.h),
                          Text(
                            "Click to upload or drag and drop",
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 1.h),
                          Text(
                            "PDF, TXT, JPG, or GIF (max. 800×400px)",
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: Colors.black54,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  )
                else if (selectedFile != null)
                  _buildUploadedFileCard(),
              ] else if (selectedButton == 'mic') ...[
                _buildMicUi(),
              ],
            ],
          );
        }),
      ),
    );
  }

  Widget _buildUploadedFileCard() {
    final fileName = homeController.selectedFile.value!.name;
    final fileSize =
        (homeController.selectedFile.value!.size / 1024).toStringAsFixed(2);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: AppColors.white.withOpacity(0.4),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.white),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(1.h),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.insert_drive_file_outlined,
                    size: 6.w, color: AppColors.secondary),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Text(fileName,
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.bold)),
                        ),
                        SizedBox(width: 1.w),
                        InkWell(
                          onTap: homeController.clearSelectedFile,
                          child: Container(
                            padding: EdgeInsets.all(0.5.h),
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.clear,
                              color: Colors.white,
                              size: 5.w,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 0.5.h),
                    Text("$fileSize KB",
                        style: TextStyle(
                            fontSize: 9.sp, color: Colors.grey.shade600)),
                    SizedBox(height: 0.5.h),
                    Row(
                      children: [
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: LinearProgressIndicator(
                              value: 1.0,
                              backgroundColor: Colors.grey.shade300,
                              color: AppColors.primary,
                              minHeight: 1.h,
                            ),
                          ),
                        ),
                        SizedBox(width: 2.w),
                        Text("100%",
                            style: TextStyle(
                                fontSize: 10.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColors.black)),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 2.h),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        FlatButtonGlobal(
          text: "Transformer en Flashcard",
          onPressed: () {
            // Flashcard action
          },
          useGradient: true,
        ),
        SizedBox(height: 2.h),
        FlatButtonGlobal(
          text: "Transformer en Quiz",
          onPressed: () {
            // Quiz action
          },
          useGradient: true,
        ),
        SizedBox(height: 2.h),
        GradientButton(
          text: "Enregistrer sur mon espace perso",
          onPressed: () {
            // Save action
          },
        ),
      ],
    );
  }

  Widget _buildMicUi() {
    return Obx(() {
      if (homeController.isRecording.value) {
        return _buildRecordingUi();
      } else if (homeController.recordedFilePath.value.isNotEmpty) {
        return _buildPlaybackUi();
      } else {
        return _buildStartRecordingButton();
      }
    });
  }

  Widget _buildRecordingUi() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SizedBox(
            height: 60,
            child: AudioWaveforms(
              recorderController: homeController.recorderController,
              size: const Size(double.infinity, 60),
              waveStyle: WaveStyle(
                gradient: AppColors.audioWaveGradient.createShader(
                  const Rect.fromLTWH(0, 0, 2000, 100),
                ),
                extendWaveform: true,
                showMiddleLine: false,
                waveCap: StrokeCap.round,
                waveThickness: 3.0,
              ),
            ),
          ),
        ),
        IconButton(
          icon: Icon(
            homeController.isPaused.value
                ? Icons.play_arrow_rounded
                : Icons.pause_rounded,
            color: AppColors.secondary,
          ),
          iconSize: 30,
          onPressed: () {
            if (homeController.isPaused.value) {
              homeController.resumeRecording();
            } else {
              homeController.pauseRecording();
            }
          },
        ),
        IconButton(
          icon: const Icon(
            Icons.delete_rounded,
            color: AppColors.secondary,
          ),
          iconSize: 30,
          onPressed: homeController.cancelRecording,
        ),
        IconButton(
          icon: const Icon(
            Icons.done,
            color: AppColors.secondary,
          ),
          iconSize: 30,
          onPressed: homeController.stopRecording,
        ),
      ],
    );
  }

  Widget _buildPlaybackUi() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: AppColors.white.withOpacity(0.4),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.white),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              IconButton(
                icon: Obx(() {
                  return Icon(
                    homeController.isPlaying.value
                        ? Icons.pause_rounded
                        : Icons.play_arrow_rounded,
                    color: AppColors.secondary,
                    size: 30,
                  );
                }),
                onPressed: () {
                  if (homeController.isPlaying.value) {
                    homeController.pausePlayback();
                  } else {
                    homeController.playRecording();
                  }
                },
              ),
              Expanded(
                child: SizedBox(
                  height: 60,
                  child: AudioFileWaveforms(
                    playerController: homeController.playerController,
                    size: const Size(double.infinity, 60),
                    enableSeekGesture: true,
                    waveformType: WaveformType.long,
                    animationCurve: Curves.easeInOut,
                    playerWaveStyle: PlayerWaveStyle(
                      fixedWaveGradient:
                          AppColors.audioWaveGradient.createShader(
                        const Rect.fromLTWH(0, 0, 1000, 60),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 2.w),
              Obx(() {
                final displayText = homeController.isPlaying.value
                    ? homeController.currentPositionText.value
                    : homeController.totalDurationText.value;

                return Text(
                  displayText,
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: AppColors.secondary,
                  ),
                );
              }),
              SizedBox(width: 1.w),
              InkWell(
                onTap: () {
                  homeController.stopPlayback();
                  homeController.recordedFilePath.value = '';
                  homeController.isPlaying.value = false;
                },
                child: Container(
                  padding: EdgeInsets.all(0.5.h),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.clear,
                    color: Colors.white,
                    size: 5.w,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 2.h),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildStartRecordingButton() {
    return Center(
      child: ElevatedButton(
        onPressed: homeController.startRecording,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.white,
          padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: const Text(
          "Start Recording",
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
