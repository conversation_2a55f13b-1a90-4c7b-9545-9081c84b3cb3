<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('niveaux_matiere', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('niveau_id');
            $table->unsignedBigInteger('matiere_id');
            $table->timestamps();

            $table->foreign('niveau_id')->references('id')->on('niveaux');
            $table->foreign('matiere_id')->references('id')->on('matieres');
        });
    }

    public function down()
    {
        Schema::dropIfExists('niveaux_matiere');
    }
};

