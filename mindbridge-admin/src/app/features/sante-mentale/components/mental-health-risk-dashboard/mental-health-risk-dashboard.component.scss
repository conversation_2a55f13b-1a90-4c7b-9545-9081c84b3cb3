.dashboard-container {
  .summary-card {
    transition: all 0.3s ease;
    border: 1px solid #E9EDF7;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .icon-container {
      min-width: 60px;
      min-height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .risk-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      border-color: #4318FF;
      background-color: #F7F9FF;
    }
  }

  .chart-card {
    border: 1px solid #E9EDF7;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

    .chart-container {
      padding: 16px;
      
      .chart-wrapper {
        position: relative;
        width: 100%;
      }
    }
  }

  .students-table-card {
    border: 1px solid #E9EDF7;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

    .table-container {
      max-height: 600px;
      overflow-y: auto;
    }

    .mat-mdc-table {
      .mat-mdc-header-row {
        background-color: #F7F9FF;
      }

      .mat-mdc-row {
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #F9FAFB;
        }

        &.risk-high {
          border-left: 4px solid #EF4444;
        }

        &.risk-medium {
          border-left: 4px solid #F59E0B;
        }

        &.risk-low {
          border-left: 4px solid #10B981;
        }
      }

      .mat-mdc-cell, .mat-mdc-header-cell {
        padding: 12px 16px;
      }
    }
  }

  .error-card {
    border: 1px solid #FEE2E2;
    background-color: #FEF2F2;
    
    .mat-mdc-card-content {
      padding: 20px;
    }
  }

  .refresh-btn {
    background-color: #4318FF;
    color: white;
    
    &:hover {
      background-color: #3311CC;
    }

    &:disabled {
      background-color: #A3AED0;
    }
  }
}

// Chart.js custom styles
.chart-wrapper {
  canvas {
    max-height: 100% !important;
  }
}

// Material Design overrides
.mat-mdc-card {
  border-radius: 12px;
}

.mat-mdc-card-header {
  padding: 20px 20px 0 20px;
  
  .mat-mdc-card-title {
    margin-bottom: 0;
  }
}

.mat-mdc-card-content {
  padding: 20px;
}

.mat-mdc-chip {
  font-size: 12px;
  font-weight: 500;
  border-radius: 16px;
}

.mat-mdc-button {
  border-radius: 8px;
}

.mat-mdc-raised-button {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
    
    .grid {
      grid-template-columns: 1fr;
    }
    
    .students-table-card {
      .table-container {
        overflow-x: auto;
      }
      
      .mat-mdc-table {
        min-width: 800px;
      }
    }
  }
}

@media (max-width: 640px) {
  .dashboard-container {
    .summary-card {
      .mat-mdc-card-content {
        flex-direction: column;
        text-align: center;
        
        .icon-container {
          margin-right: 0;
          margin-bottom: 12px;
        }
      }
    }
    
    .chart-card {
      .chart-wrapper {
        height: 250px !important;
      }
    }
  }
}

// Loading spinner styles
.mat-mdc-progress-spinner {
  --mdc-circular-progress-active-indicator-color: #4318FF;
}

// Custom scrollbar for table
.table-container {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #F1F5F9;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #CBD5E1;
    border-radius: 3px;
    
    &:hover {
      background: #94A3B8;
    }
  }
}
