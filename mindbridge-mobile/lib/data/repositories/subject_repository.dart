import '../../../core/constants/api_routes.dart';
import '../../core/models/subject.dart';
import '../providers/remote/dio_client.dart';

class SubjectResponse {
  final Subject? data;
  final bool isEmpty;
  final bool isError;

  SubjectResponse({
    this.data,
    this.isEmpty = false,
    this.isError = false,
  });
}

class SubjectRepository {
  final DioClient _dioClient;

  SubjectRepository({required DioClient dioClient}) : _dioClient = dioClient;

  Future<SubjectResponse> fetchSubjects(int categoryId) async {
    try {
      final response =
          await _dioClient.dio.get('${ApiRoutes.matiers}/$categoryId');

      if (response.statusCode == 200) {
        if (response.data['matiers'] == null ||
            response.data['matiers'].isEmpty) {
          return SubjectResponse(isEmpty: true);
        }
        return SubjectResponse(data: Subject.fromJson(response.data));
      }else if (response.statusCode == 404 || response.statusCode == 403) {
        return SubjectResponse(isEmpty: true);
      }
    } catch (e) {
      print("Error fetching subjects: $e");
      return SubjectResponse(isError: true);
    }
    return SubjectResponse(isError: true);
  }
}
