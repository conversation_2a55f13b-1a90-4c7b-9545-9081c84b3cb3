<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('etudiant_badges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('eb_etudiant_id')
                ->nullable()
                ->constrained('etudiants')
                ->cascadeOnDelete();

            $table->foreignId('eb_badge_id')
                ->nullable()
                ->constrained('badges')
                ->cascadeOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('etudiant_badges');
    }
};
