
.content_main {
    width: 76vw;
    height: calc(100vh - 100px);
    margin-inline: auto;
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .filter {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 10px;

        .filde {
            width: 33%;
            height: 44px;
            position: relative;

            .search {
                position: absolute;
                top: 50%;
                left: 10px;
                transform: translateY(-50%);
            }

            .input {
                width: 100%;
                height: 44px;
                padding: 0 10px 0 40px;
                border: 1px solid #D5D7DA;
                border-radius: 8px;
                box-sizing: border-box;
            }
        }

        .filde:has(>.button) {
            width: 10%;
        }

        .filde > .button {
            width: 100%;
            height: 44px;
            padding: 0 10px;
            border: 1px solid #D5D7DA;
            border-radius: 8px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .filde:has(>.add) {
            width: 16%;
            margin-left: auto;
        }

        .filde > .add {
            all: unset;
            width: 100%;
            height: 44px;
            padding: 0 10px;
            border-radius: 8px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            background: linear-gradient(to bottom, #C1EFF7 0%, #6BA4BD 42%, #7E588E 100%, #BE1A87 100%);
            cursor: pointer;

            span {
                color: #fff;
            }
        }
    }

    .add_question {
        width: 100%;
    }

    .list_questions {
        width: 100%;
        height: calc(100% - 64px);
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 20px;
    }
}

.actions {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left_actions {
        display: flex;
        align-items: center;
        gap: 10px;

        .btn_right_actions {
            color: #568397;
            background: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 14px;
            border-radius: 8px;
        }

        .btn_right_actions:last-child {
            background: linear-gradient(180deg, #F0F6F8 0%, #E1EDF2 51.5%, #F2EEF4 100%);
        }
    }
}

.span_questions {
    background: transparent !important;
    border: 0 !important;
    margin-bottom: 10px !important;
    color: #344054;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
}

.correct_response {
    span {
        text-wrap: nowrap;
        // font-size: #414651;
    }
}

.description_questions {
    color: #344054;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
}



button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.required {
    border: 1px solid red !important;
    border-radius: 5px !important;
}

.required_input {
    color: red !important;
}

// ================ TABLE ================
.timer {
    position: relative;

    .toggle_timer {
        position: absolute;
        top: 70%;
        right: 10px;
        transform: translateY(-50%);
        cursor: pointer;
    }


    .set_timer {
        position: absolute;
        top: 100%;
        right: 0;
        width: 350px;
        height: 134px;
        background-color: #fff;
        box-shadow: 0px 0px 20px 4px #BFBFBF40;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30px;

        .content_timer {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 15px;
            height: 100%;

            img {
                width: 30px;
                height: 30px;
                cursor: pointer;
            }
        }
    }
}

.scrolling_container {
    overflow-y: auto;
    height: 100%;
    max-height: calc(100vh - 200px);
}

.border_b {
    border-bottom: 2px solid #e5e7eb;
    border-radius: 0;
    background-color: #bcc3d30d;
}

.bg_btn {
    background: linear-gradient(180deg, #C1EFF7 6%, #6BA4BD 100%, #7E588E 100%, #BE1A87 100%);
    color: #fff;
}

.bg_btn_censel {
    background-color: #56839729;
    color: #568397;
}

.not_allowed {
    cursor: not-allowed !important;
}

.list_test {
    background-color: #fff;
    position: relative;

    .delete_test {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 20px;
        height: 20px;
        cursor: pointer;
    }

    h2 {
        font-weight: 400;
    }
}

.input_answer {
    position: relative;

    .icon_answer {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 15px;
        top: 20%;
        cursor: pointer;
    }
}

.toggle_description {
    margin-bottom: 20px !important;
}
