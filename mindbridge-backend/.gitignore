/.phpunit.cache
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/app/public/bulltin/*
!/storage/app/public/bulltin/.gitkeep
/storage/app/public/qr_codes/*
!/storage/app/public/qr_codes/.gitkeep
/storage/app/public/templetOrigin/*
/storage/app/public/mindBridge/*
!/storage/app/public/templetOrigin/.gitkeep
!/storage/app/public/mindBridge/.gitkeep
/storage/app/temp/*
!/storage/app/temp/.gitkeep
/vendor
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode

!public/users/profiles/dev-user-avatar.png
!public/enseignant/profiles/enseignant-avatar.png
!public/etudiants/profiles/etudiant-avatar.png
!public/parrents/profiles/parrent-avatar.png
