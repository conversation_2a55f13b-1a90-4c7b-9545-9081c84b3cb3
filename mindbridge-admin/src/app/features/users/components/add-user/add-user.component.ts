import { Component, inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-add-user',
    templateUrl: './add-user.component.html',
    styleUrl: './add-user.component.scss',
    imports: [MatIcon, ReactiveFormsModule]
})
export class AddUserComponent {
  private dialogRef = inject<MatDialogRef<AddUserComponent>>(MatDialogRef);
  data = inject(MAT_DIALOG_DATA);

  form: FormGroup = new FormGroup({});


  close() {
    this.dialogRef.close();
  }
}
