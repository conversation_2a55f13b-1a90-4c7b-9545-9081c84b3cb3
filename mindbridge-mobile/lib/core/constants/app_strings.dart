class AppStrings {
  // Splash screen strings
  static const String splashTitle1 = 'Lorem ipsum dolor';
  static const String splashSubtitle1 =
      'Aujourd’hui, on commence par un petit test ludique pour mieux te connaître. Pas d’inquiétude, il n’y a ni note, ni pression : l’idée est de comprendre comment tu apprends, ce qui te motive, et ce qui pourrait t’aider à progresser.';
  static const String splashTitle2 = 'Duis aute irure dolor';
  static const String splashSubtitle2 =
      'Grâce à tes réponses, MindBridge pourra te proposer des contenus sur mesure, adaptés à ton style et à tes besoins, pour que chaque étape de ton apprentissage soit une réussite.';
  static const String splashTitle3 = 'At vero eos et accusamus';
  static const String splashSubtitle3 =
      'Prêt(e) à commencer cette aventure ? On est là pour t’accompagner à chaque pas. 🚀';
  static const String splashSkip = 'Passer';
  static const String splashNext = 'Suivant';
  static const String splashTest = 'Passer le test';

  // Login screen strings
  static const String loginTitle = 'Log in to your account';
  static const String loginSubtitle =
      'Welcome back!\nPlease enter your details.';
  static const String emailHint = 'Enter your email';
  static const String passwordHint = 'Password';
  static const String rememberMe = 'Remember for 30 days';
  static const String forgotPassword = 'Forgot password';
  static const String signIn = 'Sign in';
  static const String noAccount = "Don’t have an account?";
  static const String signUp = 'Sign up';

  // Complete profile screen strings
  static const String aptitudeTestTitle = 'Test d’aptitude';
  static const String next = 'Suivant';
  static const String finish = 'Terminer';

  // Congratulations screen strings
  static const String congratulationsTitle = 'Félicitations';
  static const String congratulationsSubtitle =
      'Tu viens de terminer ton test, et grâce à tes réponses, MindBridge connaît désormais un peu mieux ton style d’apprentissage, tes forces et tes besoins.\n\nÀ partir de maintenant, tout ce que tu trouveras ici sera pensé pour toi : des leçons adaptées, des défis sur mesure, et des outils pour t’aider à aller toujours plus loin.\n\nTu es prêt(e) à découvrir tout ce que tu peux accomplir ? On te suit dans cette aventure unique. 🚀✨';
  static const String start = 'Démarrer';

  // Profile card strings
  static const String balance = 'Balance';
  static const String classement = 'Classement';

  // AI Card strings
  static const String aiTitle = 'Hooty IA';
  static const String promptHint = 'Prompt...';
  static const String micIconTooltip = 'Record audio';
  static const String attachFileTooltip = 'Attach file';
  static const String generateTooltip = 'Generate';

  // Cards strings
  // Cards titles
  static const String weeklyChallenge = 'Challenge hebdomadaire';
  static const String takeSurvey = 'Sondages tests';
  static const String generalQuiz = 'Quiz culture Générale';
  static const String recommendations = 'Recommandations MindBridge';
  static const String selectLesson = 'Choisis ta leçon';
  static const String examSimulation = 'Examens simulés';

  // Button texts
  static const String acceptChallenge = 'Accepter le challenge';
  static const String participateSurvey = 'Participer au sondage';
  static const String takeQuiz = 'Passer le quiz';
  static const String chooseLesson = 'Liste des leçons';
  static const String takeExam = 'Passer l’examen';

  //Select lesson screen
  static const String progress = 'Progrès';

  // Lesson screen button text
  static const String evaluateUnderstanding = 'Evaluer ma compréhension';

  // Error handler
  static const String errorMessage =
      "Une erreur s'est produite, veuillez réessayer.";
  static const String retry = "Réessayer";

  // Loading messages
  static const String loading = "Chargement...";
  static const String submitting = "Soumission en cours...";
  static const String successMessage = "Soumis avec succès!";
  static const String loginInProgress = "Connexion...";

  // Evaluation screen
  static const String evaluationTitle = 'Évaluer ma compréhension';
  static const String challengeHebdomadaire = 'Challenge hebdomadaire';
  static const String evaluationPrevious = '← Précédent';
  static const String recommandationMindbridge = 'Recommandation';

  // Init screen
  static const String initTitle = 'Feeling Knowledge';

  // Info dialog
  static const String hootyIADescription =
      'Hooty IA is our advanced AI-powered assistant designed to help you learn efficiently and interactively. Explore its features and capabilities for a better experience!';
  static const String gotIt = 'Got it!';

  // Empty state messages
  static const String noDataMessage =
      "Aucune donnée disponible pour le moment.";
  static const String goBack = "Retourner";

  // General buttons
  static const String continuer = 'Continuer';

  // Introduction strings for Test and Survey
  static const String testIntroTitle = "Bienvenue dans le Test";
  static const String testIntroDescription =
      "Ce test est conçu pour évaluer vos connaissances. Prenez le temps de répondre attentivement à chaque question.";
  static const String startTestButton = "Commencer le Test";

  static const String surveyIntroTitle = "Bienvenue dans le Sondage";
  static const String surveyIntroDescription =
      "Ce sondage est conçu pour recueillir vos opinions et vos retours. Prenez le temps de répondre à chaque question attentivement.";
  static const String startSurveyButton = "Commencer le Sondage";
  static const String mentalHealthTitle = "Temps de pause";
  static const String mentalHealthDescription = "Rien d'exigeant, juste un petit instant à vous accorder.";
  static const String mentalHealthButton = "Prendre ce temps";

  // Espace Personnel
  static const String badgesCollected = 'Badges';
  static const String myDocuments = "Mes documents";
  static const String myRecordings = "Mes enregistrements";
  static const String noRecordings = "Aucun enregistrement pour l'instant";
}
