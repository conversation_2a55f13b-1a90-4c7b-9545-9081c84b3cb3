import { inject, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from '../../core/app.state';
import { patchState, signalState } from '@ngrx/signals';
import { SanteMentaleService } from './sante-mentale.service';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { of, pipe } from 'rxjs';
import { catchError, finalize, map, switchMap, tap } from 'rxjs/operators';
import { SetLoading } from '../shared/store/shared.actions';
import { CategoryTest } from './models/category-tests-response.model';
import { Pagination } from '../shared/helpers/query.helper';
import { ApiResponse } from '../../core/models/api-response';
import {
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from '../shared/global.config';
import { ParticipantResponse } from './models/participant-response';
import {
  IParticipantSearchForm,
  ParticipantSearchForm,
} from './models/particicpant-search-request';
import { data } from 'autoprefixer';
import {
  MentalHealthRiskDashboardData,
  DashboardFilters,
  DashboardFiltersForm,
  AtRiskStudent,
  DashboardSummary,
  DashboardCharts
} from './models/mental-health-risk-dashboard';

export type SanteMentaleState = {
  categoryTests: CategoryTest[];
  participantsSearchForm: IParticipantSearchForm;
  current_page: number;
  total_pages: number;
  per_page: number;
  total_items: number;
  participants: ParticipantResponse[];
  // Dashboard state
  dashboardData: MentalHealthRiskDashboardData | null;
  dashboardFilters: DashboardFilters;
  dashboardLoading: boolean;
  dashboardError: string | null;
};

export const initialSanteMentaleState: SanteMentaleState = {
  categoryTests: [],
  participantsSearchForm: new ParticipantSearchForm(),
  current_page: DEFAULT_CURRENT_PAGE,
  total_pages: 1,
  per_page: DEFAULT_PAGE_SIZE,
  total_items: 0,
  participants: [],
  // Dashboard initial state
  dashboardData: null,
  dashboardFilters: new DashboardFiltersForm(),
  dashboardLoading: false,
  dashboardError: null,
};

@Injectable({
  providedIn: 'root',
})
export class SanteMentaleStore {
  store = inject<Store<AppState>>(Store);

  readonly state = signalState<SanteMentaleState>(initialSanteMentaleState);
  #santeMentaleService = inject(SanteMentaleService);

  getCategoryTests = rxMethod<number>(
    pipe(
      switchMap((category_id: number) => {
        console.log('[SanteMentaleStore] Fetching tests for category:', category_id);
        return this.#santeMentaleService.getCategoryTests(category_id).pipe(
          tap((response: any) => {
            console.log('[SanteMentaleStore] API Response:', response);
          }),
          map((response: any) => {
            console.log('[SanteMentaleStore] Extracted data:', response.data);
            return response.data;
          }),
          tap((categoryTests: CategoryTest[]) => {
            console.log('[SanteMentaleStore] Setting categoryTests in state:', categoryTests);
            patchState(this.state, { categoryTests });
          }),
          catchError((error: any) => {
            console.error('[SanteMentaleStore] Error fetching category tests:', error);
            console.error('[SanteMentaleStore] Error status:', error.status);
            console.error('[SanteMentaleStore] Error message:', error.message);
            console.error('[SanteMentaleStore] Full error object:', error);
            this.store.dispatch(SetLoading({ isAppLoading: false }));
            return of([]);
          }),
          finalize(() => {
            console.log('[SanteMentaleStore] Finalize - setting loading to false');
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        );
      }),
      finalize(() => {})
    )
  );

  setSearchForm = (participantsSearchForm: IParticipantSearchForm) =>
    patchState(this.state, { participantsSearchForm });

  setCurrentPage = (current_page: number) =>
    patchState(this.state, { current_page });

  getParticipants = rxMethod<void>(
    pipe(
      switchMap(() => {
        const pagination: Pagination = {
          current_page: this.state.current_page(),
          per_page: this.state.per_page(),
        };
        return this.#santeMentaleService
          .getListeParticipants(this.state.participantsSearchForm(), pagination)
          .pipe(
            tap((response: ApiResponse<ParticipantResponse>) => {
              patchState(this.state, {
                participants: response.data,

                current_page: response.current_page,
                per_page: response.per_page,
                total_pages: response.total_pages,
                total_items: response.total_items,
              });
            }),
            finalize(() => {
              this.store.dispatch(SetLoading({ isAppLoading: false }));
            })
          );
      }),
      finalize(() => {})
    )
  );

  // Dashboard methods
  setDashboardFilters = (filters: DashboardFilters) =>
    patchState(this.state, { dashboardFilters: filters });

  clearDashboardData = () =>
    patchState(this.state, {
      dashboardData: null,
      dashboardError: null
    });

  getDashboardData = rxMethod<DashboardFilters | void>(
    pipe(
      tap(() => {
        patchState(this.state, {
          dashboardLoading: true,
          dashboardError: null
        });
        this.store.dispatch(SetLoading({ isAppLoading: true }));
      }),
      switchMap((filters) => {
        const filtersToUse = filters || this.state.dashboardFilters();
        return this.#santeMentaleService
          .getMentalHealthRiskDashboard(filtersToUse)
          .pipe(
            tap((response) => {
              if (response.success) {
                patchState(this.state, {
                  dashboardData: response.data,
                  dashboardError: null,
                });
              } else {
                patchState(this.state, {
                  dashboardError: 'Failed to load dashboard data',
                });
              }
            }),
            catchError((error) => {
              console.error('[SanteMentaleStore] Dashboard error:', error);
              patchState(this.state, {
                dashboardError: error.message || 'An error occurred while loading dashboard data',
              });
              return of(null);
            }),
            finalize(() => {
              patchState(this.state, { dashboardLoading: false });
              this.store.dispatch(SetLoading({ isAppLoading: false }));
            })
          );
      })
    )
  );

  // Computed selectors for dashboard data
  get dashboardSummary() {
    return this.state.dashboardData()?.summary || null;
  }

  get atRiskStudents() {
    return this.state.dashboardData()?.students || [];
  }

  get dashboardCharts() {
    return this.state.dashboardData()?.charts || null;
  }

  get isHighRiskStudent() {
    return (student: AtRiskStudent) => student.risk_level === 'high';
  }

  get filteredStudentsByRisk() {
    return (riskLevel?: 'high' | 'medium' | 'low') => {
      const students = this.atRiskStudents;
      if (!riskLevel) return students;
      return students.filter(student => student.risk_level === riskLevel);
    };
  }
}
