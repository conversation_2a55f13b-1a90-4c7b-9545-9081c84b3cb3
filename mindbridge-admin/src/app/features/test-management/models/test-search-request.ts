import { FormControl } from '@angular/forms';
import { OPTION_ALL } from '../../shared/global.config';

export interface ITestSearchForm {
  matiere: number;
  type: string;
  niveau: number;
  category_id: number;
}

export class TestSearchForm implements ITestSearchForm {
  matiere: number;
  type: string;
  niveau: number;
  category_id: number;
  constructor() {
    this.matiere = OPTION_ALL;
    this.type = '';
    this.niveau = OPTION_ALL;
    this.category_id = OPTION_ALL;
  }
}

export interface ITestSearchFormGroup {
  matiere: FormControl<number>;
  type: FormControl<string>;
  niveau: FormControl<number>;
  category_id: FormControl<number>;
}

export class TestSearchFormGroup implements ITestSearchFormGroup {
  matiere: FormControl<number>;
  type: FormControl<string>;
  niveau: FormControl<number>;
  category_id: FormControl<number>;

  constructor(testSearchForm: ITestSearchForm = new TestSearchForm()) {
    this.matiere = new FormControl<number>(testSearchForm.matiere);
    this.type = new FormControl<string>(testSearchForm.type);
    this.niveau = new FormControl<number>(testSearchForm.niveau);
    this.category_id = new FormControl<number>(testSearchForm.category_id);
  }
}
