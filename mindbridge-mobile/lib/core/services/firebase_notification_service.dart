import 'dart:convert';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:mindbridge/data/providers/local/secure_storage_service.dart';

import '../../../data/repositories/auth_repository.dart';
import '../../presentation/screens/home/<USER>/home_controller.dart';

// Top-level function to handle background messages
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await _handleNotificationData(data: message.data);
}

Future<void> _handleNotificationData({ required Map<String, dynamic> data }) async {
  try {
    final authRepository = Get.put(AuthRepository(
      dioClient: Get.find(),
      secureStorage: Get.find(),
    ));
    final storedUser = await authRepository.getStoredUser();
    final secureStorage = Get.find<SecureStorageService>();

    // Check if the user is logged in and has completed test profiling
    if (storedUser?.token != null &&
        storedUser?.etudiant?.testProfilingCompleted == '1') {
      final homeController = Get.put(HomeController(
        homeRepository: Get.find(),
      ));
      if (data['type'] == 'mental_health_test' && data['test_id'] != null) {
        await secureStorage.write('has_mental_health_test', data['test_id'].toString());
      }
      await homeController.fetchCategories();
    } else {
      print('User not logged in or test profiling not completed.');
    }
  } catch (e) {
    print("Error handling notification data: $e");
  }
}

class FirebaseNotificationService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> initialize() async {
    try {
      // Request notification permissions
      await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      // Get the FCM token
      String? token;
      try {
        token = await _firebaseMessaging.getToken();
        if (kDebugMode) {
          print("FCM Token: $token");
        }
      } catch (e) {
        if (kDebugMode) {
          print("FCM token error (likely iOS simulator): $e");
          print("FCM tokens are not available on iOS simulator");
        }
      }

      // Setup Android and iOS notification initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        ),
      );

      // Initialize notifications
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse:
            (NotificationResponse notificationResponse) async {
          if (notificationResponse.payload != null) {
            Map<String, dynamic> payloadMap = json.decode(notificationResponse.payload!);
            await _handleNotificationData(data: payloadMap);
          }
        },
      );

      // Listen for foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
        await _showNotification(message);
        await _handleNotificationData(data: message.data);
      });

      // Handle messages when the app is opened from a notification (background state)
      FirebaseMessaging.onMessageOpenedApp
          .listen((RemoteMessage message) async {
        await _handleNotificationData(data: message.data);
      });

      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

      // Handle when the app is opened from a terminated state
      FirebaseMessaging.instance
          .getInitialMessage()
          .then((RemoteMessage? message) async {
        if (message != null) {
          await _handleNotificationData(data: message.data);
        }
      });
    } catch (e) {
      print('Failed to initialize Firebase: $e');
    }
  }

  Future<void> _showNotification(RemoteMessage message) async {
    try {
      // Setup notification details for Android and iOS
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'your_channel_id', 'your_channel_name',
        channelDescription: 'your_channel_description',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true, // Show timestamp
      );

      const DarwinNotificationDetails iosPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentSound: true,
        presentAlert: true,
        presentBadge: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iosPlatformChannelSpecifics,
      );

      // Show notification with payload
      await _flutterLocalNotificationsPlugin.show(
        0,
        message.notification?.title ?? 'Notification',
        message.notification?.body ?? 'You have a new message',
        platformChannelSpecifics,
        payload: '/notifications',
      );
    } catch (e) {
      print('Failed to show notification: $e');
    }
  }
}
