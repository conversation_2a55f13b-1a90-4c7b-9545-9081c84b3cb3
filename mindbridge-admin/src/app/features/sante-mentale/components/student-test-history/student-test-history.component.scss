// Student Test History Component Styles

:host {
  display: block;
}

// Scrollbar styling for test list
.max-h-96 {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;

    &:hover {
      background: #94a3b8;
    }
  }
}

// Tab button active state
button[class*='border-blue-500'] {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #6366f1);
  }
}

// Smooth transitions
button {
  transition: all 0.2s ease-in-out;
}

// Status badge animation
span[class*='inline-block rounded-full'] {
  transition: all 0.2s ease-in-out;
}

// Gradient background for scoring section
.bg-gradient-to-r {
  background-image: linear-gradient(to right, #eff6ff, #eef2ff);
}

// Responsive adjustments
@media (max-width: 1024px) {
  .grid-cols-3 {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}

