# Mental Health Tests Documentation - Complete Index

## 📚 Documentation Files

This comprehensive documentation explains how mental health tests ("Sante Mentale") are fetched, managed, and used across the MindBridge application.

### 1. **MENTAL_HEALTH_TESTS_SUMMARY.md** ⭐ START HERE
   - **Purpose:** Executive summary and overview
   - **Best For:** Getting a quick understanding of the system
   - **Contains:**
     - System overview and architecture
     - User journeys (student and admin)
     - Technology stack
     - Key features and benefits
   - **Read Time:** 10-15 minutes

### 2. **MENTAL_HEALTH_TESTS_DOCUMENTATION.md** 📖 COMPREHENSIVE GUIDE
   - **Purpose:** Complete technical documentation
   - **Best For:** Developers and technical teams
   - **Contains:**
     - Database architecture (8 tables, relationships)
     - Backend API endpoints (6+ endpoints)
     - Admin panel implementation (Angular)
     - Mobile app implementation (Flutter)
     - Code examples (PHP, TypeScript, Dart)
     - Database queries
     - API response examples
     - Troubleshooting guide
     - Performance optimization tips
     - Security considerations
     - Testing examples
     - Future roadmap
   - **Read Time:** 30-45 minutes
   - **Sections:** 20 major sections with subsections

### 3. **MENTAL_HEALTH_TESTS_QUICK_REFERENCE.md** 🚀 QUICK LOOKUP
   - **Purpose:** Quick reference for common tasks
   - **Best For:** Developers needing quick answers
   - **Contains:**
     - Key file locations
     - Database table reference
     - API endpoints summary
     - Category hierarchy
     - Data flow overview
     - Common tasks and code snippets
     - Status values
     - Debugging tips
     - Database queries
   - **Read Time:** 5-10 minutes
   - **Format:** Tables, code snippets, quick reference

---

## 🎯 How to Use This Documentation

### If You're New to the System
1. Start with **MENTAL_HEALTH_TESTS_SUMMARY.md**
2. Review the architecture diagrams
3. Understand the user journeys
4. Then read **MENTAL_HEALTH_TESTS_DOCUMENTATION.md** sections 1-3

### If You're a Backend Developer
1. Read **MENTAL_HEALTH_TESTS_QUICK_REFERENCE.md** for overview
2. Study **MENTAL_HEALTH_TESTS_DOCUMENTATION.md** sections 1-2 (Database & API)
3. Review code examples in section 14
4. Check database queries in section 14.8

### If You're a Frontend Developer (Admin Panel)
1. Read **MENTAL_HEALTH_TESTS_QUICK_REFERENCE.md**
2. Study **MENTAL_HEALTH_TESTS_DOCUMENTATION.md** section 3 (Admin Panel)
3. Review code examples in section 14.3-14.4
4. Check API endpoints in section 2.1

### If You're a Mobile Developer
1. Read **MENTAL_HEALTH_TESTS_QUICK_REFERENCE.md**
2. Study **MENTAL_HEALTH_TESTS_DOCUMENTATION.md** section 4 (Mobile App)
3. Review code examples in section 14.5-14.7
4. Check API endpoints in section 2.2

### If You Need to Debug an Issue
1. Go to **MENTAL_HEALTH_TESTS_QUICK_REFERENCE.md** → Debugging Tips
2. Or **MENTAL_HEALTH_TESTS_DOCUMENTATION.md** → Section 16 (Troubleshooting)
3. Use provided SQL queries to investigate

### If You Need to Add a New Feature
1. Review **MENTAL_HEALTH_TESTS_DOCUMENTATION.md** section 20 (Future Roadmap)
2. Study the existing code examples
3. Follow the established patterns
4. Update documentation accordingly

---

## 📊 Visual Diagrams

The documentation includes 4 Mermaid diagrams:

### 1. **System Architecture Diagram**
   - Shows all components and their relationships
   - Displays data flow between layers
   - Illustrates backend, API, admin panel, mobile app, and database

### 2. **Data Flow Sequence Diagram**
   - Shows step-by-step data flow
   - Includes 5 main scenarios:
     1. Student home screen initialization
     2. Student test taking
     3. Test submission and result storage
     4. Admin monitoring
     5. Test assignment and notifications

### 3. **Category Hierarchy Diagram**
   - Shows mental health test categories
   - Displays test structure
   - Includes sample tests and questions

### 4. **Complete System Flow Diagram**
   - Shows student flow, admin flow, backend processing, and database
   - Illustrates how all components work together

---

## 🗂️ File Structure Reference

```
mindbridge-backend/
├── app/
│   ├── Models/MindBridge/
│   │   ├── Test.php ⭐
│   │   ├── Category.php ⭐
│   │   ├── Step.php
│   │   ├── Question.php
│   │   ├── Option.php
│   │   ├── EtudiantTestStatus.php ⭐
│   │   ├── EtudiantTestAnswers.php ⭐
│   │   └── EtudiantTestAssignment.php ⭐
│   ├── Http/
│   │   ├── Controllers/MindBridge/
│   │   │   ├── MindBridgeTestController.php ⭐
│   │   │   ├── MindBridgeEtudiantController.php ⭐
│   │   │   └── CategoryController.php
│   │   └── Resources/MindBridge/
│   │       ├── CategoryResource.php
│   │       └── QuestionResource.php
│   └── Service/
│       └── BadgePointService.php
├── routes/
│   └── api.php ⭐
└── database/
    ├── migrations/mindBridge/
    │   ├── 2024_11_22_132142_create_categories_table.php
    │   ├── 2024_11_23_134031_create_tests_table.php
    │   ├── 2024_12_14_134032_create_etudiant_test_status.php
    │   ├── 2024_12_14_134032_create_etudiant_test_answers_table.php
    │   └── 2025_01_23_212313_create_etudiant_test_assignments_table.php
    └── seeders/mindBridge/
        └── MentalHealthTestsSeeder.php

mindbridge-admin/src/app/features/sante-mentale/
├── sante-mentale.service.ts ⭐
├── sante-mentale.store.ts ⭐
├── components/
│   ├── test-categories/
│   │   └── test-categories.component.ts ⭐
│   ├── category-tests/
│   │   └── category-tests.component.ts ⭐
│   ├── tests-sante-mentale/
│   │   └── tests-sante-mentale.component.ts ⭐
│   └── category-test-details/
│       └── category-test-details.component.ts
└── models/
    ├── category-tests-response.model.ts
    ├── participant-response.ts
    └── particicpant-search-request.ts

mindbridge-mobile/lib/
├── data/
│   ├── repositories/
│   │   ├── home_repository.dart ⭐
│   │   ├── evaluation_repository.dart ⭐
│   │   └── recommandation_repository.dart
│   └── providers/
│       └── remote/
│           └── dio_client.dart
├── core/
│   ├── models/
│   │   ├── category.dart ⭐
│   │   └── evaluation.dart
│   └── constants/
│       └── api_routes.dart
└── presentation/
    ├── widgets/
    │   └── test_btm_sheet.dart ⭐
    └── screens/
        └── home/
            └── controller/
                └── home_controller.dart
```

⭐ = Key files for mental health tests

---

## 🔑 Key Concepts

### Mental Health Test (Sante Mentale)
A structured assessment that evaluates psychological and cognitive aspects of a student's well-being.

### Category
A hierarchical organization of tests. Mental Health is Category ID 2 with 4 subcategories (5-8).

### Step
A single question within a test, containing a question and multiple answer options.

### Test Status
- **EN COURS:** Test in progress
- **TERMINE:** Test completed
- **ABANDON:** Test abandoned

### Test Assignment
The process of assigning a test to a specific student, triggering a notification.

### Scoring
Points awarded based on correct answers, difficulty level, and achievements.

---

## 🔗 Related Documentation

- **Backend Documentation:** `MindBridge_Backend_Documentation.md`
- **Data Synchronization:** `MindBridge_Data_Synchronization_Guide.md`
- **External Connections:** `MindBridge_External_Connections_Documentation.md`
- **Setup Guide:** `SETUP_CENTRAL_DATABASE.md`

---

## 📈 Statistics

- **Database Tables:** 8 core tables
- **API Endpoints:** 6+ endpoints
- **Models:** 8 Eloquent models
- **Components:** 3 main Angular components
- **Repositories:** 2 main Flutter repositories
- **Code Examples:** 8 detailed examples
- **Database Queries:** 3 complex queries
- **API Responses:** 4 example responses
- **Diagrams:** 4 Mermaid diagrams

---

## 🎓 Learning Path

### Beginner (1-2 hours)
1. Read MENTAL_HEALTH_TESTS_SUMMARY.md
2. Review architecture diagrams
3. Understand user journeys

### Intermediate (2-4 hours)
1. Read MENTAL_HEALTH_TESTS_DOCUMENTATION.md sections 1-5
2. Study code examples
3. Review API endpoints

### Advanced (4-8 hours)
1. Read entire MENTAL_HEALTH_TESTS_DOCUMENTATION.md
2. Study all code examples
3. Review database queries
4. Understand security and performance

### Expert (8+ hours)
1. Implement new features
2. Optimize performance
3. Add new test types
4. Extend functionality

---

## ✅ Checklist for Implementation

- [ ] Read MENTAL_HEALTH_TESTS_SUMMARY.md
- [ ] Review architecture diagrams
- [ ] Understand database schema
- [ ] Study API endpoints
- [ ] Review code examples
- [ ] Check security considerations
- [ ] Review performance tips
- [ ] Set up local environment
- [ ] Run tests
- [ ] Deploy to staging
- [ ] Test in production

---

## 🆘 Getting Help

### For Questions About:
- **System Architecture:** See MENTAL_HEALTH_TESTS_SUMMARY.md
- **Database Design:** See MENTAL_HEALTH_TESTS_DOCUMENTATION.md section 1
- **API Endpoints:** See MENTAL_HEALTH_TESTS_DOCUMENTATION.md section 2
- **Admin Panel:** See MENTAL_HEALTH_TESTS_DOCUMENTATION.md section 3
- **Mobile App:** See MENTAL_HEALTH_TESTS_DOCUMENTATION.md section 4
- **Code Examples:** See MENTAL_HEALTH_TESTS_DOCUMENTATION.md section 14
- **Debugging:** See MENTAL_HEALTH_TESTS_DOCUMENTATION.md section 16
- **Quick Lookup:** See MENTAL_HEALTH_TESTS_QUICK_REFERENCE.md

---

## 📝 Document Versions

| Version | Date | Changes |
|---------|------|---------|
| 1.0 | Oct 2025 | Initial comprehensive documentation |

---

## 🎯 Next Steps

1. **Choose your role:** Backend, Frontend, Mobile, or Admin
2. **Read the appropriate section** from the documentation
3. **Review code examples** for your technology stack
4. **Set up your development environment**
5. **Start implementing** following the patterns
6. **Test thoroughly** using provided examples
7. **Deploy with confidence**

---

**Happy coding! 🚀**

For the most detailed information, refer to **MENTAL_HEALTH_TESTS_DOCUMENTATION.md**

