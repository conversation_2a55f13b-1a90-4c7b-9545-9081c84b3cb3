<?php

namespace App\Console\Commands;

use App\Models\School;
use App\Service\TenantService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;

class MigrateCommande extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenants:migrate {tenantName? : The name of the tenant} {--fresh : Drop all tables and re-run all migrations} {--seed : Seed the database with records}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenantName = $this->argument('tenantName');
        $isFresh = $this->option('fresh');
        $shouldSeed = $this->option('seed');
    
        if ($isFresh && !$this->confirm('Do you really wish to drop all tables and re-run all migrations?')) {
            $this->info('Migration cancelled.');
            return Command::SUCCESS;
        }
    
        $migrationCommand = $isFresh ? 'migrate:fresh' : 'migrate';
    
        if ($tenantName) {
            $tenant = School::where('name', $tenantName)->first();
            if ($tenant) {
                $this->migrateTenant($tenant, $migrationCommand, $shouldSeed);
            } else {
                $this->error('School not found.');
            }
        } else {
            $tenants = School::all();
            $tenants->each(function ($tenant) use ($migrationCommand, $shouldSeed) {
                $this->migrateTenant($tenant, $migrationCommand, $shouldSeed);
            });
        }
    
        return Command::SUCCESS;
    }
    
    
    protected function migrateTenant($tenant, $migrationCommand, $shouldSeed)
    {
        TenantService::switchToTenant($tenant->database);
        $this->info('Starting migration for: ' . $tenant->domain);
        $this->info('----------------------------------');
        
        $params = [
            '--path' => $tenant->migrations_path,
            '--database' => 'tenant'
        ];
    
        if ($shouldSeed) {
            $params['--seed'] = true;
        }
        Config::set('app.seeder_type', 'tenant');
    
        Artisan::call($migrationCommand, $params);
    
        $this->info(Artisan::output());
    }
    
}
