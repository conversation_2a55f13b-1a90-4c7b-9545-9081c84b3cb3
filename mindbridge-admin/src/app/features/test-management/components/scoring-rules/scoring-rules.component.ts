import { Component, Input, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { TestScoringRuleService } from '../../services/test-scoring-rule.service';
import { TestScoringRule, TestScoringRuleRequest } from '../../models/test-scoring-rule';
import { CommonService } from '../../../shared/common.service';
@Component({
  selector: 'app-scoring-rules',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
  ],
  templateUrl: './scoring-rules.component.html',
  styleUrls: ['./scoring-rules.component.scss'],
})
export class ScoringRulesComponent implements OnInit {
  @Input() testId: number;

  #fb = inject(FormBuilder);
  #scoringRuleService = inject(TestScoringRuleService);
  #commonService = inject(CommonService);
  #dialog = inject(MatDialog);

  scoringRules: TestScoringRule[] = [];
  displayedColumns: string[] = ['min_percentage', 'max_percentage', 'interpretation', 'feedback', 'recommendation', 'actions'];
  isLoading = false;
  showForm = false;
  editingRuleId: number | null = null;

  scoringRuleForm: FormGroup;

  ngOnInit(): void {
    this.initializeForm();
    this.loadScoringRules();
  }

  initializeForm(): void {
    this.scoringRuleForm = this.#fb.group({
      min_percentage: [null, [Validators.required, Validators.min(0), Validators.max(100)]],
      max_percentage: [null, [Validators.required, Validators.min(0), Validators.max(100)]],
      interpretation: ['', [Validators.required, Validators.maxLength(255)]],
      feedback: [''],
      recommendation: [''],
    });
  }

  loadScoringRules(): void {
    this.isLoading = true;
    this.#scoringRuleService.getScoringRules(this.testId).subscribe({
      next: (response) => {
        this.scoringRules = response.data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading scoring rules:', error);
        this.#commonService.showToast('Erreur lors du chargement des règles', 'ERROR');
        this.isLoading = false;
      },
    });
  }

  openForm(rule?: TestScoringRule): void {
    this.showForm = true;
    if (rule) {
      this.editingRuleId = rule.id;
      this.scoringRuleForm.patchValue({
        min_percentage: rule.min_percentage,
        max_percentage: rule.max_percentage,
        interpretation: rule.interpretation,
        feedback: rule.feedback,
        recommendation: rule.recommendation,
      });
    } else {
      this.editingRuleId = null;
      this.scoringRuleForm.reset();
    }
  }

  closeForm(): void {
    this.showForm = false;
    this.editingRuleId = null;
    this.scoringRuleForm.reset();
  }

  saveRule(): void {
    if (this.scoringRuleForm.invalid) {
      this.#commonService.showToast('Veuillez remplir tous les champs requis', 'ERROR');
      return;
    }

    const formValue = this.scoringRuleForm.getRawValue();
    const request: TestScoringRuleRequest = {
      min_percentage: formValue.min_percentage,
      max_percentage: formValue.max_percentage,
      interpretation: formValue.interpretation,
      feedback: formValue.feedback,
      recommendation: formValue.recommendation,
    };

    if (this.editingRuleId) {
      this.#scoringRuleService.updateScoringRule(this.testId, this.editingRuleId, request).subscribe({
        next: () => {
          this.#commonService.showToast('Règle mise à jour avec succès', 'SUCCESS');
          this.closeForm();
          this.loadScoringRules();
        },
        error: (error) => {
          console.error('Error updating rule:', error);
          this.#commonService.showToast(error.error?.error || 'Erreur lors de la mise à jour', 'ERROR');
        },
      });
    } else {
      this.#scoringRuleService.createScoringRule(this.testId, request).subscribe({
        next: () => {
          this.#commonService.showToast('Règle créée avec succès', 'SUCCESS');
          this.closeForm();
          this.loadScoringRules();
        },
        error: (error) => {
          console.error('Error creating rule:', error);
          this.#commonService.showToast(error.error?.error || 'Erreur lors de la création', 'ERROR');
        },
      });
    }
  }

  deleteRule(ruleId: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette règle ?')) {
      this.#scoringRuleService.deleteScoringRule(this.testId, ruleId).subscribe({
        next: () => {
          this.#commonService.showToast('Règle supprimée avec succès', 'SUCCESS');
          this.loadScoringRules();
        },
        error: (error) => {
          console.error('Error deleting rule:', error);
          this.#commonService.showToast('Erreur lors de la suppression', 'ERROR');
        },
      });
    }
  }
}

