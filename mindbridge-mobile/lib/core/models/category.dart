// To parse this JSON data, do
//
//     final category = categoryFromJson(jsonString);

import 'dart:convert';

Category categoryFromJson(String str) => Category.fromJson(json.decode(str));

String categoryToJson(Category data) => json.encode(data.toJson());

class Category {
  TestModels? testModels;
  String? welcomeMessage;
  ToDo? testToDo;
  ToDo? mentalHealthToDo;
  ToDo? sondagesToDo;

  Category({
    this.testModels,
    this.welcomeMessage,
    this.testToDo,
    this.sondagesToDo,
    this.mentalHealthToDo,
  });

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        testModels: json["test_models"] == null
            ? null
            : TestModels.fromJson(json["test_models"]),
        welcomeMessage: json["welcome_message"],
        testToDo: json["test_to_do"] == null
            ? null
            : ToDo.fromJson(json["test_to_do"]),
        sondagesToDo: json["sondages_to_do"] == null
            ? null
            : ToDo.fromJson(json["sondages_to_do"]),
        mentalHealthToDo: json["mental_health_to_do"] == null
            ? null
            : ToDo.fromJson(json["mental_health_to_do"]),
      );

  Map<String, dynamic> toJson() => {
        "test_models": testModels?.toJson(),
        "welcome_message": welcomeMessage,
        "test_to_do": testToDo?.toJson(),
        "sondages_to_do": sondagesToDo?.toJson(),
        "mental_health_to_do": mentalHealthToDo?.toJson(),
      };
}

class ToDo {
  int? id;
  String? title;
  String? description;
  dynamic target;
  dynamic type;
  dynamic timer;
  dynamic challengeDate;
  dynamic difficultyLevel;
  dynamic matiereId;
  int? categoryId;
  int? createdBy;
  dynamic niveauId;
  dynamic contentId;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic content;
  List<Step>? steps;

  ToDo({
    this.id,
    this.title,
    this.description,
    this.target,
    this.type,
    this.timer,
    this.challengeDate,
    this.difficultyLevel,
    this.matiereId,
    this.categoryId,
    this.createdBy,
    this.niveauId,
    this.contentId,
    this.createdAt,
    this.updatedAt,
    this.content,
    this.steps,
  });

  factory ToDo.fromJson(Map<String, dynamic> json) => ToDo(
        id: json["id"],
        title: json["title"],
        description: json["description"],
        target: json["target"],
        type: json["type"],
        timer: json["timer"],
        challengeDate: json["challenge_date"],
        difficultyLevel: json["difficulty_level"],
        matiereId: json["matiere_id"],
        categoryId: json["category_id"],
        createdBy: json["created_by"],
        niveauId: json["niveau_id"],
        contentId: json["content_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        content: json["content"],
        steps: json["steps"] == null
            ? []
            : List<Step>.from(json["steps"]!.map((x) => Step.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "target": target,
        "type": type,
        "timer": timer,
        "challenge_date": challengeDate,
        "difficulty_level": difficultyLevel,
        "matiere_id": matiereId,
        "category_id": categoryId,
        "created_by": createdBy,
        "niveau_id": niveauId,
        "content_id": contentId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "content": content,
        "steps": steps == null
            ? []
            : List<dynamic>.from(steps!.map((x) => x.toJson())),
      };
}

class Step {
  int? id;
  int? testId;
  int? questionId;
  bool? required;
  String? type;
  dynamic condition;
  int? order;
  DateTime? createdAt;
  DateTime? updatedAt;
  Question? question;

  Step({
    this.id,
    this.testId,
    this.questionId,
    this.required,
    this.type,
    this.condition,
    this.order,
    this.createdAt,
    this.updatedAt,
    this.question,
  });

  factory Step.fromJson(Map<String, dynamic> json) => Step(
        id: json["id"],
        testId: json["test_id"],
        questionId: json["question_id"],
        required: json["required"],
        type: json["type"],
        condition: json["condition"],
        order: json["order"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        question: json["question"] == null
            ? null
            : Question.fromJson(json["question"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "test_id": testId,
        "question_id": questionId,
        "required": required,
        "type": type,
        "condition": condition,
        "order": order,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "question": question?.toJson(),
      };
}

class Question {
  int? id;
  String? type;
  String? content;
  String? description;
  dynamic isTrue;
  dynamic isFalse;
  bool? isRequired;
  dynamic imagePath;
  DateTime? createdAt;
  DateTime? updatedAt;
  List<Option>? options;

  Question({
    this.id,
    this.type,
    this.content,
    this.description,
    this.isTrue,
    this.isFalse,
    this.isRequired,
    this.imagePath,
    this.createdAt,
    this.updatedAt,
    this.options,
  });

  factory Question.fromJson(Map<String, dynamic> json) => Question(
        id: json["id"],
        type: json["type"],
        content: json["content"],
        description: json["description"],
        isTrue: json["is_true"],
        isFalse: json["is_false"],
        isRequired: json["is_required"],
        imagePath: json["image_path"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        options: json["options"] == null
            ? []
            : List<Option>.from(
                json["options"]!.map((x) => Option.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "type": type,
        "content": content,
        "description": description,
        "is_true": isTrue,
        "is_false": isFalse,
        "is_required": isRequired,
        "image_path": imagePath,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "options": options == null
            ? []
            : List<dynamic>.from(options!.map((x) => x.toJson())),
      };
}

class Option {
  int? id;
  String? name;
  String? icon;
  int? isCorrect;
  int? questionId;
  DateTime? createdAt;
  DateTime? updatedAt;

  Option({
    this.id,
    this.name,
    this.icon,
    this.isCorrect,
    this.questionId,
    this.createdAt,
    this.updatedAt,
  });

  factory Option.fromJson(Map<String, dynamic> json) => Option(
        id: json["id"],
        name: json["name"],
        icon: json["icon"],
        isCorrect: json["isCorrect"],
        questionId: json["question_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "icon": icon,
        "isCorrect": isCorrect,
        "question_id": questionId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class TestModels {
  int? id;
  String? name;
  bool? enabled;
  List<Child>? children;
  dynamic code;

  TestModels({
    this.id,
    this.name,
    this.enabled,
    this.children,
    this.code,
  });

  factory TestModels.fromJson(Map<String, dynamic> json) => TestModels(
        id: json["id"],
        name: json["name"],
        enabled: json["enabled"],
        children: json["children"] == null
            ? []
            : List<Child>.from(json["children"]!.map((x) => Child.fromJson(x))),
        code: json["code"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "enabled": enabled,
        "children": children == null
            ? []
            : List<dynamic>.from(children!.map((x) => x.toJson())),
        "code": code,
      };
}

class Child {
  int? id;
  String? name;
  bool? enabled;
  String? code;
  String? description;
  String? buttonText;
  String? gradientBackground;
  String? gradientBorder;
  String? icon;
  String? imageUrl;
  String? actionType;
  int? isMobile;
  int? isBo;
  int? position;
  int? count;

  Child({
    this.id,
    this.name,
    this.enabled,
    this.code,
    this.description,
    this.buttonText,
    this.gradientBackground,
    this.gradientBorder,
    this.icon,
    this.imageUrl,
    this.actionType,
    this.isMobile,
    this.isBo,
    this.position,
    this.count,
  });

  factory Child.fromJson(Map<String, dynamic> json) => Child(
        id: json["id"],
        name: json["name"],
        enabled: json["enabled"],
        code: json["code"],
        description: json["description"],
        buttonText: json["button_text"],
        gradientBackground: json["gradient_background"],
        gradientBorder: json["gradient_border"],
        icon: json["icon"],
        imageUrl: json["image_url"],
        actionType: json["action_type"],
        isMobile: json["is_mobile"],
        isBo: json["is_bo"],
        position: json["position"],
        count: json["count"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "enabled": enabled,
        "code": code,
        "description": description,
        "button_text": buttonText,
        "gradient_background": gradientBackground,
        "gradient_border": gradientBorder,
        "icon": icon,
        "image_url": imageUrl,
        "action_type": actionType,
        "is_mobile": isMobile,
        "is_bo": isBo,
        "position": position,
        "count": count,
      };
}
