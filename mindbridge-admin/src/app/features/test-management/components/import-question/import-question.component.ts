import { Component, computed, inject, OnInit, Signal } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatCheckbox } from '@angular/material/checkbox';
import { QuestionsStore } from '../../../questions-repository/questions.store';
import { QuestionResponse } from '../../../questions-repository/models/questions-response';
import { FormsModule } from '@angular/forms';
import {
  QuestionDisplayReadOnlyComponent
} from "../../../questions-repository/components/list-questions-repository/question-display-read-only/question-display-read-only.component";

@Component({
  selector: 'app-import-question',
  templateUrl: './import-question.component.html',
  styleUrl: './import-question.component.scss',
  imports: [MatCheckbox, FormsModule, QuestionDisplayReadOnlyComponent],
  providers: [QuestionsStore],
})
export class ImportQuestionComponent {
  private dialogRef =
    inject<MatDialogRef<ImportQuestionComponent>>(MatDialogRef);
  questionsStore = inject(QuestionsStore);
  questions: Signal<QuestionResponse[]> = this.questionsStore.state.questions;
  current_page: Signal<number> = this.questionsStore.state.current_page;
  total_pages: Signal<number> = computed(() =>
    Math.max(this.questionsStore.state.total_pages(), 1)
  );
  data: {
    category: number;
    matiere: number;
    niveau: number;
  } = inject(MAT_DIALOG_DATA);

  questionsChecked: QuestionResponse[] = [];
  title: string = '';
  constructor() {
    this.questionsStore.setSearchForm({
      ...this.questionsStore.state.questionsSearchForm(),
      ...this.data,
    });
    this.questionsStore.searchQuestions();
  }

  selectQuestion(event: any, question: QuestionResponse): void {
    if (event.checked && question) {
      this.questionsChecked.push(question);
    } else {
      // remove the question from the questionsChecked array
      this.questionsChecked = this.questionsChecked.filter(
        (q) => q.id !== question.id
      );
    }
  }

  close() {
    this.dialogRef.close();
  }

  save() {
    this.dialogRef.close(this.questionsChecked);
  }

  search() {
    this.questionsStore.setSearchForm({
      ...this.questionsStore.state.questionsSearchForm(),
      title: this.title,
    });
    this.questionsStore.searchQuestions();
  }
  goToPage(page: number) {
    this.questionsStore.setCurrentPage(page);
    this.search();
  }
}
