<div class="dashboard-container bg-[#F4F7FE] min-h-screen p-6">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-2xl font-bold text-[#2B3674] mb-2">Dashboard Santé Mentale</h1>
      <p class="text-[#A3AED0] text-sm">Surveillance des étudiants à risque et analyse des tests de santé mentale</p>
    </div>
    <button 
      mat-raised-button 
      color="primary" 
      (click)="refreshData()"
      [disabled]="dashboardLoading()"
      class="refresh-btn">
      <mat-icon>refresh</mat-icon>
      Actualiser
    </button>
  </div>

  <!-- Loading State -->
  @if (dashboardLoading()) {
    <div class="flex items-center justify-center py-12">
      <mat-spinner diameter="50"></mat-spinner>
      <span class="ml-4 text-[#A3AED0]">Chargement des données...</span>
    </div>
  }

  <!-- Error State -->
  @if (dashboardError() && !dashboardLoading()) {
    <mat-card class="error-card mb-6">
      <mat-card-content class="flex items-center">
        <mat-icon class="text-red-500 mr-3">error</mat-icon>
        <div>
          <h3 class="text-red-600 font-semibold">Erreur de chargement</h3>
          <p class="text-gray-600">{{ dashboardError() }}</p>
        </div>
        <button mat-button color="primary" (click)="refreshData()" class="ml-auto">
          Réessayer
        </button>
      </mat-card-content>
    </mat-card>
  }

  <!-- Dashboard Content -->
  @if (summary() && !dashboardLoading()) {
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total At-Risk Students -->
      <mat-card class="summary-card">
        <mat-card-content class="flex items-center p-5">
          <div class="icon-container bg-red-100 p-4 rounded-full mr-4">
            <mat-icon class="text-red-600">warning</mat-icon>
          </div>
          <div>
            <p class="text-[#A3AED0] text-sm">Étudiants à Risque</p>
            <p class="text-2xl font-bold text-[#2B3674]">{{ summary()!.total_at_risk_students }}</p>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Average Score -->
      <mat-card class="summary-card">
        <mat-card-content class="flex items-center p-5">
          <div class="icon-container bg-blue-100 p-4 rounded-full mr-4">
            <mat-icon class="text-blue-600">assessment</mat-icon>
          </div>
          <div>
            <p class="text-[#A3AED0] text-sm">Score Moyen</p>
            <p class="text-2xl font-bold text-[#2B3674]">{{ summary()!.average_score }}%</p>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Total Tests Completed -->
      <mat-card class="summary-card">
        <mat-card-content class="flex items-center p-5">
          <div class="icon-container bg-green-100 p-4 rounded-full mr-4">
            <mat-icon class="text-green-600">quiz</mat-icon>
          </div>
          <div>
            <p class="text-[#A3AED0] text-sm">Tests Complétés</p>
            <p class="text-2xl font-bold text-[#2B3674]">{{ summary()!.total_tests_completed }}</p>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Students Needing Attention -->
      <mat-card class="summary-card">
        <mat-card-content class="flex items-center p-5">
          <div class="icon-container bg-orange-100 p-4 rounded-full mr-4">
            <mat-icon class="text-orange-600">priority_high</mat-icon>
          </div>
          <div>
            <p class="text-[#A3AED0] text-sm">Attention Immédiate</p>
            <p class="text-2xl font-bold text-[#2B3674]">{{ summary()!.students_needing_attention }}</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Risk Distribution -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <mat-card class="risk-card" 
                [class.selected]="selectedRiskFilter() === 'high'"
                (click)="onRiskFilterChange(selectedRiskFilter() === 'high' ? undefined : 'high')">
        <mat-card-content class="p-5">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Risque Élevé</p>
              <p class="text-2xl font-bold text-red-600">{{ summary()!.risk_distribution.high_risk }}</p>
            </div>
            <div class="w-4 h-4 rounded-full" [style.background-color]="RISK_LEVEL_COLORS.high"></div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="risk-card" 
                [class.selected]="selectedRiskFilter() === 'medium'"
                (click)="onRiskFilterChange(selectedRiskFilter() === 'medium' ? undefined : 'medium')">
        <mat-card-content class="p-5">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Risque Moyen</p>
              <p class="text-2xl font-bold text-yellow-600">{{ summary()!.risk_distribution.medium_risk }}</p>
            </div>
            <div class="w-4 h-4 rounded-full" [style.background-color]="RISK_LEVEL_COLORS.medium"></div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="risk-card" 
                [class.selected]="selectedRiskFilter() === 'low'"
                (click)="onRiskFilterChange(selectedRiskFilter() === 'low' ? undefined : 'low')">
        <mat-card-content class="p-5">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Risque Faible</p>
              <p class="text-2xl font-bold text-green-600">{{ summary()!.risk_distribution.low_risk }}</p>
            </div>
            <div class="w-4 h-4 rounded-full" [style.background-color]="RISK_LEVEL_COLORS.low"></div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Students by Niveau Chart -->
      <mat-card class="chart-card">
        <mat-card-header>
          <mat-card-title class="text-lg font-semibold text-[#2B3674]">
            Étudiants à Risque par Niveau
          </mat-card-title>
        </mat-card-header>
        <mat-card-content class="chart-container">
          <div class="chart-wrapper" style="height: 300px;">
            <canvas 
              baseChart
              [data]="studentsByNiveauChartData()"
              [options]="chartOptions"
              [type]="barChartType">
            </canvas>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Trigger Distribution Chart -->
      <mat-card class="chart-card">
        <mat-card-header>
          <mat-card-title class="text-lg font-semibold text-[#2B3674]">
            Distribution des Déclencheurs
          </mat-card-title>
        </mat-card-header>
        <mat-card-content class="chart-container">
          <div class="chart-wrapper" style="height: 300px;">
            <canvas 
              baseChart
              [data]="triggerDistributionChartData()"
              [options]="pieChartOptions"
              [type]="pieChartType">
            </canvas>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Score Trends Chart -->
    <mat-card class="chart-card mb-8">
      <mat-card-header>
        <mat-card-title class="text-lg font-semibold text-[#2B3674]">
          Évolution des Scores dans le Temps
        </mat-card-title>
      </mat-card-header>
      <mat-card-content class="chart-container">
        <div class="chart-wrapper" style="height: 400px;">
          <canvas
            baseChart
            [data]="scoreTrendsChartData()"
            [options]="lineChartOptions"
            [type]="lineChartType">
          </canvas>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Students Table -->
    <mat-card class="students-table-card">
      <mat-card-header class="flex items-center justify-between">
        <mat-card-title class="text-lg font-semibold text-[#2B3674]">
          Liste des Étudiants à Risque
          @if (selectedRiskFilter()) {
            <mat-chip class="ml-2" [style.background-color]="getRiskLevelColor(selectedRiskFilter()!)" 
                     [style.color]="'white'">
              {{ RISK_LEVEL_LABELS[selectedRiskFilter()!] }}
            </mat-chip>
          }
        </mat-card-title>
        @if (selectedRiskFilter()) {
          <button mat-button (click)="onRiskFilterChange(undefined)">
            <mat-icon>clear</mat-icon>
            Effacer le filtre
          </button>
        }
      </mat-card-header>
      <mat-card-content>
        <div class="table-container">
          <table mat-table [dataSource]="students()" class="w-full">
            <!-- Student Name Column -->
            <ng-container matColumnDef="student_name">
              <th mat-header-cell *matHeaderCellDef class="font-semibold">Nom de l'Étudiant</th>
              <td mat-cell *matCellDef="let student">{{ student.student_name }}</td>
            </ng-container>

            <!-- Niveau Column -->
            <ng-container matColumnDef="niveau">
              <th mat-header-cell *matHeaderCellDef class="font-semibold">Niveau</th>
              <td mat-cell *matCellDef="let student">{{ student.niveau }}</td>
            </ng-container>

            <!-- Test Count Column -->
            <ng-container matColumnDef="test_count">
              <th mat-header-cell *matHeaderCellDef class="font-semibold">Nb Tests</th>
              <td mat-cell *matCellDef="let student">{{ student.test_count }}</td>
            </ng-container>

            <!-- Average Score Column -->
            <ng-container matColumnDef="average_score">
              <th mat-header-cell *matHeaderCellDef class="font-semibold">Score Moyen</th>
              <td mat-cell *matCellDef="let student">{{ student.average_score }}%</td>
            </ng-container>

            <!-- Lowest Score Column -->
            <ng-container matColumnDef="lowest_score">
              <th mat-header-cell *matHeaderCellDef class="font-semibold">Score Min</th>
              <td mat-cell *matCellDef="let student">{{ student.lowest_score }}%</td>
            </ng-container>

            <!-- Risk Level Column -->
            <ng-container matColumnDef="risk_level">
              <th mat-header-cell *matHeaderCellDef class="font-semibold">Niveau de Risque</th>
              <td mat-cell *matCellDef="let student">
                <mat-chip [style.background-color]="getRiskLevelColor(student.risk_level)" 
                         [style.color]="'white'">
                  {{ RISK_LEVEL_LABELS[student.risk_level] }}
                </mat-chip>
              </td>
            </ng-container>

            <!-- Last Test Date Column -->
            <ng-container matColumnDef="last_test_date">
              <th mat-header-cell *matHeaderCellDef class="font-semibold">Dernier Test</th>
              <td mat-cell *matCellDef="let student">{{ formatDate(student.last_test_date) }}</td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef class="font-semibold">Actions</th>
              <td mat-cell *matCellDef="let student">
                <button mat-icon-button [routerLink]="['/etudiant', student.etudiant_id]" 
                        matTooltip="Voir le profil de l'étudiant">
                  <mat-icon>visibility</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
                [class]="getRiskLevelClass(row.risk_level)"></tr>
          </table>
        </div>
      </mat-card-content>
    </mat-card>
  }
</div>
