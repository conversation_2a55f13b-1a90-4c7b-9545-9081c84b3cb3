<?php

namespace App\Http\Controllers\MindBridge;

use App\Http\Controllers\Controller;
use App\Models\MindBridge\Option;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class OptionController extends Controller
{
    public function index(Request $request)
    {
       $options = Option::orderBy('created_at', 'desc')->get();
       return response()->json($options);
    }

    public function store(Request $request)
    {
        try {
            $request->validate( [
                'name' => 'required:string',
                'question_id' => 'required|exists:questions,id',
            ]);
            return Option::create($request->all());
        } catch (ValidationException $e) {
            return response()->json($e->validator->errors(), 422);
        }catch (ModelNotFoundException $e) { 
            return response()->json($e->getMessage(),0);
         }
    } 

    public function show($id)
    {
        try{
            $option = Option::findOrFail($id);
            return response()->json($option, 200);
        }catch (ModelNotFoundException $e) {
            return response()->json([
                'error'=> $e->getMessage(),
                'message' => 'Option not found'
            ],404);
        }catch (Exception $e) {
            Log::error($e);
            return response()->json($e->getMessage(), 500);
        }
    }

    public function update($id, Request $request)
    {
        try {
           $option = Option::findOrFail($id);

           $option->update($request->only(['name', 'target', 'type']));
            return response()->json($option, 200);

        } catch (ModelNotFoundException $e) {
            \Log::error("Test not found: ID {$id}", ['exception' => $e]);
            return response()->json([
                'error' => 'Test not found.',
            ], 404);
        } catch (Exception $e) {
            \Log::error("Unexpected error in OptionController@store: ", ['exception' => $e]);
            return response()->json([
                'error' => 'An unexpected error occurred.',
            ], 500);
        }
    }

     public function destroy($id)
     {
         try {
            $option = Option::find($id);
            $option->delete();
             return response()->json([
                 'message' => 'Option deleted successfully'
             ], 204);
         } catch (Exception $e) {
             Log::error($e);
         }
     }
}
