import { HostListener, Injectable, inject } from "@angular/core";
import { Store } from "@ngrx/store";
import { TestManagementService } from "../services/test-management.services";
import { catchError, finalize, map, switchMap, tap } from 'rxjs/operators';
import { of } from "rxjs";
import { AppState } from "../../../core/app.state";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { CommonService } from "../../shared/common.service";
import { SetLoading } from "../../shared/store/shared.actions";
import * as ListsTestsManagement from "../store/test-management.actions";
import { HttpErrorResponse } from "@angular/common/http";

@Injectable()
export class TestManagementEffects {
    private actions$ = inject(Actions);
    store = inject<Store<AppState>>(Store);
    private testManagementService = inject(TestManagementService);
    private commonService = inject(CommonService);


    isScreenSizeValid(): boolean {
        return window.innerWidth >= 600;
    }

    @HostListener('window:resize', ['$event'])
    onResize() {
        this.isScreenSizeValid();
    }

    // tests effects
    AllTestsEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ListsTestsManagement.AllTestsManagement),
            switchMap((action) =>
                this.testManagementService.AllTestsManagement(action.payload).pipe(
                    map((testsManagement: any) => ListsTestsManagement.AllTestsManagementSuccess({ testsManagement })),
                    catchError(() =>
                        of(
                            ListsTestsManagement.AllTestsManagementError()
                        ).pipe(
                            tap(() => this.commonService.showToast('Credentials error!', 'ERROR'))
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    )

    AddTestsEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ListsTestsManagement.AddTestManagement),
            switchMap((action) =>
                this.testManagementService.AddTestMangement(action.payload).pipe(
                    map((testManagement: any) => ListsTestsManagement.AddTestManagementSuccess({ testManagement })),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            ListsTestsManagement.AddTestManagementError()
                        ).pipe(
                            tap(() => {
                                console.log('err', err.error);
                                
                                this.commonService.showToast(err.error, 'ERROR')
                            })
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    )
}