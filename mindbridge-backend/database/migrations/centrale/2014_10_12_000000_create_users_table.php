<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\SoftDeletes;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->nullable()->unique();
            $table->string('phone')->unique();
            $table->string('password')->nullable();
            $table->string('avatar')->default('users/profiles/dev-user-avatar.png');
            $table->boolean('active')->default(true);
            $table->boolean('is_mind_bridge_user')->default(false);
            $table->enum('type',['parrent','enseignant','admin', 'etudiant']);
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
            $table->string('fcm_token')->nullable();
            $table->enum('status',['active','inactive'])->default('active');
            $table->index('name');
            $table->index('phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
