<div class="bg-gray-50 px-7 pb-4 pt-7">
  <h2>{{ testModels().name }}</h2>
  <div
    class="flex-center max-md: mt-4 grid grid-cols-5 gap-2 max-md:grid-cols-3"
  >
    @for (testCategory of testModels().children; track testCategory.id) {
      <div
        class="ml-4 mt-2 flex w-full flex-col items-center justify-start rounded-lg border border-gray-200 bg-white pt-0"
        [ngClass]="{
          'cursor-pointer': testCategory.enabled,
          'cursor-not-allowed grayscale filter': !testCategory.enabled,
        }"
      >
        <div
          class="space-2 mt-1 flex w-full flex-col items-center gap-1 py-2"
          (click)="testCategory.enabled ? goToAddTest(testCategory.code) : null"
        >
          <img
            class="mx-4 h-12 w-12 max-md:h-6 max-md:w-6"
            src="{{ testCategory.icon }}"
            alt="{{ testCategory.name }}"
          />
          <div class="w-full">
            <hr class="border-t border-gray-200" />
          </div>
          <div class="m-1 text-center text-gray-500 max-md:text-xs">
            {{ testCategory.name }}
          </div>
        </div>
      </div>
    }
  </div>

  <div class="mt-4 flex flex-col items-center">
    <form
      class="flex w-full flex-col items-center justify-end md:flex-row"
      [formGroup]="form"
      (ngSubmit)="searchTests()"
    >
      <h2 class="flex-1 text-left">Tests</h2>
      <div class="my-4 flex flex-col items-end justify-end gap-2 md:flex-row">
        <div class="rounded-lg border-2 border-gray-200 bg-white p-2">
          <select
            class="w-40"
            formControlName="category_id"
            (change)="searchTests()"
          >
            <option [ngValue]="OPTION_ALL" selected>
              Toutes les catégories
            </option>
            @for (
              testCategory of testModels().children;
              track testCategory.id
            ) {
              <option [ngValue]="testCategory.id">
                {{ testCategory.name }}
              </option>
            }
          </select>
        </div>
        <div class="rounded-lg border-2 border-gray-200 bg-white p-2">
          <select
            class="w-40"
            formControlName="matiere"
            (change)="matiereChange()"
          >
            <option [ngValue]="OPTION_ALL" selected>Toutes les matières</option>
            @for (matiere of allMatieres(); track matiere.id) {
              <option [ngValue]="matiere.id">{{ matiere.name_fr }}</option>
            }
          </select>
        </div>
        <div class="rounded-lg border-2 border-gray-200 bg-white p-2">
          <select
            class="w-40"
            formControlName="niveau"
            (change)="searchTests()"
          >
            <option [ngValue]="OPTION_ALL" selected>Tous les niveaux</option>
            @for (niveau of allNiveauxByMatiere(); track niveau.id) {
              <option [ngValue]="niveau.id">{{ niveau.name }}</option>
            }
          </select>
        </div>
      </div>
    </form>
    @if (tests()) {
      <div class="container mx-auto mt-2 max-w-screen-xl">
        <div class="overflow-x-auto rounded-lg border shadow-md">
          <table class="min-w-full divide-y divide-gray-200 bg-white">
            <tr class="flex w-full items-start justify-start text-left">
              <th class="w-40 px-4 py-3 font-medium">Titre</th>
              <th class="w-48 px-4 py-3 font-medium">Description</th>
              <th class="w-40 px-4 py-3 font-medium">Catégorie</th>
              <th class="w-28 px-4 py-3 font-medium">Niveau</th>
              <th class="w-28 px-4 py-3 font-medium">Matière</th>
              <th class="w-28 px-4 py-3 font-medium">Crée par</th>
              <th class="w-28 px-4 py-3 font-medium">Modifié le</th>
              <th class="w-20 flex-1 py-3 pr-6 text-right font-medium"></th>
            </tr>
            @for (test of tests(); track test.id) {
              <tr class="flex w-full items-start justify-start text-left">
                <td class="w-40 px-4 py-3 font-medium">{{ test.title }}</td>
                <td class="w-48 px-4 py-3 font-medium">
                  {{ test.description }}
                </td>
                <td class="w-40 px-4 py-3 font-medium">
                  {{ test?.category?.name ?? '--' }}
                </td>
                <td class="w-28 px-4 py-3 font-medium">
                  {{ test?.niveau?.name ?? '--' }}
                </td>
                <td class="w-28 px-4 py-3 font-medium">
                  {{ test?.matiere?.name_fr ?? '--' }}
                </td>
                <td class="w-28 px-4 py-3 font-medium">
                  {{ test?.creator?.name ?? '--' }}
                </td>
                <td class="w-28 px-4 py-3 font-medium">
                  {{ test.updated_at | date }}
                </td>
                <td
                  class="flex w-20 flex-1 items-end justify-end gap-2 py-3 text-right font-medium"
                >
                  <button (click)="viewTest(test)">
                    <img class="" src="assets/icones/visibility.png" alt="" />
                  </button>
                  <button (click)="editTest(test)">
                    <img class="" src="assets/icones/edit.png" alt="" />
                  </button>
                  <button class="me-2 p-0.5" (click)="deleteTest(test.id)">
                    <img class="" src="assets/icones/delete.png" alt="" />
                  </button>
                </td>
              </tr>
            }
          </table>
          <hr />
          <div class="flex items-start justify-start gap-2 bg-white px-4 py-4">
            <div class="rounded-lg border border-gray-300 bg-white p-1.5">
              <button
                class="w-16 text-center font-medium"
                [disabled]="current_page() === 1"
                (click)="goToPage(current_page() - 1)"
              >
                Précédent
              </button>
            </div>
            <div class="rounded-lg border border-gray-300 bg-white p-1.5">
              <button
                class="w-14 text-center font-medium"
                [disabled]="current_page() === total_pages()"
                (click)="goToPage(current_page() + 1)"
              >
                Suivant
              </button>
            </div>
            <p class="flex-1 p-2 text-right font-medium">
              Page {{ current_page() }} de {{ total_pages() }}
            </p>
          </div>
        </div>
      </div>
    }
  </div>
</div>
