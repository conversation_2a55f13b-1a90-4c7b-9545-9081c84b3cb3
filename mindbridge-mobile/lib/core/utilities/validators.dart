class Validators {
  static String? validateEmail(String value) {
    if (value.isEmpty) {
      return 'L\'email est requis';
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Veuillez entrer une adresse email valide';
    }
    return null;
  }

  static String? validatePassword(String value) {
    if (value.isEmpty) {
      return 'Le mot de passe est requis';
    }
    if (value.length < 4) {
      return 'Le mot de passe doit contenir au moins 4 caractères';
    }
    return null;
  }

  static bool validateSelection(List<int> selectedChoices) {
    return selectedChoices.isNotEmpty;
  }
}
