<?php

namespace App\Models\MindBridge;

use App\Models\MatiereCentrale;
use App\Models\NiveauCentrale;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Content extends Model
{
    use HasFactory;
    protected $connection = 'mind_bridge';
    protected $table = 'contents';
    
    protected $fillable = [
        "type",
        'title',
        "content",
        'description',
        "chapter_id",
        "niveau_id",
        "matiere_id"
    ];

    public function niveau()
    {
        return $this->belongsTo(NiveauCentrale::class, 'niveau_id');
    }

    public function matiere()
    {
        return $this->belongsTo(MatiereCentrale::class, 'matiere_id');
    }

    public function chapter()
    {
        return $this->belongsTo(Chapter::class, 'chapter_id');
    }

    
}
