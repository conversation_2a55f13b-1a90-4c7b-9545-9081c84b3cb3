.container_notification {
  width: 425px;
  min-height: 62px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgb(218 23 23 / 77%);
  border-radius: 4px;
  padding: 10px;
}

.box_btn_and_notifie {
  width: 85%;
  height: 100%;
  display: flex;
  align-items: center;
}
.place_texts {
  width: 80%;
  height: 54%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.text_notie1 {
  margin: 0 !important;
  color: #212529;
}
.btn_close_notie {
  width: 14px;
  height: 14px;
  margin: auto;
  color: #595959;
  cursor: pointer;
}

///
.container_notification_success {
  width: 425px;
  height: 67px;
  display: flex;
  justify-content: space-between;
  background-color: rgb(11 111 27 / 79%);
}

.box_btn_and_notifie {
  width: 85%;
  height: 100%;
  display: flex;
  align-items: center;
}
.icon_self {
  width: 30px;
  height: 30px;
  margin: 0 12px 0 5px;
}
.icon_true {
  width: 100%;
  height: 100%;
}
.place_texts_success {
  width: 80%;
  height: 54%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.text_notie1_success {
  margin: 0 !important;
  color: #212529;
}
.btn_close_notie_success {
  width: 14px;
  height: 14px;
  margin: auto;
  color: #06AB6B;
  cursor: pointer;
}
.span_toast{
  color: white;
  font-size: 16px;
}

.ml13 {
  margin-left: 13px;
}