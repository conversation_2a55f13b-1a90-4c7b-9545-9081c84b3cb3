// import 'package:flutter/material.dart';

// class CardData {
//   final String title;
//   final Color? titleColor;
//   final String imagePath;
//   final List<Color> gradientColors;
//   final List<Color> borderGradientColors;
//   final String buttonText;
//   final VoidCallback? onPressed;

//   CardData({
//     required this.title,
//     this.titleColor = Colors.white,
//     required this.imagePath,
//     required this.gradientColors,
//     required this.borderGradientColors,
//     required this.buttonText,
//     this.onPressed,
//   });
// }
