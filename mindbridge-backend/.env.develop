APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:EDKpxYL0n/+c7oTF+lEEZemmUJOlqywg+/yukvGmosg=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=centrale
DB_HOST=db
DB_PORT=3306
DB_DATABASE=centrale
DB_USERNAME=root
DB_PASSWORD=password

DB_DATABASE_MINDBRIDGE=mind_bridge


CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# BANKILY_BASE_URL = 'https://ebankily.appspot.com/'
#BANKILY_BASE_URL='https://ebankily-tst.appspot.com/'
#CLIENT_ID='ebankily'
#USERNAME='ELMOURAD_CRF'
#PASSWORD='12345'
#GRANT_TYPE='password'

BANKILY_BASE_URL = 'https://ebankily.appspot.com/'
CLIENT_ID = 'ebankily'
USERNAME = 'elmourad_crf'
PASSWORD = '0fdd635d-de7b-4d37-b560-1df57044d936'
GRANT_TYPE= 'password'


SERVICE_NOTIF_URL="https://schoolyg-notif.awlyg.tech"

SANCTUM_EXPIRES_IN=720