<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chapter_test', function (Blueprint $table) {
            $table->id();

            $table->foreignId('test_id')
                  ->constrained('tests')
                  ->cascadeOnDelete();

            $table->foreignId('chapter_id')
                  ->constrained('chapters')
                  ->cascadeOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chapter_test');
    }
};
