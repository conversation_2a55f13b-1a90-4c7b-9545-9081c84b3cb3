import 'package:flutter/material.dart';

import '../../../core/constants/api_routes.dart';
import '../../core/models/evaluation.dart';
import '../providers/remote/dio_client.dart';

class EvaluationResponse {
  final Evaluation? data;
  final List<Evaluation>? dataList;
  final bool isEmpty;
  final bool isError;

  EvaluationResponse({
    this.data,
    this.dataList,
    this.isEmpty = false,
    this.isError = false,
  });
}

class EvaluationRepository {
  final DioClient _dioClient;

  EvaluationRepository({required DioClient dioClient}) : _dioClient = dioClient;

  Future<EvaluationResponse> fetchEvaluation(int contentId, bool isLesonCompleted) async {
    try {
      final url = '${ApiRoutes.evaluation}?content_id=$contentId&lesson_completed=$isLesonCompleted';
      final response = await _dioClient.dio.get(url);

      if (response.statusCode == 200) {
        if (response.data == null ||
            response.data['test'] == null ||
            (response.data['test']['steps'] as List).isEmpty) {
          return EvaluationResponse(isEmpty: true);
        }
        return EvaluationResponse(data: Evaluation.fromJson(response.data['test']));
      }else if (response.statusCode == 404 || response.statusCode == 403) {
        return EvaluationResponse(isEmpty: true);
      }
    } catch (e) {
      print("Error fetching evaluation: $e");
      return EvaluationResponse(isError: true);
    }
    return EvaluationResponse(isError: true);
  }

  Future<Map<String, dynamic>?> submitEvaluation(
      Map<String, dynamic> payload) async {
    try {
      final response =
          await _dioClient.dio.post(ApiRoutes.evaluationSubmit, data: payload);

      if (response.statusCode == 200 && response.data != null) {
        return response.data;
      }
    } catch (e) {
      debugPrint("Error submitting evaluation: $e");
    }
    return null;
  }


  Future<EvaluationResponse> fetchChallengeHebdomadaire() async {
    try {
      final response = await _dioClient.dio.get(ApiRoutes.challengeHebdomadaire);

      if (response.statusCode == 200) {
        if (response.data == null ||
            response.data['tests'] == null ||
            (response.data['tests'] as List).isEmpty) {
          return EvaluationResponse(isEmpty: true);
        }
        return EvaluationResponse(dataList: List<Evaluation>.from(
            response.data['tests'].map((x) => Evaluation.fromJson(x))));
      }else if (response.statusCode == 404 || response.statusCode == 403) {
        return EvaluationResponse(isEmpty: true);
      }
    } catch (e) {
      print("Error fetching ApiRoutes.challengeHebdomadaire: $e");
      return EvaluationResponse(isError: true);
    }
    return EvaluationResponse(isError: true);
  }
}
