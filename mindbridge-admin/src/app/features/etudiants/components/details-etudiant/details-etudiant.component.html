<div
  class="w-12/12 m-6 flex h-full flex-col items-center justify-between rounded-lg border border-gray-200 bg-white px-8 py-4 max-md:m-2 max-md:gap-4 md:flex-row"
>
  <div class="flex flex-row">
    <img
      class="mr-1 h-16 w-16 rounded-full object-cover"
      [src]="etudiant()?.avatar || 'assets/icones/etudiant-avatar.png'"
    />
    <div class="flex flex-col">
      <div class="text-lg font-semibold">
        {{ etudiant()?.first_name }} {{ etudiant()?.last_name }}
      </div>
      <span class="mb-2 text-[#475467]">{{ etudiant()?.email }}</span>
      <span class="text-[#475467]">
        Niveau :
        <span
          class="rounded-xl px-2 py-1 text-xs"
          [ngStyle]="{
            color: etudiant()?.niveau.color,
            'background-color': etudiant()?.niveau.background,
          }"
        >
          {{ etudiant()?.niveau?.name }}
        </span>
      </span>
    </div>
  </div>

  <div class="flex flex-col items-center gap-4">
    <!-- SVG Circular Progress -->
    <div class="relative h-16 w-16">
      <svg
        class="h-max w-max -rotate-0 transform"
        viewBox="-0.5 -0.5 37 37"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#6BA4BD" />
            <stop offset="100%" stop-color="#7E588E" />
          </linearGradient>
        </defs>
        <!-- Background Circle -->
        <circle
          class="text-gray-200"
          stroke="currentColor"
          stroke-width="5"
          fill="none"
          r="16"
          cx="18"
          cy="18"
        />
        <!-- Progress Circle -->
        <circle
          stroke="url(#gradient)"
          stroke-width="5"
          stroke-linecap="round"
          fill="none"
          r="16"
          cx="18"
          cy="18"
          [attr.stroke-dasharray]="circumference"
          [attr.stroke-dashoffset]="dashOffset"
        />
      </svg>

      <!-- Percentage Label -->
      <div class="absolute inset-0 flex items-center justify-center text-xs">
        {{ etudiant()?.taux_engagement }}%
      </div>
    </div>

    <p class="mt-4 text-sm font-semibold text-gray-800">Taux d’engagement</p>
  </div>

  <div class="flex flex-col items-center">
    <img class="h-12 w-12" src="assets/icones/temps-utilisation.png" alt="" />
    <span class="mt-2 font-semibold">
      {{ etudiant()?.heure_moyen }}h / jour
    </span>
    <span class="mt-0.5 text-[#5D6370]">Temps d’utilisation MindBridge</span>
  </div>
</div>

<div
  class="w-12/12 m-6 flex h-full flex-col items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-4 max-md:m-2"
>
  <div class="mb-3 flex w-full flex-row items-center justify-between">
    <span class="text-base font-semibold">Information personnel</span>
    <button
      class="flex flex-row rounded-lg bg-gradient-to-b from-[#F0F6F8] via-[#E1EDF2] to-[#F2EEF4] px-4 py-2 font-semibold text-[#568397]"
    >
      <img class="text-xs" src="assets/icones/edit-2.png" alt="" />
      <span class="text-sm">Edit</span>
    </button>
  </div>
  <div class="mb-3 flex w-full flex-row items-center justify-start">
    <div class="flex w-80 flex-col">
      <span class="mb-1.5 font-semibold">{{ etudiant()?.first_name }}</span>
      <span class="text-[#475467]">Prénom</span>
    </div>
    <div class="flex w-80 flex-col">
      <span class="mb-1.5 font-semibold">{{ etudiant()?.last_name }}</span>
      <span class="text-[#475467]">Nom</span>
    </div>
    <div class="flex w-80 flex-col">
      <span class="mb-1.5 font-semibold">{{ etudiant()?.email }}</span>
      <span class="text-[#475467]">Email adress</span>
    </div>
  </div>
  <div class="flex w-full flex-row items-center justify-start">
    <div class="flex w-80 flex-col">
      <span class="mb-1.5 font-semibold">{{ etudiant()?.niveau.name }}</span>
      <span class="text-[#475467]">Niveau actuel</span>
    </div>
    <div class="flex w-80 flex-col">
      <span class="mb-1.5 font-semibold">{{ etudiant()?.school.name }}</span>
      <span class="text-[#475467]">Etablissement</span>
    </div>
    <div class="flex w-80 flex-col">
      <span class="mb-1.5 font-semibold">---</span>
      <span class="flex flex-row">
        <span class="text-[#475467]">Test mentale avec problème</span>
        <button class="flex flex-row text-xs text-[#568397]">
          <span>Voir test</span>
          <img src="assets/icones/retour.png" />
        </button>
      </span>
    </div>
  </div>
</div>

<!-- Student Test History Section -->
<app-student-test-history [etudiantId]="id()" />

