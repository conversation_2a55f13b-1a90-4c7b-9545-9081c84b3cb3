import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { MatiereResponse, NiveauMini } from './models/matiere-response.model';
import { Matiere } from './models/matiere.model';
import { Chapitre } from './models/chapitre.model';
import { Contenue } from './models/contenue.model';
import { getQuery, Pagination } from '../shared/helpers/query.helper';
import { ContenueResponse } from './models/contenue-response.model';
import { ChapterResponse } from './models/chapter-response.model';
import { ChapterRequest } from './models/chapter-request.model';
import {
  ApiResponse,
  ApiResponseNoPagination,
} from '../../core/models/api-response';
import { MatieresByNiveauResponseWrapper } from './models/niveau-response.model';
import { IContentSearchForm } from './models/content-search-request';

@Injectable({
  providedIn: 'root',
})
export class MatieresService {
  private http = inject(HttpClient);

  getListeMatieres(): Observable<ApiResponse<MatiereResponse>> {
    return this.http.get<ApiResponse<MatiereResponse>>(
      environment.BASE_URL_API + 'mind_bridge/matieres?loadRelation=true'
    );
  }

  // ===== Matieres =====
  allMatieres(): Observable<ApiResponseNoPagination<MatiereResponse>> {
    return this.http.get<ApiResponseNoPagination<MatiereResponse>>(
      environment.BASE_URL_API + 'mind_bridge/subjects'
    );
  }

  addMatiere(payload: Matiere): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/matieres',
      payload
    );
  }

  updateMatiere(payload: Matiere, id: number): Observable<any> {
    // Remove id from payload
    const { id: _, ...updatedPayload } = payload;

    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/matieres/' + id,
      payload
    );
  }

  deleteMatiere(id: number): Observable<any> {
    return this.http.delete<any>(
      environment.BASE_URL_API + 'mind_bridge/matieres/' + id
    );
  }

  // ===== Niveaux =====
  allNiveaus(): Observable<ApiResponseNoPagination<NiveauMini>> {
    return this.http.get<ApiResponseNoPagination<NiveauMini>>(
      environment.BASE_URL_API + 'mind_bridge/levels'
    );
  }

  // ===== Chapitres =====
  allChapitres(): Observable<any> {
    return this.http.get<any>(
      environment.BASE_URL_API + 'mind_bridge/chapters'
    );
  }

  addChapitre(payload: Chapitre): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/chapters',
      payload
    );
  }

  updateChapitre(payload: Chapitre, id: number): Observable<any> {
    // Remove id from payload
    const { id: _, ...updatedPayload } = payload;

    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/chapters/' + id,
      payload
    );
  }

  deleteChapitre(id: number): Observable<any> {
    return this.http.delete<any>(
      environment.BASE_URL_API + 'mind_bridge/chapters/' + id
    );
  }

  // ===== Contents =====
  allContenues(): Observable<any> {
    return this.http.get<any>(
      environment.BASE_URL_API + 'mind_bridge/contents'
    );
  }

  matiereContentsByJustMatriereId(
    matiere_id: number
  ): Observable<ApiResponse<ContenueResponse>> {
    const params: HttpParams = getQuery({ matiere_id });
    return this.http.get<ApiResponse<ContenueResponse>>(
      environment.BASE_URL_API + 'mind_bridge/matiere-contents',
      { params }
    );
  }

  matiereContents(
    matiere_id: number,
    searchForm: IContentSearchForm,
    pagination: Pagination
  ): Observable<ApiResponse<ContenueResponse>> {
    const { niveau, chapter } = searchForm;
    const params: HttpParams = getQuery(
      { matiere_id, niveau, chapter },
      pagination
    );
    return this.http.get<ApiResponse<ContenueResponse>>(
      environment.BASE_URL_API + 'mind_bridge/matiere-contents',
      { params }
    );
  }

  addContenue(payload: FormData): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/contents',
      payload
    );
  }

  updateContenue(payload: Contenue, id: number): Observable<any> {
    // Remove id from payload
    const { id: _, ...updatedPayload } = payload;

    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/contents/' + id,
      payload
    );
  }

  deleteContenue(id: number): Observable<any> {
    return this.http.delete<any>(
      environment.BASE_URL_API + 'mind_bridge/contents/' + id
    );
  }

  allNiveauxByMatiere(matiere_id: number): Observable<MatiereResponse> {
    return this.http.get<MatiereResponse>(
      environment.BASE_URL_API + 'mind_bridge/niveaux/matiere/' + matiere_id
    );
  }
  getAllMatieresByNiveau(
    niveau_id: number
  ): Observable<MatieresByNiveauResponseWrapper> {
    return this.http.get<MatieresByNiveauResponseWrapper>(
      environment.BASE_URL_API + 'mind_bridge/matieres/niveau/' + niveau_id
    );
  }

  chapterByMatiereAndNiveau(
    payload: ChapterRequest
  ): Observable<ChapterResponse[]> {
    const params: HttpParams = getQuery(payload);
    return this.http.get<ChapterResponse[]>(
      environment.BASE_URL_API + 'mind_bridge/chapters/niveau-matiere',
      { params }
    );
  }
}
