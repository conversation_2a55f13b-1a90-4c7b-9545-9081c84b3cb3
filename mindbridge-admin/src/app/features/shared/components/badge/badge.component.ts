import { Component, input, InputSignal } from '@angular/core';
import {BadgeNiveauEnum} from "../../enums/badge-niveau.enum";

@Component({
  selector: 'app-badge',
  standalone: true,
  imports: [],
  template: `
    <p
      class="m-0 w-fit rounded-lg px-2.5 py-0.5 text-xs font-medium"
      [class.badge-septieme]="niveau() === BadgeNiveauEnum.septieme"
      [class.badge-huitieme]="niveau() === BadgeNiveauEnum.huitieme"
      [class.badge-neuvieme]="niveau() === BadgeNiveauEnum.neuvieme"
    >
      {{ content() }}
    </p>
  `,
  styles: `
    .badge-septieme {
      background-color: #F9F5FF;
      color: #6941C6;
    }

    .badge-huitieme {
      background-color: #EFF8FF;
      color: #175CD3;
    }

    .badge-neuvieme {
      background-color: #EEF4FF;
      color: #3538CD;
    }
  `,
})
export class BadgeComponent {
  content: InputSignal<string> = input.required<string>();
  niveau: InputSignal<BadgeNiveauEnum> = input.required<BadgeNiveauEnum>();
  protected readonly BadgeNiveauEnum = BadgeNiveauEnum;
}
