import { FormControl } from '@angular/forms';
import { OPTION_ALL } from '../../shared/global.config';

export interface IEtudiantSearchForm {
  school: number;
  niveau: number;
}

export class EtudiantSearchForm implements IEtudiantSearchForm {
  school: number;
  niveau: number;
  constructor() {
    this.school = OPTION_ALL;
    this.niveau = OPTION_ALL;
  }
}

export interface IEtudiantSearchFormGroup {
  school: FormControl<number>;
  niveau: FormControl<number>;
}

export class EtudiantSearchFormGroup implements IEtudiantSearchFormGroup {
  school: FormControl<number>;
  niveau: FormControl<number>;

  constructor(
    etudiantSearchForm: IEtudiantSearchForm = new EtudiantSearchForm()
  ) {
    this.school = new FormControl<number>(etudiantSearchForm.school);
    this.niveau = new FormControl<number>(etudiantSearchForm.niveau);
  }
}
