import { Component, inject, Signal } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from './core/app.state';
import { RouterOutlet } from '@angular/router';
import { LoaderComponent } from './features/shared/loader/loader.component';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  imports: [RouterOutlet, LoaderComponent],
})
export class AppComponent {
  private store = inject<Store<AppState>>(Store);

  title = 'mindBridge-bo';
  isLoading: Signal<boolean> = this.store.selectSignal(
    (state) => state.shared.isAppLoading
  );
}
