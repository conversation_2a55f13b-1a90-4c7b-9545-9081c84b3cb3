name: mindbridge
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+6

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  animated_text_kit: ^4.2.2
  audio_waveforms: ^1.2.0
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  confetti: ^0.8.0
  cupertino_icons: ^1.0.6
  curved_navigation_bar: ^1.0.6
  dio: ^5.7.0
  file_picker: ^8.1.7
  firebase_core: ^3.10.1
  firebase_messaging: ^15.2.1
  flutter:
    sdk: flutter
  flutter_easyloading: ^3.0.5
  flutter_launcher_icons: ^0.14.2
  flutter_local_notifications: ^18.0.1
  flutter_pdfview: ^1.3.4
  flutter_secure_storage: ^9.2.4
  flutter_svg: ^2.0.16
  get: ^4.6.5
  gradient_borders: ^1.0.1
  intl: ^0.20.1
  lottie: ^3.1.3
  path_provider: ^2.1.5
  random_avatar: ^0.0.8
  record: ^5.2.0
  shimmer: ^3.0.0
  sizer: ^2.0.15
  skeletonizer: ^2.1.0

dev_dependencies:
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter
flutter_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/logo.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/images/logo.png"

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/json/
