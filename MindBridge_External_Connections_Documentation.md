# MindBridge External Connections & Integrations Documentation

## Overview
This document provides comprehensive details about all external connections, third-party services, APIs, and integrations used in the MindBridge application ecosystem.

## 🔗 External Service Integrations

### 1. Firebase Services (Google Cloud)

#### Firebase Cloud Messaging (FCM)
**Purpose**: Push notifications for mobile applications
**Configuration**:
- **Project ID**: `mindbridge-728f4`
- **Android App ID**: `1:*************:android:6142b3b610ddbca39ecbd7`
- **iOS App ID**: `1:*************:ios:8ef6230bf71933e79ecbd7`
- **Package Name**: `com.mindbridge.lyg`
- **Sender ID**: `*************`

**API Keys**:
- **Android**: `AIzaSyDEKj4xQctYZ00c43Sg8753dXjmEORTQLs`
- **iOS**: `AIzaSyBnoT4U18ZuVJBTwsMhDvy18Sb0e22r-QU`

**Backend Integration**:
- Service Account: `<EMAIL>`
- FCM Endpoint: `https://fcm.googleapis.com/v1/projects/mindbridge-728f4/messages:send`
- Configuration File: `config/mindbridge-728f4-firebase-adminsdk-fbsvc-40cc448ae5.json`

**Features**:
- Push notifications for Android and iOS
- Background message handling
- Foreground notification display
- Custom notification payloads
- Token management and refresh

#### Firebase Storage
**Purpose**: File storage for mobile applications
**Storage Bucket**: `mindbridge-728f4.firebasestorage.app`

### 2. WhatsApp Business API Integration

#### Notification Service
**Purpose**: WhatsApp notifications for account creation, OTP, and alerts
**Service URL**: `https://schoolyg-notif.awlyg.tech`

**Endpoints**:
- Account Creation: `/notification-whatsapp/new-account-access`
- Teacher Account: `/notification-whatsapp/new-account-access-prof`
- Absence Notification: `/notification-whatsapp/absence-notification`
- OTP Reset: `/notification-whatsapp/reset-password-otp`
- Payment Reminder: `/notification-whatsapp/payment-reminder`

**Implementation**:
- HTTP POST requests with retry mechanism (2 attempts)
- 10-second timeout per request
- JSON payload with phone numbers and template data
- Response validation for delivery status

### 3. Bankily Payment Gateway

#### Payment Processing
**Purpose**: Mobile money payments integration
**Base URL**: `https://ebankily.appspot.com/`
**Test URL**: `https://ebankily-tst.appspot.com/` (commented)

**Authentication**:
- **Client ID**: `ebankily`
- **Username**: `elmourad_crf`
- **Password**: `0fdd635d-de7b-4d37-b560-1df57044d936`
- **Grant Type**: `password`

**Features**:
- OAuth2 authentication
- Payment processing
- Transaction management
- Balance inquiries

### 4. Google APIs & Services

#### Google Client Library
**Purpose**: Google services authentication and API access
**Library**: `google/apiclient` v2.15.0

**OAuth Configuration**:
- **Auth URI**: `https://accounts.google.com/o/oauth2/auth`
- **Token URI**: `https://oauth2.googleapis.com/token`
- **Cert URL**: `https://www.googleapis.com/oauth2/v1/certs`

**Service Account**:
- **Client ID**: `100135362453600856015`
- **Client Email**: `<EMAIL>`

### 5. Email Services Configuration

#### SMTP Configuration
**Current Setup**: Local development with Mailpit
- **Host**: `mailpit`
- **Port**: `1025`
- **Encryption**: None
- **From Address**: `<EMAIL>`

#### Supported Email Providers
**Mailgun**:
- Domain: `env('MAILGUN_DOMAIN')`
- Secret: `env('MAILGUN_SECRET')`
- Endpoint: `api.mailgun.net`

**Amazon SES**:
- Key: `env('AWS_ACCESS_KEY_ID')`
- Secret: `env('AWS_SECRET_ACCESS_KEY')`
- Region: `us-east-1`

**Postmark**:
- Token: `env('POSTMARK_TOKEN')`

### 6. Real-time Communication Services

#### Pusher Configuration
**Purpose**: Real-time broadcasting and WebSocket connections
**Configuration**:
- **App ID**: `env('PUSHER_APP_ID')`
- **Key**: `env('PUSHER_APP_KEY')`
- **Secret**: `env('PUSHER_APP_SECRET')`
- **Cluster**: `mt1`
- **Host**: `api-mt1.pusher.com`
- **Port**: `443`
- **Scheme**: `https`

#### Ably Alternative
**Purpose**: Alternative real-time messaging service
**Key**: `env('ABLY_KEY')`

### 7. Cloud Storage Services

#### Amazon Web Services (AWS)
**Purpose**: Cloud storage and services
**Configuration**:
- **Access Key**: `env('AWS_ACCESS_KEY_ID')`
- **Secret Key**: `env('AWS_SECRET_ACCESS_KEY')`
- **Region**: `us-east-1`
- **Bucket**: `env('AWS_BUCKET')`

**Services**:
- **S3**: File storage
- **SES**: Email delivery
- **SQS**: Queue management
- **DynamoDB**: NoSQL database (cache)

### 8. API Endpoints & Domains

#### Production API
**Main Backend**: `https://api.mindbridge.awlyg.tech`
**API Base**: `https://api.mindbridge.awlyg.tech/api`

#### Alternative Environments
- **Production**: `https://api.schoolyg.awlyg.tech`
- **Test**: `https://api.schoolyg-test.awlyg.tech`
- **Local**: `http://*************:7562`

#### Mobile App Endpoints
**Authentication**:
- Student Login: `/api/mind_bridge/auth/etudiant/login`
- Admin Login: `/api/mind_bridge/auth/login`

**Student APIs**:
- Profile Info: `/api/mind_bridge/etudiant/infos/{userId}`
- Categories: `/api/mind_bridge/etudiant/categories/board`
- Tests: `/api/mind_bridge/etudiant/tests`
- Subjects: `/api/mind_bridge/etudiant/matiers/{id}`
- Lessons: `/api/mind_bridge/etudiant/contents`

### 9. Security & Authentication

#### API Security
**Mobile App Secret**: `this_is_a_token_for_the_mobile_app`
**Header**: `APPSECRET`

#### Token Management
**Laravel Sanctum**:
- Token expiration: 720 minutes (12 hours)
- Bearer token authentication
- Automatic token refresh

#### CORS Configuration
**Allowed Origins**: Configured for cross-origin requests
**Headers**: Standard CORS headers supported

### 10. Database Connections

#### Primary Databases
**Central Database**:
- **Host**: `db` (Docker container)
- **Port**: `3306`
- **Database**: `centrale`
- **Username**: `root`
- **Password**: `password`

**Tenant Database**:
- **Database**: `mind_bridge`
- **Same connection parameters as central**

#### Cache & Session Storage
**Redis** (Optional):
- **Host**: `127.0.0.1`
- **Port**: `6379`
- **Password**: None

**Memcached** (Optional):
- **Host**: `127.0.0.1`

### 11. File Processing Services

#### Document Processing
**PhpSpreadsheet**: Excel file processing
**PhpWord**: Word document generation
**DomPDF**: PDF generation
**QR Code**: QR code generation

#### File Storage
**Local Storage**: `storage/app/`
**Public Storage**: `storage/app/public/`
**Upload Paths**:
- Student avatars: `public/etudiants/`
- Content files: `mindbridge/uploads/content/`

### 12. Development & Monitoring

#### Docker Services
**API Container**: `mindbridge_api` (Port 7562)
**Database**: MySQL 8.0.27
**phpMyAdmin**: Database management (Port 8080)

#### Logging
**Channels**: Stack, single, daily
**Level**: Debug (development)
**External Logging**: Potential integration with external services

## 🔒 Security Considerations

### API Rate Limiting
- Request timeouts: 1 minute connect, 30 seconds receive
- Retry mechanisms for external services
- Circuit breaker patterns for service failures

### Data Protection
- Encrypted token storage (Flutter Secure Storage)
- HTTPS enforcement for all external communications
- Service account key protection

### Authentication Flow
1. Mobile app authenticates with backend
2. Backend validates credentials
3. Sanctum token issued
4. FCM token registered for notifications
5. External service calls authenticated via service accounts

## 📊 Monitoring & Health Checks

### Service Availability
- Firebase FCM status monitoring
- WhatsApp API response validation
- Payment gateway connectivity checks
- Email delivery confirmation

### Error Handling
- Comprehensive logging for all external service calls
- Fallback mechanisms for critical services
- Graceful degradation when services unavailable

This documentation provides a complete overview of all external connections and integrations in the MindBridge ecosystem, enabling proper maintenance, monitoring, and troubleshooting of external dependencies.
