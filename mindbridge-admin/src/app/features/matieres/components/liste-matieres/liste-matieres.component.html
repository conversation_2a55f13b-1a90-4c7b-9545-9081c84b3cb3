<div class="mt-2 flex flex-col items-center px-7">
  <div class="flex w-full flex-row justify-end">
    <h2 class="flex-1 text-left">Liste des matières</h2>
  </div>
  @if (allMatieres()) {
    <div class="container mx-auto mt-2">
      <div class="overflow-x-auto rounded-lg border shadow-md">
        <table class="bg-white min-w-full divide-y divide-gray-200">
          <tr class="flex w-full items-start justify-start text-left">
            <th class="w-32 px-4 py-3 font-medium">ID</th>
            <th class="w-64 px-4 py-3 font-medium">Nom</th>
            <th class="w-64 px-4 py-3 font-medium">Niveau</th>
            <th class="w-32"></th>
          </tr>
          @for (matiere of allMatieres(); track matiere.id) {
            <tr class="flex items-start justify-start">
              <td class="w-32 px-4 py-3 font-medium">{{ matiere.id }}</td>
              <td class="w-64 px-4 py-3 font-medium text-[#475467]">
                {{ matiere.name_fr }}
              </td>
              <td
                class="flex w-64 items-start justify-start gap-2 px-4 py-3 font-medium"
              >
                @if (matiere.niveaux.length <= 3) {
                  @for (niv of matiere.niveaux; track niv.id) {
                    <p
                      class="m-0 w-fit rounded-lg px-2.5 py-0.5 text-xs font-medium"
                      [ngStyle]="{
                        color: niv.color,
                        'background-color': niv.background,
                      }"
                    >
                      {{ niv.name }}
                    </p>
                  }
                } @else {
                  @for (niv of matiere.niveaux | slice: -3; track niv.id) {
                    <p
                      class="m-0 w-fit rounded-lg px-2.5 py-0.5 text-xs font-medium"
                      [ngStyle]="{
                        color: niv.color,
                        'background-color': niv.background,
                      }"
                    >
                      {{ niv.name }}
                    </p>
                  }
                  <p
                    class="nbr-niv m-0 w-fit rounded-lg px-1.5 py-0.5 text-xs font-medium"
                    [matTooltip]="
                      getTooltipMessage(matiere.niveaux | slice: 0 : -3)
                    "
                  >
                    +{{ matiere.niveaux.length - 3 }}
                  </p>
                }
              </td>
              <td
                class="flex w-32 flex-1 items-end justify-end gap-6 py-3 pl-4 pr-6 text-right font-medium"
              >
                <button>Supprimer</button>
                <button
                  type="button"
                  class="text-[#568397]"
                  (click)="getContenus(matiere)"
                >
                  Voir contenu
                </button>
              </td>
            </tr>
          }
        </table>
      </div>
    </div>
  }
</div>
