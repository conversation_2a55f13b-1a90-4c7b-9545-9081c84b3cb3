import {
  Component,
  computed,
  effect,
  inject,
  OnInit,
  Signal,
  untracked,
} from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TestManagementStore } from '../../test-management.store';
import { TestResponse } from '../../models/test-response';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../core/app.state';
import { SetLoading } from '../../../shared/store/shared.actions';
import { DatePipe, NgClass } from '@angular/common';
import { MatieresStore } from '../../../matieres/matieres.store';
import {
  MatiereResponseMini,
  NiveauMini,
} from '../../../matieres/models/matiere-response.model';
import { OPTION_ALL } from '../../../shared/global.config';
import {
  ITestSearchForm,
  ITestSearchFormGroup,
  TestSearchFormGroup,
} from '../../models/test-search-request';
import { ITestModels } from '../../models/test-categories-board';
import { TestCategoryEnum } from '../../models/test-category-enum';

@Component({
  selector: 'app-list-tests',
  templateUrl: './liste-tests.component.html',
  styleUrl: './liste-tests.component.scss',
  providers: [TestManagementStore, MatieresStore],
  imports: [ReactiveFormsModule, DatePipe, NgClass],
})
export class ListTestsComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private store = inject<Store<AppState>>(Store);

  #testManagementStore = inject(TestManagementStore);
  #matieresStore = inject(MatieresStore);
  tests: Signal<TestResponse[]> = this.#testManagementStore.state.tests;
  current_page: Signal<number> = this.#testManagementStore.state.current_page;
  total_pages: Signal<number> = computed(() =>
    Math.max(this.#testManagementStore.state.total_pages(), 1)
  );
  testsSearchForm: Signal<ITestSearchForm> =
    this.#testManagementStore.state.testsSearchForm;
  allMatieres: Signal<MatiereResponseMini[]> =
    this.#matieresStore.state.allMatieres;
  allNiveauxByMatiere: Signal<NiveauMini[]> = this.#matieresStore.state.niveaux;
  testModels: Signal<ITestModels> = computed(
    () => this.#testManagementStore.state.testCategoriesBoard().test_models
  );
  form: FormGroup<ITestSearchFormGroup> = this.fb.group(
    new TestSearchFormGroup()
  );

  constructor() {
    effect(() => {
      const searchFormValue = this.testsSearchForm();
      untracked(() => this.form.patchValue(searchFormValue));
    });
  }
  ngOnInit() {
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.searchTests();
    this.#matieresStore.getAllMatieres();
    this.#matieresStore.getAllNiveaux();
    this.#testManagementStore.getTestCategoriesBoard();
  }

  convertTypeName(type: string): string {
    const typeMappings: { [key: string]: string } = {
      test_profiling: 'Test Profiling',
      culture_general: 'Culture General',
      test_content: 'Test Content',
      exam_simulation: 'Exam Simulation',
    };

    return typeMappings[type] || '----';
  }

  searchTests() {
    this.#testManagementStore.setSearchForm(this.form.getRawValue());
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.#testManagementStore.getTests();
  }

  editTest(test: TestResponse) {
    this.router.navigate(['/add-test', test.id], {
      queryParams: { update_mode: true },
    });
  }

  viewTest(test: TestResponse) {
    this.router.navigate(['/add-test', test.id], {
      state: { title: test.title },
    });
  }

  goToPage(page: number) {
    this.#testManagementStore.setCurrentPage(page);
    this.searchTests();
  }

  protected readonly OPTION_ALL = OPTION_ALL;

  matiereChange() {
    this.searchTests();
    if (this.form.value.matiere === OPTION_ALL) {
      this.#matieresStore.getAllNiveaux();
    } else {
      this.form.controls.niveau.setValue(OPTION_ALL);
      this.#testManagementStore.setSearchForm(this.form.getRawValue());
      this.#matieresStore.getNiveauxByMatiere(this.form.value.matiere);
    }
  }

  protected readonly Math = Math;

  goToAddTest(testCategory: TestCategoryEnum) {
    this.router.navigate(['/add-test'], {
      queryParams: { type: testCategory },
    });
  }

  deleteTest(id: number) {
    const answer = confirm('Êtes vous sur de vouloir supprimer ce test?');
    if (answer) {
      this.#testManagementStore.deleteTest(id);
    }
  }
}
