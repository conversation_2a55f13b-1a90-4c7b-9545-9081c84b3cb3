.lign_de_credit_container {
    .inputs_lign_de_credit {
        justify-content: end;

        .input_contetnt {

            .input_lign_de_credit {
                all: unset;
                height: 35px;
                display: flex;
                box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
                border-radius: 5px;
                margin-bottom: 20px;

                .icon_btn {
                    all: unset;
                    width: fit-content;
                    height: 100%;
                    background-color: #6BA4BD;
                    color: #ffffff;
                    text-wrap: nowrap;
                    border-radius: 5px 0 0 5px !important;
                    cursor: pointer;
                }

                .icon_btn:first-child {
                    padding: 0 10px;
                }

                .icon_btn:last-child {
                    width: 35px;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: #ffffff;
                    border-radius: 5px 0 0 5px !important;

                    .icon {
                        width: 16px;
                        height: 16px;
                    }
                }
            }
        }

    }

    .box_component_gestion_user {
        .nouvelle_tick {
            width: auto !important;
            margin-bottom: 20px !important;
            margin-left: auto !important;
        }

        .contents_user_component {
            .instal_main {
                padding: 5px;

                .tickets {
                    line-height: 0px !important;
                    padding: 10px 0 0 0 !important;

                    >.user_image {
                        margin: 25px 0 5px 0 !important;
                        background-color: white !important;
                        width: 100px !important;
                        height: 100px !important;

                        .user_img {
                            width: 55% !important;
                            height: 55% !important;
                        }
                    }

                    .footer_tichets_card {
                        width: 100%;
                        padding: 0 0 10px 0;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: flex-start;

                        .subtitel {
                            font-size: 11px;
                            height: 14px;
                            color: #6BA4BDde;
                            line-height: 14px;
                        }
                    }

                }
            }
        }
    }

    @media (max-width: 540px) {
        .box_component_gestion_user {
            .nouvelle_tick {
                width: 100% !important;
            }
        }
    }


    .box_component_gestion_user {
        width: 95%;
        display: flex;
        flex-direction: column;
        margin: 0 auto;

        .contents_user_component {
            display: flex;
            flex-direction: column;

            .instal_main {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(238px, 1fr));
                grid-column-gap: 71px !important;
                grid-gap: 20px;
                max-height: 74vh;

                .tickets {
                    display: flex;
                    flex-direction: column !important;
                    align-items: center;
                    justify-content: center;
                    gap: 9px !important;
                    font-weight: 600;
                    padding: 15px 0 0px 0px !important;
                    position: relative !important;
                    border-radius: 18px;
                    background-color: #F2F2F2;
                    height: 287px;

                    .modifieng {
                        width: 20px !important;
                        height: 20px !important;
                        position: absolute !important;
                        top: 0 !important;
                        right: 15px !important;
                        background: none !important;
                        transform: rotate(90deg) !important;
                    }

                    .icon_create_user {
                        width: 50px;
                        height: 50px;
                    }

                    .create_user {
                        font-size: 12px;
                    }

                    .user_name {
                        font-size: 15px;
                        font-weight: 600;
                        text-align: center;
                        margin: 0;
                    }

                    .user_mail {
                        font-size: 11px;
                        margin-bottom: 19px;
                    }

                    .user_image {
                        width: 110px;
                        height: 107px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        background-color: #cae8e8;

                        .user_img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .roles {
                        font-size: 13px;
                        padding: 7px 19px;
                        line-height: 18px;
                        border-radius: 5px;
                        margin: 6px auto 25px auto;
                        border: none;
                        background-color: #6BA4BD;
                        color: white;
                        cursor: pointer;
                    }
                }
            }
        }

        .arrows_content_table_box_component_gestion_user {
            width: 100%;
            padding: 15px 0;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-top: auto !important;
        }
    }

    .status_label {
        text-transform: uppercase;
    }

}

.body_container {
    max-height: calc(100vh - 192px) !important;
}

.content_main {
    width: 95%;
    margin-inline: auto;
}