import { createAction, props } from "@ngrx/store";
import { Matiere } from "../models/matiere.model";
import { <PERSON>pit<PERSON> } from "../models/chapitre.model";
import { Contenue } from "../models/contenue.model";
import {ChapterResponse} from "../models/chapter-response.model";

// ===== All Matieres =====
export const AllMatieres = createAction(
  '[ Matieres ] - All Matieres',
);

export const AllMatieresSuccess = createAction(
  '[ Matieres ] - All Matieres Success ',
  props<{ matieres: any }>()
);

export const AllMatieresError = createAction(
  '[ Matieres ] - All Matieres Error',
);

// Add Matiere
export const AddMatiere = createAction(
  '[ Matieres ] - Add Matiere',
  props<{ payload: Matiere }>()
);

export const AddMatiereSuccess = createAction(
  '[ Matieres ] - Add Matiere Success ',
  props<{ matiere: any }>()
);

export const AddMatiereError = createAction(
  '[ Matieres ] - Add Matiere Error',
);

// Update Matiere
export const UpdateMatiere = createAction(
  '[ Matieres ] - Update Matiere',
  props<{ payload: Matiere, id: number }>()
);

export const UpdateMatiereSuccess = createAction(
  '[ Matieres ] - Update Matiere Success ',
  props<{ matiere: any }>()
);

export const UpdateMatiereError = createAction(
  '[ Matieres ] - Update Matiere Error',
);

// Delete Matiere
export const DeleteMatiere = createAction(
  '[ Matieres ] - Delete Matiere',
  props<{ id: number }>()
);

export const DeleteMatiereSuccess = createAction(
  '[ Matieres ] - Delete Matiere Success ',
  props<{ message: string }>()
);

export const DeleteMatiereError = createAction(
  '[ Matieres ] - Delete Matiere Error',
);

// ===== All Niveaus =====
export const AllNiveaus = createAction(
  '[ Niveaus ] - All Niveaus',
);

export const AllNiveausSuccess = createAction(
  '[ Niveaus ] - All Niveaus Success ',
  props<{ niveaus: any }>()
);

export const AllNiveausError = createAction(
  '[ Niveaus ] - All Niveaus Error',
);

// ===== All Chapitres =====
export const AllChapitres = createAction(
  '[ Chapitres ] - All Chapitres',
);

export const AllChapitresSuccess = createAction(
  '[ Chapitres ] - All Chapitres Success ',
  props<{ chapitres: any }>()
);

export const AllChapitresError = createAction(
  '[ Chapitres ] - All Chapitres Error',
);

// Add Chapitre
export const AddChapitre = createAction(
  '[ Chapitres ] - Add Chapitre',
  props<{ payload: Chapitre }>()
);

export const AddChapitreSuccess = createAction(
  '[ Chapitres ] - Add Chapitre Success ',
  props<{ chapitre: any }>()
);

export const AddChapitreError = createAction(
  '[ Chapitres ] - Add Chapitre Error',
);

// Update Chapitre
export const UpdateChapitre = createAction(
  '[ Chapitres ] - Update Chapitre',
  props<{ payload: Chapitre, id: number }>()
);

export const UpdateChapitreSuccess = createAction(
  '[ Chapitres ] - Update Chapitre Success ',
  props<{ chapitre: any }>()
);

export const UpdateChapitreError = createAction(
  '[ Chapitres ] - Update Chapitre Error',
);

// Delete Chapitre
export const DeleteChapitre = createAction(
  '[ Chapitres ] - Delete Chapitre',
  props<{ id: number }>()
);

export const DeleteChapitreSuccess = createAction(
  '[ Chapitres ] - Delete Chapitre Success ',
  props<{ message: string }>()
);

export const GetChapiters = createAction(
  '[Chapiters] - get chapiters by matier and niveau',
  props<{ matierId: number, niveauId: number}>()
)

export const GetChapitersSuccess = createAction(
  '[Chapiters] - get chapiters by matier and niveau success',
  props<{ chapters: ChapterResponse[]}>()
)

export const GetChapitersError = createAction(
  '[chapiters] - get chapiters by matier and niveau error',
)

export const ClearChapters = createAction(
  '[chapiters] - clear chapiters by matier and niveau error',
)

export const DeleteChapitreError = createAction(
  '[ Chapitres ] - Delete Chapitre Error',
);

// ===== All Contenues =====
export const AllContenues = createAction(
  '[ Contenues ] - All Contenues',
);

export const AllContenuesSuccess = createAction(
  '[ Contenues ] - All Contenues Success ',
  props<{ contenues: any }>()
);

export const AllContenuesError = createAction(
  '[ Contenues ] - All Contenues Error',
);

// Add Contenue
export const AddContenue = createAction(
  '[ Contenues ] - Add Contenue',
  props<{ payload: FormData }>()
);

export const AddContenueSuccess = createAction(
  '[ Contenues ] - Add Contenue Success ',
  props<{ contenue: any }>()
);

export const AddContenueError = createAction(
  '[ Contenues ] - Add Contenue Error',
);

// Update Contenue
export const UpdateContenue = createAction(
  '[ Contenues ] - Update Contenue',
  props<{ payload: Contenue, id: number }>()
);

export const UpdateContenueSuccess = createAction(
  '[ Contenues ] - Update Contenue Success ',
  props<{ contenue: any }>()
);

export const UpdateContenueError = createAction(
  '[ Contenues ] - Update Contenue Error',
);

// Delete Contenue
export const DeleteContenue = createAction(
  '[ Contenues ] - Delete Contenue',
  props<{ id: number }>()
);

export const DeleteContenueSuccess = createAction(
  '[ Contenues ] - Delete Contenue Success ',
  props<{ message: string }>()
);

export const DeleteContenueError = createAction(
  '[ Contenues ] - Delete Contenue Error',
);
