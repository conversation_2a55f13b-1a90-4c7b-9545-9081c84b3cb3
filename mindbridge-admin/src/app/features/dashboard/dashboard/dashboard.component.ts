import { Component } from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { BaseChartDirective } from 'ng2-charts';

import {
  ChartOptions,
  LinearScale,
  CategoryScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  BarController,
  BarElement,
  Filler,
  ChartData,
  ChartConfiguration,
  ArcElement,
  PieController,
  LineController,
} from 'chart.js';

import { Chart } from 'chart.js'; // Import Chart.js for registration

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
  imports: [MatIcon, BaseChartDirective],
  standalone: true,
})
export class DashboardComponent {
  public formationsEffectueesData: ChartData<'line'> = {
    labels: ['SEP', 'OCT', 'NOV', 'DEC', 'JAN', 'FEV'],
    datasets: [
      {
        data: [22, 20, 28, 32, 20, 30],
        label: '',
        borderColor: '#2D2EFF',
        backgroundColor: 'transparent',
        pointBackgroundColor: '#FFFFFF',
        pointBorderColor: '#2D2EFF',
        pointRadius: 6,
        pointHoverRadius: 8,
        tension: 0.4, // Courbe lisse
        fill: 'false',
      },
      {
        data: [11, 10, 14, 16, 10, 15],
        label: '',
        borderColor: '#6EC6FF',
        backgroundColor: 'transparent',
        pointBackgroundColor: '#FFFFFF',
        pointBorderColor: '#6EC6FF',
        pointRadius: 6,
        pointHoverRadius: 8,
        tension: 0.4,
        fill: 'false',
      },
    ],
  };

  public formationsEffectueesOptions: ChartOptions = {
    responsive: true,
    plugins: {
      legend: { display: false },
      tooltip: {
        enabled: true,
        backgroundColor: '#2D2EFF',
        titleColor: '#FFFFFF',
        bodyColor: '#FFFFFF',
        padding: 10,
        displayColors: false,
        callbacks: {
          label: (tooltipItem) => `${tooltipItem.raw}`,
        },
      },
    },
    scales: {
      x: {
        ticks: { color: '#B0B3C6', font: { size: 14 } },
        grid: { display: false },
      },
      y: {
        display: false,
      },
    },
  };

  public nombreEtudiantsParVilleData: ChartData<'bar'> = {
    labels: ['CAS', 'RAB', 'FAS', 'MAR', 'TAN', 'MEK'],
    datasets: [
      {
        data: [10, 20, 30, 25, 40, 35],
        label: 'Performance',
        borderColor: 'rgba(70, 80, 255, 0.5)',
        backgroundColor: 'rgba(70, 80, 255, 0.5)',
        barThickness: 20,
        borderRadius: 10,
      },
    ],
  };

  public nombreEtudiantsParVilleOptions: ChartOptions = {
    responsive: true,
    plugins: {
      legend: { display: false },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(70, 80, 255, 0.5)',
        titleColor: '#FFFFFF',
        bodyColor: '#FFFFFF',
        padding: 10,
        displayColors: false,
        callbacks: {
          label: (tooltipItem) => `${tooltipItem.raw}`, // Show only value
        },
      },
    },
    scales: {
      x: {
        ticks: { color: '#B0B3C6', font: { size: 14 } }, //
        grid: { display: false },
      },
      y: {
        display: false,
      },
    },
  };

  public formationsPopulairesData: ChartData<'pie'> = {
    labels: [],
    datasets: [
      {
        data: [60, 22, 18],
        backgroundColor: ['#4318FF', '#6AD2FF', '#EFF4FB'],
        borderColor: ['#4318FF', '#6AD2FF', '#EFF4FB'],
        borderWidth: 2, // Optional: border width for each segment
      },
    ],
  };

  public formationsPopulairesOptions: ChartConfiguration<
    'pie',
    Array<number>,
    any
  >['options'] = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: (tooltipItem) => `${tooltipItem.raw}`, // Show only value
        },
      },
    },
  };

  constructor() {
    Chart.register(
      LinearScale,
      CategoryScale,
      LineElement,
      PointElement,
      Title,
      Tooltip,
      Legend,
      BarController,
      BarElement,
      Filler,
      ArcElement,
      PieController,
      LineController
    );
  }
}
