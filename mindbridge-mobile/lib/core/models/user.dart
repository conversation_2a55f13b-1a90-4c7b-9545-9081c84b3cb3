// To parse this JSON data, do
//
//     final user = userFromJson(jsonString);

import 'dart:convert';

User userFromJson(String str) => User.fromJson(json.decode(str));

String userToJson(User data) => json.encode(data.toJson());

class User {
  String? token;
  UserClass? user;
  Etudiant? etudiant;

  User({
    this.token,
    this.user,
    this.etudiant,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        token: json["token"],
        user: json["user"] == null ? null : UserClass.fromJson(json["user"]),
        etudiant: json["etudiant"] == null
            ? null
            : Etudiant.fromJson(json["etudiant"]),
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "user": user?.toJson(),
        "etudiant": etudiant?.toJson(),
      };
}

class Etudiant {
  int? id;
  String? name;
  dynamic identifiant;
  dynamic nni;
  String? avatar;
  String? testProfilingCompleted;
  int? userId;
  int? niveauId;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;

  Etudiant({
    this.id,
    this.name,
    this.identifiant,
    this.nni,
    this.avatar,
    this.testProfilingCompleted,
    this.userId,
    this.niveauId,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  factory Etudiant.fromJson(Map<String, dynamic> json) => Etudiant(
        id: json["id"],
        name: json["name"],
        identifiant: json["identifiant"],
        nni: json["nni"],
        avatar: json["avatar"],
        testProfilingCompleted: json["test_profiling_completed"],
        userId: json["user_id"],
        niveauId: json["niveau_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "identifiant": identifiant,
        "nni": nni,
        "avatar": avatar,
        "test_profiling_completed": testProfilingCompleted,
        "user_id": userId,
        "niveau_id": niveauId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt,
      };
}

class UserClass {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? avatar;
  int? active;
  String? type;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? firstLoginCompleted;
  dynamic fcmToken;
  String? status;
  int? isMindBridgeUser;

  UserClass({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.avatar,
    this.active,
    this.type,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.firstLoginCompleted,
    this.fcmToken,
    this.status,
    this.isMindBridgeUser,
  });

  factory UserClass.fromJson(Map<String, dynamic> json) => UserClass(
        id: json["id"],
        name: json["name"],
        email: json["email"],
        phone: json["phone"],
        avatar: json["avatar"],
        active: json["active"],
        type: json["type"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"],
        firstLoginCompleted: json["first_login_completed"],
        fcmToken: json["fcm_token"],
        status: json["status"],
        isMindBridgeUser: json["is_mind_bridge_user"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "email": email,
        "phone": phone,
        "avatar": avatar,
        "active": active,
        "type": type,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt,
        "first_login_completed": firstLoginCompleted,
        "fcm_token": fcmToken,
        "status": status,
        "is_mind_bridge_user": isMindBridgeUser,
      };
}
