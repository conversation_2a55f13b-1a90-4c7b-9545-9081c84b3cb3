import { createAction, props } from '@ngrx/store';
import { QuestionRepository } from '../models/questions-repository-response';
/*
  TODO : delete all of this, move to signal store
 */

// All QuestionRepository
export const AllQuestionsRepository = createAction(
  '[ Test ] - All Questions Repository'
);

export const AllQuestionsRepositorySuccess = createAction(
  '[ Test ] - All Questions Repository Success ',
  props<{ questionsRepository: any }>()
);

export const AllQuestionsRepositoryError = createAction(
  '[ Test ] - All Questions Repository Error'
);

// Add QuestionRepository
export const AddQuestionRepository = createAction(
  '[ Test ] - Add Question Repository',
  props<{ payload: QuestionRepository }>()
);

export const AddQuestionRepositorySuccess = createAction(
  '[ Test ] - Add Question Repository Success ',
  props<{ questionRepository: any }>()
);

export const AddQuestionRepositoryError = createAction(
  '[ Test ] - Add Question Repository Error'
);

// Update QuestionRepository
export const UpdateQuestionRepository = createAction(
  '[ Test ] - Update Question Repository',
  props<{ payload: QuestionRepository; id: number }>()
);

export const UpdateQuestionRepositorySuccess = createAction(
  '[ Test ] - Update Question Repository Success ',
  props<{ questionRepository: any }>()
);

export const UpdateQuestionRepositoryError = createAction(
  '[ Test ] - Update Question Repository Error'
);

// Delete QuestionRepository
export const DeleteQuestionRepository = createAction(
  '[ Test ] - Delete Question Repository',
  props<{ id: number }>()
);

export const DeleteQuestionRepositorySuccess = createAction(
  '[ Test ] - Delete Question Repository Success ',
  props<{ message: string }>()
);

export const DeleteQuestionRepositoryError = createAction(
  '[ Test ] - Delete Question Repository Error'
);
