import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  computed,
  effect,
  ElementRef,
  inject,
  Input,
  numberAttribute,
  OnChanges,
  OnInit,
  QueryList,
  signal,
  Signal,
  ViewChildren,
  WritableSignal,
} from '@angular/core';
import {
  <PERSON><PERSON>rray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { AppState } from '../../../../core/app.state';
import { SetLoading } from '../../../shared/store/shared.actions';
import { ActivatedRoute, Router } from '@angular/router';
import { EmojiButton } from '@joeattardi/emoji-button';
import {
  CdkDrag,
  CdkDragDrop,
  CdkDropList,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { TestManagementStore } from '../../test-management.store';
import { TestResponse } from '../../models/test-response';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { DatePipe, JsonPipe, NgClass } from '@angular/common';
import { TestCategoryEnum } from '../../models/test-category-enum';
import { TestCategoryNamePipe } from '../../../shared/pipes/test-category-name.pipe';
import { UnicodeToEmojiPipe } from '../../../shared/pipes/unicode-to-emoji.pipe';
import {
  MatiereResponse,
  NiveauMini,
} from '../../../matieres/models/matiere-response.model';
import { MatieresStore } from '../../../matieres/matieres.store';
import {
  TestCreationRequestQuestionFormGroup,
  TestCreationRequestQuestionOptionFormGroup,
  TestCreationRequestStepFormGroup,
  TestCreationRequestWrapperFormGroup,
} from '../../models/test-creation-form-group';
import { TypeQuestionEnum } from '../../models/type-question.enum';
import {
  TestCreationRequestStep,
  TestCreationRequestWrapper,
} from '../../models/test-creation-form';
import { ITestModels } from '../../models/test-categories-board';
import { ContenueResponse } from '../../../matieres/models/contenue-response.model';
import { MatError } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { DifficultyLevelEnum } from '../../models/difficulty-level-enum';
import { FormatDifficultyLevelPipe } from '../../../shared/pipes/format-difficulty-level.pipe';
import { MatRadioButton, MatRadioGroup } from '@angular/material/radio';
import { ImportQuestionComponent } from '../import-question/import-question.component';
import { QuestionResponse } from '../../../questions-repository/models/questions-response';
import {
  TestUpdateRequestWrapper,
  TestUpdateRequestWrapperComplete,
} from '../../models/update-creation-form';

declare global {
  interface Window {
    showUnicode: () => void;
  }
}

@Component({
  selector: 'app-add-test',
  templateUrl: './add-test.component.html',
  styleUrl: './add-test.component.scss',
  imports: [
    ReactiveFormsModule,
    CdkDropList,
    CdkDrag,
    NgClass,
    MatSlideToggle,
    TestCategoryNamePipe,
    UnicodeToEmojiPipe,
    MatError,
    DatePipe,
    JsonPipe,
    MatIcon,
    FormatDifficultyLevelPipe,
    MatRadioGroup,
    MatRadioButton,
    FormsModule,
  ],
  providers: [MatieresStore, TestManagementStore, DatePipe],
})
export class AddTestComponent implements OnInit, AfterViewInit, OnChanges {
  #store = inject<Store<AppState>>(Store);
  #routeActive = inject(ActivatedRoute);
  #fb = inject(FormBuilder);
  #dialog = inject(MatDialog);
  #cdr = inject(ChangeDetectorRef);
  #router = inject(Router);
  #testManagementStore = inject(TestManagementStore);
  #matieresStore = inject(MatieresStore);
  #datePipe = inject(DatePipe);

  today = new Date();
  TestCategoryEnum = TestCategoryEnum;
  TypeQuestionEnum = TypeQuestionEnum;

  @Input() id: number;
  @Input() type: string;
  @Input({ transform: numberAttribute }) category_id: number;
  @Input({ transform: numberAttribute }) niveau_id: number;
  @Input({ transform: numberAttribute }) matiere_id: number;
  @Input({ transform: numberAttribute }) content_id: number;
  @Input() update_mode: boolean;
  question_disabled = false;
  testCreationFormGroup: FormGroup<TestCreationRequestWrapperFormGroup> =
    this.#fb.group(new TestCreationRequestWrapperFormGroup());
  private emojiPicker: any;
  checked = false;
  readOnlyMode = false;
  testCategory: WritableSignal<TestCategoryEnum> = signal(null);

  matieres: Signal<MatiereResponse[]> = this.#matieresStore.state.matieres;
  allNiveauxByMatiere: Signal<NiveauMini[]> = this.#matieresStore.state.niveaux;
  contenues: Signal<ContenueResponse[]> = this.#matieresStore.state.contenues;
  testModels: Signal<ITestModels> = computed(
    () => this.#testManagementStore.state.testCategoriesBoard().test_models
  );
  currentEditing: { stepIndex: number; optionIndex: number } | null = null;
  currentStepIndex: number = -1;
  isTestProfiling: boolean = false;

  dialogRef!: MatDialogRef<any>;

  questionsRepository$: Observable<any> = this.#store.select(
    (state) => state.questionsRepository.questionsRepository
  );
  questionsRepository: any = [];

  @ViewChildren('emojiPickerBtn') emojiPickerButtons!: QueryList<ElementRef>;

  testDetails: Signal<TestResponse> =
    this.#testManagementStore.state.testDetails;

  difficultyLevels: DifficultyLevelEnum[] = Object.values(DifficultyLevelEnum);

  constructor() {
    this.questionsRepository$.subscribe((questionsRepository: any) => {
      this.questionsRepository = questionsRepository?.data;
    });

    effect(() => {
      const testDetails = this.testDetails();



      console.log('tetss details ' , testDetails);


      if (testDetails) {
        if (testDetails?.matiere_id) {
          this.#matieresStore.matiereContentsJustByMatiereId(
            testDetails?.matiere_id
          );
        }
        if (testDetails?.niveau_id) {
          this.#matieresStore.getMatieresByNiveau(testDetails?.niveau_id);
        }

        this.testCategory.set(testDetails?.category?.code);

        this.testCreationFormGroup.patchValue({
          test: {
            id: testDetails.id,
            title: testDetails.title,
            description: testDetails.description,
            difficulty_level: testDetails.difficulty_level,
            category_id: testDetails.category_id,
            matiere_id: testDetails.matiere_id,
            niveau_id: testDetails.niveau_id,
            content_id: testDetails?.content?.id,
            timer: testDetails?.timer,
            challenge_date: testDetails?.challenge_date,
          },
        });
        this.testCreationFormGroup.controls.steps.clear();

        testDetails.steps.map((step) => {
          const stepFormGroup = new FormGroup<TestCreationRequestStepFormGroup>(
            new TestCreationRequestStepFormGroup({
              id: step.id,
              type: step.type,
              order: step.order,
              question: step.question
                ? {
                    id: step.question.id,
                    type: step.question.type,
                    content: step.question.content,
                    description: step.question.description,
                    image_path: step.question.image_path,
                    required: step.question.is_required,
                    answer: step.question.is_true,
                    options: step.question.options.map((option) => ({
                      id: option.id,
                      name: option.name,
                      icon: option.icon,
                      isCorrect: option.isCorrect,
                    })),
                  }
                : null,
            })
          );
          stepFormGroup.disable();
          this.testCreationFormGroup.controls.steps.push(stepFormGroup);
        });
        this.question_disabled = true;
      } else {
        this.addStep();
        this.question_disabled = false;
      }
    });
    effect(() => {
      const fullTestCategory = this.testModels().children.find(
        (child) => child.code === this.testCategory()
      );

      console.log('full test category ', fullTestCategory);

      
      if (fullTestCategory) {
        this.testCreationFormGroup.controls.test.controls.category_id.setValue(
          fullTestCategory.id
        );
      }
    });
  }

  ngOnInit(): void {
    this.#matieresStore.getAllNiveaux();
    this.#testManagementStore.getTestCategoriesBoard();
  }

  ngOnChanges() {
    if (this.id && !this.update_mode) {
      this.readOnlyMode = true;
      this.#store.dispatch(SetLoading({ isAppLoading: true }));
      this.#testManagementStore.getTestDetail(this.id);
    }
    if (this.id && this.update_mode) {
      this.#store.dispatch(SetLoading({ isAppLoading: true }));
      this.#testManagementStore.getTestDetail(this.id);
    }
    this.testCategory.set(
      this.#routeActive.snapshot.queryParamMap.get('type') as TestCategoryEnum
    );
    if (this.type !== null) {
      this.testCategory.set(this.type as TestCategoryEnum);
    }

    if (this.category_id) {
      this.testCreationFormGroup.patchValue({
        test: {
          category_id: this.category_id,
        },
      });
    }
    if (this.niveau_id) {
      this.#store.dispatch(SetLoading({ isAppLoading: true }));
      this.#matieresStore.getMatieresByNiveau(this.niveau_id);
      this.testCreationFormGroup.patchValue({
        test: {
          niveau_id: this.niveau_id,
        },
      });
    }
    if (this.matiere_id) {
      this.#store.dispatch(SetLoading({ isAppLoading: true }));
      this.#matieresStore.matiereContentsJustByMatiereId(this.matiere_id);
      this.testCreationFormGroup.patchValue({
        test: {
          matiere_id: this.matiere_id,
        },
      });
    }
    if (this.content_id) {
      this.testCreationFormGroup.patchValue({
        test: {
          content_id: this.content_id,
        },
      });
    }
  }
  onImagePicked(event: Event, sIndex: number) {
    const file = (event.target as HTMLInputElement).files[0]; // Here we use only the first file (single file)
    this.testCreationFormGroup.controls.steps
      .at(sIndex)
      .controls.question.controls.image_path.setValue(file);
  }
  displayImage(blob: Blob | string): string {
    return typeof blob === 'string' ? blob : URL.createObjectURL(blob);
  }
  selectQuestion(question: any): void {
    if (question) {
      this.importQuestion(question, this.currentStepIndex);
    }
    this.dialogRef.close();
  }

  openSelectQuestionDialog(event: Event, stepIndex: number): void {
    event.stopPropagation();

    this.currentStepIndex = stepIndex;

    const value = this.testCreationFormGroup.getRawValue();
    this.dialogRef = this.#dialog.open(ImportQuestionComponent, {
      data: {
        category: value.test.category_id,
        matiere: this.testCreationFormGroup.value.test.matiere_id,
        niveau: this.testCreationFormGroup.value.test.niveau_id,
      },
      width: '600px',
    });

    this.dialogRef
      .afterClosed()
      .subscribe((selectedQuestions: QuestionResponse[]) => {
        if (selectedQuestions) {
          this.importQuestion(selectedQuestions, stepIndex);
        }
      });
  }

  importQuestion(questions: QuestionResponse[], stepIndex: number): void {
    for (let i = 0; i < questions.length; i++) {
      const question = questions.at(i);
      const insertAt = stepIndex + i + 1;
      this.testCreationFormGroup.controls.steps.insert(
        insertAt,
        new FormGroup<TestCreationRequestStepFormGroup>(
          new TestCreationRequestStepFormGroup()
        )
      );
      const step = this.testCreationFormGroup.controls.steps.at(insertAt);
      step.controls.question.patchValue({
        id: question.id,
        type: question.type,
        content: question.content,
        answer: question.answer,
        description: question.description,
        required: true,
      });
      // step.controls.type.patchValue(question.type);
      if (question.options.filter((o) => o.isCorrect).length > 1) {
        step.controls.type.patchValue(TypeQuestionEnum.many);
      }
      if (question.options.filter((o) => o.isCorrect).length === 1) {
        step.controls.type.patchValue(TypeQuestionEnum.one);
      }

      if (question.options.length === 0) {
        step.controls.type.patchValue(TypeQuestionEnum.TrueOrFalse);
      }
      step.controls.question.controls.options.clear(); //to remove the default option
      question.options
        .map((option) => ({
          id: option.id,
          name: option.name,
          icon: option.icon,
          isCorrect: option?.isCorrect,
        }))
        .map((option) => {
          step.controls.question.controls.options.push(
            new FormGroup<TestCreationRequestQuestionOptionFormGroup>(
              new TestCreationRequestQuestionOptionFormGroup(option)
            )
          );
        });

      console.log(step.controls.question.controls.options.length);
      console.log(step.controls.question.controls.options.value);
    }
    this.recalculateQuestionOrder();
  }

  recalculateQuestionOrder() {
    this.testCreationFormGroup.controls.steps.controls.forEach(
      (question, index) => {
        question.controls.order.setValue(index + 1);
      }
    );
  }
  cancelDialog(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  // Créer un nouveau step group
  newStep(): FormGroup<TestCreationRequestStepFormGroup> {
    const testCreationRequestStep = new TestCreationRequestStep();
    testCreationRequestStep.order =
      this.testCreationFormGroup.controls.steps.controls.length + 1;
    return this.#fb.group<TestCreationRequestStepFormGroup>(
      new TestCreationRequestStepFormGroup(testCreationRequestStep)
    );
  }

  toggleDescription(stepIndex: number, isChecked: boolean): void {
    const question = this.getQuestion(stepIndex);

    if (!isChecked) {
      question.controls.description.setValue('');
    }
  }

  addStep(index?: number): void {
    const newStep = this.newStep();
    if (
      index !== undefined &&
      index >= 0 &&
      index <= this.testCreationFormGroup.controls.steps.controls.length
    ) {
      this.testCreationFormGroup.controls.steps.insert(index + 1, newStep);
    } else {
      this.testCreationFormGroup.controls.steps.push(newStep);
    }

    if (this.testCategory() === TestCategoryEnum.TestProfiling) {
      const lastStep = this.testCreationFormGroup.controls.steps.at(
        this.testCreationFormGroup.controls.steps.length - 1
      );
      const questionGroup = lastStep.controls.question;
      if (questionGroup) {
        const options = questionGroup.controls.options;
        options.controls.forEach(
          (option: FormGroup<TestCreationRequestQuestionOptionFormGroup>) => {
            option.controls.isCorrect.setValue(false);
            option.controls.isCorrect.disable();
          }
        );
      }
    }
  }

  removeStep(index: number): void {
    this.testCreationFormGroup.controls.steps.removeAt(index);
  }

  getQuestion(
    stepIndex: number
  ): FormGroup<TestCreationRequestQuestionFormGroup> {
    return this.testCreationFormGroup.controls.steps.at(stepIndex).controls
      .question;
  }

  getOptions(
    stepIndex: number
  ): FormArray<FormGroup<TestCreationRequestQuestionOptionFormGroup>> {
    return this.getQuestion(stepIndex).controls.options;
  }

  addOption(stepIndex: number): void {
    const options = this.getOptions(stepIndex);
    const newOptionGroup = this.#fb.group(
      new TestCreationRequestQuestionOptionFormGroup()
    );
    options.push(newOptionGroup);
  }

  removeOption(stepIndex: number, optionIndex: number): void {
    const options = this.getOptions(stepIndex);
    const removedOption = options.at(optionIndex);
    const wasCorrect = removedOption.controls.isCorrect.value;

    options.removeAt(optionIndex);

    if (wasCorrect && options.length > 0) {
      options.at(options.length - 1).controls?.isCorrect?.setValue(true);
    }
  }
  /**
   * Définir l'option en cours de modification
   * @param stepIndex Index du step
   * @param optionIndex Index de l'option
   */
  setEditingOption(stepIndex: number, optionIndex: number): void {
    this.currentEditing = { stepIndex, optionIndex };
    this.#cdr.detectChanges();
  }

  /**
   * Réinitialiser l'option en cours de modification
   */
  resetEditingOption(): void {
    this.currentEditing = null;
    this.#cdr.detectChanges();
  }

  /**
   * Marquer une option comme correcte et réinitialiser l'édition
   * @param stepIndex Index du step
   * @param optionIndex Index de l'option
   */
  toggleCorrectAnswer(stepIndex: number, optionIndex: number): void {
    if (
      this.testCreationFormGroup.controls.steps.at(stepIndex).controls.type
        .value === TypeQuestionEnum.one
    ) {
      //if the type of question allows only one correct answer, we switch the other ones to false
      const options = this.getOptions(stepIndex);
      options.controls.forEach((ctrl, idx) => {
        if (idx !== optionIndex) {
          ctrl.controls.isCorrect.setValue(false);
        }
      });
    }

    this.resetEditingOption();
    this.#cdr.detectChanges();
  }

  isToggleVisible(stepIndex: number, optionIndex: number): boolean {
    if (this.isTestProfiling) {
      return false;
    }

    const isCorrect =
      this.getOptions(stepIndex).at(optionIndex).controls.isCorrect.value;
    const isEditing =
      this.currentEditing?.stepIndex === stepIndex &&
      this.currentEditing.optionIndex === optionIndex;
    return isCorrect || isEditing;
  }

  // Gestion des émojis
  drop(event: CdkDragDrop<any[]>) {
    moveItemInArray(
      this.testCreationFormGroup.controls.steps.controls,
      event.previousIndex,
      event.currentIndex
    );
    this.testCreationFormGroup.controls.steps.controls.forEach((question) => {
      question.controls.order.setValue(event.currentIndex + 1);
    });
  }
  ngAfterViewInit(): void {
    this.initializeEmojiPickers();

    // Listen for changes to emojiPickerButtons
    this.emojiPickerButtons.changes.subscribe(() => {
      this.initializeEmojiPickers();
    });
    if (this.id && !this.update_mode) {
      this.readOnlyMode = true;
      this.testCreationFormGroup.disable();
    }
  }

  initializeEmojiPickers(): void {
    this.emojiPickerButtons.forEach((button) => {
      const emojiPicker = new EmojiButton();

      // Attach picker toggle to button click
      button.nativeElement.addEventListener('click', () => {
        emojiPicker.togglePicker(button.nativeElement);
      });

      // Retrieve the corresponding step and option index from data attributes
      const stepIndexAttr =
        button.nativeElement.getAttribute('data-step-index');
      const optionIndexAttr =
        button.nativeElement.getAttribute('data-option-index');

      if (stepIndexAttr === null || optionIndexAttr === null) return;

      const stepIndex = parseInt(stepIndexAttr, 10);
      const optionIndex = parseInt(optionIndexAttr, 10);

      // Safely handle emoji selection
      emojiPicker.on('emoji', (selection: any) => {
        const options = this.getOptions(stepIndex);
        console.log(selection.emoji, selection, stepIndex);
        if (options.at(optionIndex)) {
          options.at(optionIndex).controls.icon.setValue(selection.emoji);
        }
      });
    });
  }

  saveOrUpdate(): void {
    const formValue: TestCreationRequestWrapper =
      this.testCreationFormGroup.getRawValue();
    formValue.steps.forEach((step: TestCreationRequestStep) => {
      if (step.type === TypeQuestionEnum.TrueOrFalse) {
        delete step.question.options;
        step.question.is_true = step.question.answer;
        step.question.is_false = !step.question.answer;
        delete step.question.answer;
      }
    });

    formValue.test.challenge_date = this.#datePipe.transform(
      formValue.test.challenge_date,
      'yyyy-MM-dd'
    );
    if (this.id && this.update_mode) {
      const testUpdateRequestComplete: TestUpdateRequestWrapperComplete =
        new TestUpdateRequestWrapperComplete();
      testUpdateRequestComplete.id = this.id;
      testUpdateRequestComplete.testUpdateRequestWrapper = formValue;
      this.#store.dispatch(SetLoading({ isAppLoading: true }));
      this.#testManagementStore.editTest(testUpdateRequestComplete);
    } else {
      this.#store.dispatch(SetLoading({ isAppLoading: true }));
      this.#testManagementStore.createTest(formValue);
    }
  }

  cancel() {
    this.#router.navigate(['/tests']);
  }

  matiereChange() {
    this.#store.dispatch(SetLoading({ isAppLoading: true }));
    this.#matieresStore.matiereContentsJustByMatiereId(
      this.testCreationFormGroup.value.test.matiere_id
    );
  }
  niveauChange() {
    this.#store.dispatch(SetLoading({ isAppLoading: true }));
    this.#matieresStore.getMatieresByNiveau(
      this.testCreationFormGroup.value.test.niveau_id
    );
  }

  questionTypeChange(stepIndex: number) {
    if (
      this.testCreationFormGroup.controls.steps.at(stepIndex).value.type ===
      TypeQuestionEnum.one
    ) {
      const options = this.getOptions(stepIndex);
      let hasMultipleCorrectAnswers =
        options.value.filter((option) => option.isCorrect).length > 1;
      if (hasMultipleCorrectAnswers) {
        //if a question has multiple correct answers, and we change it to one, we reset them all to false until the user picks one
        options.controls.forEach((ctrl, idx) => {
          ctrl.controls.isCorrect.setValue(false);
        });
        alert(
          "Le type de question a été changé à 'un seul choix'. Toutes les réponses correctes vont être réinitialisées. Veuillez en choisir une seule."
        );
      }
    }
  }
}
