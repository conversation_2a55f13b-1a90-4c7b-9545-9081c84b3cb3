<div class="db dimensions_container">
    <div class="df bg-color-head">
        <h2>{{ 'Ajouter Utilisateur' }}</h2>
        <mat-icon (click)="close()">close</mat-icon>
    </div>
    <form class="filters flex-column" [formGroup]="form">
        <div class="df">
            <div class="search flex-column">
                <label>First name <span class="required">*</span></label>
                <input type="text" placeholder="First name">
            </div>
            <div class="search flex-column">
                <label>Last name <span class="required">*</span></label>
                <input type="text" placeholder="Last name">
            </div>
        </div>
        <div class="df">
            <div class="flex-column search">
                <label>Email Address</label>
                <input type="email" placeholder="<EMAIL>" autocomplete="new-email">
            </div>
            <div class="flex-column search password">
                <label>Password</label>
                <input type="text" placeholder="********"
                    
                    autocomplete="new-password">
            </div>
        </div>
        <div class="flex-column search">
            <label>Phone <span class="required">*</span></label>
            <input type="text" placeholder="+222">
        </div>
        <div class="between_item">
            <button type="reset" class="bg-color-btn" (click)="close()">
                Cancel
            </button>
            <button type="submit" class="bg-color-btn bg-active-btn">
                {{ 'Enregistrer' }}
            </button>
        </div>
    </form>
</div>