<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('user_message_states', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('category');
            $table->string('sub_category')->nullable();
            $table->integer('last_index')->default(-1);
            $table->timestamps();

            $table->foreign('user_id')
                  ->references('id')
                  ->on(config('database.connections.centrale.database') . '.users')
                  ->onDelete('cascade');
            $table->unique(['user_id', 'category', 'sub_category'], 'unique_user_category');
        });
    }

 
    public function down()
    {
        Schema::dropIfExists('user_message_states');
    }
};
