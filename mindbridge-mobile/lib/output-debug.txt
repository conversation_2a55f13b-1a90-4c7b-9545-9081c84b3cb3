Launching lib/main.dart on IPHONE xs in debug mode...
Warning: CocoaPods recommended version 1.16.2 or greater not installed.
Pods handling may fail on some projects involving plugins.
To update CocoaPods, see https://guides.cocoapods.org/using/getting-started.html#updating-cocoapods

Xcode build done.                                           98,9s
Failed to build iOS app
Error output from Xcode build:
↳
    --- xcodebuild: WARNING: Using the first of multiple matching destinations:
2
    { platform:iOS Simulator, id:0E4C3987-05C0-4AC1-9E83-8048737DBC4E, OS:17.0.1, name:IPHONE xs }
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.533 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.534 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.548 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.548 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.548 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.548 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.548 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.548 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.548 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.548 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.548 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.549 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.550 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.551 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.551 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.551 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.551 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.551 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.551 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.551 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.551 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.551 xcodebuild[31858:1731148] [MT] DVTAssertions: Warning in /System/Volumes/Data/SWE/Apps/DT/BuildRoots/BuildRoot11/ActiveBuildRoot/Library/Caches/com.apple.xbs/Sources/IDEFrameworks/IDEFrameworks-22269/IDEFoundation/Provisioning/Capabilities Infrastructure/IDECapabilityQuerySelection.swift:103
    Details:  createItemModels creation requirements should not create capability item model for a capability item model that already exists.
    Function: createItemModels(for:itemModelSource:)
    Thread:   <_NSMainThread: 0x126609c90>{number = 1, name = main}
    Please file a bug at https://feedbackassistant.apple.com with this warning message and any useful information you can provide.
    2025-10-13 18:01:57.622 xcodebuild[31858:1731197]  DVTPortal: Service '<DVTPortalViewDeveloperService: 0x118de8970; action='viewDeveloper'>' encountered an unexpected result code from the portal ('1100')
    2025-10-13 18:01:57.622 xcodebuild[31858:1731197]  DVTPortal: Error:
    Error Domain=DVTPortalServiceErrorDomain Code=1100 "Your session has expired. Please log in." UserInfo={payload={
        creationTimestamp = "2025-10-13T17:01:57Z";
        httpCode = 200;
        protocolVersion = QH65B2;
        requestUrl = "https://developerservices2.apple.com/services/QH65B2/viewDeveloper.action";
        responseId = "95dd3d09-4f61-4ef4-8b48-6605bad45ad8";
        resultCode = 1100;
        resultString = "Your session has expired. Please log in.";
        userLocale = "en_US";
        userString = "Your session has expired. Please log in.";
    }, NSLocalizedDescription=Your session has expired. Please log in.}
    2025-10-13 18:01:59.143 xcodebuild[31858:1731189]  DVTPortal: Service '<DVTPortalViewDeveloperService: 0x11980d470; action='viewDeveloper'>' encountered an unexpected result code from the portal ('1100')
    2025-10-13 18:01:59.144 xcodebuild[31858:1731189]  DVTPortal: Error:
    Error Domain=DVTPortalServiceErrorDomain Code=1100 "Your session has expired. Please log in." UserInfo={payload={
        creationTimestamp = "2025-10-13T17:01:59Z";
        httpCode = 200;
        protocolVersion = QH65B2;
        requestUrl = "https://developerservices2.apple.com/services/QH65B2/viewDeveloper.action";
        responseId = "f300ef22-f929-41e6-89ec-417c9a7f4ae1";
        resultCode = 1100;
        resultString = "Your session has expired. Please log in.";
        userLocale = "en_US";
        userString = "Your session has expired. Please log in.";
    }, NSLocalizedDescription=Your session has expired. Please log in.}
    2025-10-13 18:02:03.374 xcodebuild[31858:1731197]  DVTPortal: Service '<DVTPortalViewDeveloperService: 0x118a30970; action='viewDeveloper'>' encountered an unexpected result code from the portal ('1100')
    2025-10-13 18:02:03.374 xcodebuild[31858:1731197]  DVTPortal: Error:
    Error Domain=DVTPortalServiceErrorDomain Code=1100 "Your session has expired. Please log in." UserInfo={payload={
        creationTimestamp = "2025-10-13T17:02:03Z";
        httpCode = 200;
        protocolVersion = QH65B2;
        requestUrl = "https://developerservices2.apple.com/services/QH65B2/viewDeveloper.action";
        responseId = "a47e5cff-b693-4209-9ce1-ae0551ce0cb1";
        resultCode = 1100;
        resultString = "Your session has expired. Please log in.";
        userLocale = "en_US";
        userString = "Your session has expired. Please log in.";
    }, NSLocalizedDescription=Your session has expired. Please log in.}
    2025-10-13 18:02:05.409 xcodebuild[31858:1731189]  DVTPortal: Service '<DVTPortalViewDeveloperService: 0x119811590; action='viewDeveloper'>' encountered an unexpected result code from the portal ('1100')
    2025-10-13 18:02:05.409 xcodebuild[31858:1731189]  DVTPortal: Error:
    Error Domain=DVTPortalServiceErrorDomain Code=1100 "Your session has expired. Please log in." UserInfo={payload={
        creationTimestamp = "2025-10-13T17:02:05Z";
        httpCode = 200;
        protocolVersion = QH65B2;
        requestUrl = "https://developerservices2.apple.com/services/QH65B2/viewDeveloper.action";
        responseId = "b14d0f8f-4a2d-4c2b-a17a-cc06bb1583cd";
        resultCode = 1100;
        resultString = "Your session has expired. Please log in.";
        userLocale = "en_US";
        userString = "Your session has expired. Please log in.";
    }, NSLocalizedDescription=Your session has expired. Please log in.}
    2025-10-13 18:02:06.307 xcodebuild[31858:1731197]  DVTPortal: Service '<DVTPortalViewDeveloperService: 0x1180321b0; action='viewDeveloper'>' encountered an unexpected result code from the portal ('1100')
    2025-10-13 18:02:06.307 xcodebuild[31858:1731197]  DVTPortal: Error:
    Error Domain=DVTPortalServiceErrorDomain Code=1100 "Your session has expired. Please log in." UserInfo={payload={
        creationTimestamp = "2025-10-13T17:02:06Z";
        httpCode = 200;
        protocolVersion = QH65B2;
        requestUrl = "https://developerservices2.apple.com/services/QH65B2/viewDeveloper.action";
        responseId = "2db931fe-3a61-4435-972f-b69937bfee6a";
        resultCode = 1100;
        resultString = "Your session has expired. Please log in.";
        userLocale = "en_US";
        userString = "Your session has expired. Please log in.";
    }, NSLocalizedDescription=Your session has expired. Please log in.}
    2025-10-13 18:02:09.427 xcodebuild[31858:1731197]  DVTPortal: Service '<DVTPortalViewDeveloperService: 0x118a33300; action='viewDeveloper'>' encountered an unexpected result code from the portal ('1100')
    2025-10-13 18:02:09.427 xcodebuild[31858:1731197]  DVTPortal: Error:
    Error Domain=DVTPortalServiceErrorDomain Code=1100 "Your session has expired. Please log in." UserInfo={payload={
        creationTimestamp = "2025-10-13T17:02:09Z";
        httpCode = 200;
        protocolVersion = QH65B2;
        requestUrl = "https://developerservices2.apple.com/services/QH65B2/viewDeveloper.action";
        responseId = "c01fd106-9961-49e4-9801-8a063af86611";
        resultCode = 1100;
        resultString = "Your session has expired. Please log in.";
        userLocale = "en_US";
        userString = "Your session has expired. Please log in.";
    }, NSLocalizedDescription=Your session has expired. Please log in.}
    2025-10-13 18:02:10.974 xcodebuild[31858:1731197]  DVTPortal: Service '<DVTPortalViewDeveloperService: 0x1259187f0; action='viewDeveloper'>' encountered an unexpected result code from the portal ('1100')
    2025-10-13 18:02:10.974 xcodebuild[31858:1731197]  DVTPortal: Error:
    Error Domain=DVTPortalServiceErrorDomain Code=1100 "Your session has expired. Please log in." UserInfo={payload={
        creationTimestamp = "2025-10-13T17:02:10Z";
        httpCode = 200;
        protocolVersion = QH65B2;
        requestUrl = "https://developerservices2.apple.com/services/QH65B2/viewDeveloper.action";
        responseId = "60c84905-1dd7-40c8-9ecc-36e5338099c5";
        resultCode = 1100;
        resultString = "Your session has expired. Please log in.";
        userLocale = "en_US";
        userString = "Your session has expired. Please log in.";
    }, NSLocalizedDescription=Your session has expired. Please log in.}
    2025-10-13 18:02:12.763 xcodebuild[31858:1731197]  DVTPortal: Service '<DVTPortalViewDeveloperService: 0x118f6cbc0; action='viewDeveloper'>' encountered an unexpected result code from the portal ('1100')
    2025-10-13 18:02:12.763 xcodebuild[31858:1731197]  DVTPortal: Error:
    Error Domain=DVTPortalServiceErrorDomain Code=1100 "Your session has expired. Please log in." UserInfo={payload={
        creationTimestamp = "2025-10-13T17:02:12Z";
        httpCode = 200;
        protocolVersion = QH65B2;
        requestUrl = "https://developerservices2.apple.com/services/QH65B2/viewDeveloper.action";
        responseId = "eeacb79b-e221-4c50-a1f2-df8f16abeaec";
        resultCode = 1100;
        resultString = "Your session has expired. Please log in.";
        userLocale = "en_US";
        userString = "Your session has expired. Please log in.";
    }, NSLocalizedDescription=Your session has expired. Please log in.}
    ** BUILD FAILED **
2

Xcode's output:
↳
    Writing result bundle at path:
    	/var/folders/yn/kb1frw6j491d67168hlsv4sm0000gn/T/flutter_tools.rO2hrh/flutter_ios_build_temp_dirLgVHje/temporary_xcresult_bundle

    /Users/<USER>/.pub-cache/hosted/pub.dev/audio_waveforms-1.2.0/ios/Classes/AudioRecorder.swift:119:82: warning: capture 'self' was never used
                AVAudioSession.sharedInstance().requestRecordPermission() { [unowned self] allowed in
                                                                                     ^
    /Users/<USER>/.pub-cache/hosted/pub.dev/audio_waveforms-1.2.0/ios/Classes/AudioRecorder.swift:119:82: warning: capture 'self' was never used
                AVAudioSession.sharedInstance().requestRecordPermission() { [unowned self] allowed in
                                                                                     ^
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoIncrementalIndicator.swift:161:124: warning: forming 'UnsafeMutableRawPointer' to an inout variable of type String exposes the internal representation rather than the string contents.
            scrollView.addObserver(self, forKeyPath: DKPhotoIncrementalIndicator.contentSizeKeyPath, options: [.new], context: &DKPhotoIncrementalIndicator.context)
                                                                                          ^
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoIncrementalIndicator.swift:162:126: warning: forming 'UnsafeMutableRawPointer' to an inout variable of type String exposes the internal representation rather than the string contents.
            scrollView.addObserver(self, forKeyPath: DKPhotoIncrementalIndicator.contentOffsetKeyPath, options: [.new], context: &DKPhotoIncrementalIndicator.context)
                                                                                          ^
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoIncrementalIndicator.swift:171:23: warning: forming 'UnsafeMutableRawPointer' to an inout variable of type String exposes the internal representation rather than the string contents.
            if context == &DKPhotoIncrementalIndicator.context {
                          ^
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoGalleryContentVC.swift:39:52: warning: using 'class' keyword to define a class-constrained protocol is deprecated; use 'AnyObject' instead
    internal protocol DKPhotoGalleryContentDataSource: class {
                                                       ^~~~~
                                                       AnyObject
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoGalleryContentVC.swift:55:50: warning: using 'class' keyword to define a class-constrained protocol is deprecated; use 'AnyObject' instead
    internal protocol DKPhotoGalleryContentDelegate: class {
                                                     ^~~~~
                                                     AnyObject
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoGalleryContentVC.swift:39:52: warning: using 'class' keyword to define a class-constrained protocol is deprecated; use 'AnyObject' instead
    internal protocol DKPhotoGalleryContentDataSource: class {
                                                       ^~~~~
                                                       AnyObject
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoGalleryContentVC.swift:55:50: warning: using 'class' keyword to define a class-constrained protocol is deprecated; use 'AnyObject' instead
    internal protocol DKPhotoGalleryContentDelegate: class {
                                                     ^~~~~
                                                     AnyObject
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoIncrementalIndicator.swift:161:124: warning: forming 'UnsafeMutableRawPointer' to an inout variable of type String exposes the internal representation rather than the string contents.
            scrollView.addObserver(self, forKeyPath: DKPhotoIncrementalIndicator.contentSizeKeyPath, options: [.new], context: &DKPhotoIncrementalIndicator.context)
                                                                                          ^
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoIncrementalIndicator.swift:162:126: warning: forming 'UnsafeMutableRawPointer' to an inout variable of type String exposes the internal representation rather than the string contents.
            scrollView.addObserver(self, forKeyPath: DKPhotoIncrementalIndicator.contentOffsetKeyPath, options: [.new], context: &DKPhotoIncrementalIndicator.context)
                                                                                          ^
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoIncrementalIndicator.swift:171:23: warning: forming 'UnsafeMutableRawPointer' to an inout variable of type String exposes the internal representation rather than the string contents.
            if context == &DKPhotoIncrementalIndicator.context {
                          ^
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoGalleryContentVC.swift:39:52: warning: using 'class' keyword to define a class-constrained protocol is deprecated; use 'AnyObject' instead
    internal protocol DKPhotoGalleryContentDataSource: class {
                                                       ^~~~~
                                                       AnyObject
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoGalleryContentVC.swift:55:50: warning: using 'class' keyword to define a class-constrained protocol is deprecated; use 'AnyObject' instead
    internal protocol DKPhotoGalleryContentDelegate: class {
                                                     ^~~~~
                                                     AnyObject
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoGalleryContentVC.swift:39:52: warning: using 'class' keyword to define a class-constrained protocol is deprecated; use 'AnyObject' instead
    internal protocol DKPhotoGalleryContentDataSource: class {
                                                       ^~~~~
                                                       AnyObject
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/DKPhotoGallery/DKPhotoGallery/DKPhotoGalleryContentVC.swift:55:50: warning: using 'class' keyword to define a class-constrained protocol is deprecated; use 'AnyObject' instead
    internal protocol DKPhotoGalleryContentDelegate: class {
                                                     ^~~~~
                                                     AnyObject
    /Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/FilePickerPlugin.m:289:106: warning: 'UIActivityIndicatorViewStyleWhite' is deprecated: first deprecated in iOS 13.0 [-Wdeprecated-declarations]
        UIActivityIndicatorView* indicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
                                                                                          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                                                                          UIActivityIndicatorViewStyleMedium
    In module 'UIKit' imported from /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Target Support Files/file_picker/file_picker-prefix.pch:2:
    /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.0.sdk/System/Library/Frameworks/UIKit.framework/Headers/UIActivityIndicatorView.h:19:5: note: 'UIActivityIndicatorViewStyleWhite' has been explicitly marked deprecated here
        UIActivityIndicatorViewStyleWhite API_DEPRECATED_WITH_REPLACEMENT("UIActivityIndicatorViewStyleMedium", ios(2.0, 13.0), tvos(9.0, 13.0)) API_UNAVAILABLE(visionos) = 1,
        ^
    /Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/FilePickerPlugin.m:405:17: warning: incompatible pointer types assigning to 'NSMutableArray<NSURL *> *' from 'NSArray<NSURL *> *' [-Wincompatible-pointer-types]
            newUrls = urls;
                    ^ ~~~~
    2 warnings generated.
    /Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/FilePickerPlugin.m:289:106: warning: 'UIActivityIndicatorViewStyleWhite' is deprecated: first deprecated in iOS 13.0 [-Wdeprecated-declarations]
        UIActivityIndicatorView* indicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
                                                                                          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                                                                          UIActivityIndicatorViewStyleMedium
    In module 'UIKit' imported from /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Target Support Files/file_picker/file_picker-prefix.pch:2:
    /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.0.sdk/System/Library/Frameworks/UIKit.framework/Headers/UIActivityIndicatorView.h:19:5: note: 'UIActivityIndicatorViewStyleWhite' has been explicitly marked deprecated here
        UIActivityIndicatorViewStyleWhite API_DEPRECATED_WITH_REPLACEMENT("UIActivityIndicatorViewStyleMedium", ios(2.0, 13.0), tvos(9.0, 13.0)) API_UNAVAILABLE(visionos) = 1,
        ^
    /Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/FilePickerPlugin.m:405:17: warning: incompatible pointer types assigning to 'NSMutableArray<NSURL *> *' from 'NSArray<NSURL *> *' [-Wincompatible-pointer-types]
            newUrls = urls;
                    ^ ~~~~
    2 warnings generated.
    ../../../.pub-cache/hosted/pub.dev/skeletonizer-1.4.3/lib/src/painting/skeletonizer_painting_context.dart:106:7: Error: The non-abstract class 'SkeletonizerCanvas' is missing implementations for these members:
skeletonizer_painting_context.dart:106
     - Canvas.clipRSuperellipse
     - Canvas.drawRSuperellipse
    Try to either
     - provide an implementation,
     - inherit an implementation from a superclass or mixin,
     - mark the class as abstract, or
     - provide a 'noSuchMethod' implementation.

    class SkeletonizerCanvas implements Canvas {
          ^^^^^^^^^^^^^^^^^^
    org-dartlang-sdk:///flutter/lib/ui/painting.dart:5923:8: Context: 'Canvas.clipRSuperellipse' is defined here.
      void clipRSuperellipse(RSuperellipse rsuperellipse, {bool doAntiAlias = true});
           ^^^^^^^^^^^^^^^^^
    org-dartlang-sdk:///flutter/lib/ui/painting.dart:6053:8: Context: 'Canvas.drawRSuperellipse' is defined here.
      void drawRSuperellipse(RSuperellipse rsuperellipse, Paint paint);
           ^^^^^^^^^^^^^^^^^
    ../../../.pub-cache/hosted/pub.dev/skeletonizer-1.4.3/lib/src/painting/uniting_painting_context.dart:8:7: Error: The non-abstract class 'UnitingCanvas' is missing implementations for these members:
uniting_painting_context.dart:8
     - Canvas.clipRSuperellipse
     - Canvas.drawRSuperellipse
    Try to either
     - provide an implementation,
     - inherit an implementation from a superclass or mixin,
     - mark the class as abstract, or
     - provide a 'noSuchMethod' implementation.

    class UnitingCanvas implements Canvas {
          ^^^^^^^^^^^^^
    org-dartlang-sdk:///flutter/lib/ui/painting.dart:5923:8: Context: 'Canvas.clipRSuperellipse' is defined here.
      void clipRSuperellipse(RSuperellipse rsuperellipse, {bool doAntiAlias = true});
           ^^^^^^^^^^^^^^^^^
    org-dartlang-sdk:///flutter/lib/ui/painting.dart:6053:8: Context: 'Canvas.drawRSuperellipse' is defined here.
      void drawRSuperellipse(RSuperellipse rsuperellipse, Paint paint);
           ^^^^^^^^^^^^^^^^^
    Target kernel_snapshot_program failed: Exception
    Failed to package /Users/<USER>/Desktop/MindBridge/mindbridge-mobile.
    Command PhaseScriptExecution failed with a nonzero exit code
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 11.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'record_darwin-record_darwin_privacy' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'flutter_secure_storage-flutter_secure_storage' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 11.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'flutter_local_notifications-flutter_local_notifications_privacy' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 11.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'file_picker-file_picker_ios_privacy' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'SwiftyGif-SwiftyGif' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'SwiftyGif' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'SDWebImage-SDWebImage' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'SDWebImage' from project 'Pods')
    note: Run script build phase 'Run Script' will be run during every build because the option to run the script phase "Based on dependency analysis" is unchecked. (in target 'Runner' from project 'Runner')
    note: Run script build phase 'Thin Binary' will be run during every build because the option to run the script phase "Based on dependency analysis" is unchecked. (in target 'Runner' from project 'Runner')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'PromisesObjC-FBLPromises_Privacy' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'PromisesObjC' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'DKPhotoGallery-DKPhotoGallery' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'DKPhotoGallery' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'DKImagePickerController-DKImagePickerController' from project 'Pods')
    /Users/<USER>/Desktop/MindBridge/mindbridge-mobile/ios/Pods/Pods.xcodeproj: warning: The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 9.0, but the range of supported deployment target versions is 12.0 to 17.0.99. (in target 'DKImagePickerController' from project 'Pods')

Could not build the application for the simulator.
Error launching application on IPHONE xs.
