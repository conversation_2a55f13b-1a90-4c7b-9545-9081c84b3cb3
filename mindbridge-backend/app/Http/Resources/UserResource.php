<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        return array_filter(
            [
                "id" => $this->id,
                "name" => $this->name,
                "email" => $this->email,
                "phone" => $this->phone,
                'avatar' => asset('storage/' . $this->avatar),
                "active" => $this->active,
                "roles" => $this->roles,
                "type" => $this->type,
                "fcm_token" => $this->fcm_token,
                "first_login_completed" => $this->first_login_completed,
                // "parrent" => new ParrentResource($this->parrent),
                "created_at" => $this->created_at,
                "updated_at" => $this->updated_at,
            ],
        );
    }
}
