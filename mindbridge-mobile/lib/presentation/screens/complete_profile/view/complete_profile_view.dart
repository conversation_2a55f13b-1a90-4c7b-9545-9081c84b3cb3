import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/core/utilities/extensions.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/test_profiling.dart';
import '../../../widgets/global_error.dart';
import '../../../widgets/global_header.dart';
import '../../../widgets/global_loader.dart';
import '../../../widgets/global_white_card.dart';
import '../../../widgets/gradient_button.dart';
import '../controller/step_controller.dart';

class CompleteProfileView extends StatelessWidget {
  final StepController stepController = Get.find<StepController>();

  CompleteProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const GlobalHeader(),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 5.w),
        child: Column(
          children: [
            SizedBox(height: 2.h),
            Expanded(
              child: GlobalWhiteCard(
                title: AppStrings.aptitudeTestTitle,
                onBackTap: stepController.previousStep,
                child: Obx(() {
                  if (stepController.isLoading.value) {
                    return const Center(child: GlobalLoader());
                  }

                  if (stepController.hasError.value) {
                    return GlobalErrorHandler(
                      onRetry: () {
                        stepController.fetchTestQuestions();
                      },
                    );
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LinearProgressIndicator(
                        value: stepController.currentStep.value /
                            stepController.totalSteps.value,
                        backgroundColor: AppColors.primary.withOpacity(0.2),
                        valueColor:
                            const AlwaysStoppedAnimation(AppColors.primary),
                      ),
                      SizedBox(height: 3.h),
                      Text(
                        'Question ${stepController.currentStep.value} sur ${stepController.totalSteps.value}',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.secondary,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        stepController.currentQuestion,
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.black.withOpacity(0.7),
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Expanded(
                        child: ListView(
                          children: stepController.currentChoices.map(
                            (choice) {
                              return _buildOption(choice, stepController);
                            },
                          ).toList(),
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Obx(() {
                        final isButtonEnabled =
                            stepController.isQuestionAnswered();

                        return GradientButton(
                          text: stepController.currentStep.value ==
                                  stepController.totalSteps.value
                              ? AppStrings.finish
                              : '${AppStrings.next} →',
                          onPressed: isButtonEnabled
                              ? () {
                                  if (stepController.currentStep.value ==
                                      stepController.totalSteps.value) {
                                    stepController.submitSelectedOptions();
                                  } else {
                                    stepController.nextStep();
                                  }
                                }
                              : null,
                        );
                      }),
                    ],
                  );
                }),
              ),
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  Widget _buildOption(Option choice, StepController controller) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 1.h),
      child: InkWell(
        onTap: () {
          controller.selectChoice(choice.id ?? 0);
        },
        child: Row(
          children: [
            Obx(() {
              return Checkbox(
                value: controller.getSelectedChoices().contains(choice.id),
                onChanged: (value) {
                  controller.selectChoice(choice.id ?? 0);
                },
                checkColor: AppColors.white,
                activeColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(0.5.h),
                ),
                side: BorderSide(
                  color: controller.getSelectedChoices().contains(choice.id)
                      ? AppColors.primary
                      : Colors.grey.shade400,
                  width: 0.2.h,
                ),
              );
            }),
            if (choice.icon != null) ...[
              choice.icon!.toIconText(),
              SizedBox(width: 2.w),
            ],
            Expanded(
              child: Text(
                choice.name ?? "No Option Name",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.black,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
