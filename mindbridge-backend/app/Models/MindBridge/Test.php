<?php

namespace App\Models\MindBridge;

use App\Models\MatiereCentrale;
use App\Models\NiveauCentrale;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;
use App\Models\MindBridge\Category;

class Test extends Model
{
    use HasFactory;

    protected $connection = 'mind_bridge';

    protected $table = 'tests';

    protected $fillable = [
        'title',
        'description',
        'type',
        'matiere_id',
        'category_id',
        'created_by',
        'niveau_id',
        'content_id',
        'timer',
        'challenge_date',
        'difficulty_level',
        'challenge_date_start',
        'challenge_date_end',
    ];

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function steps()
    {
        return $this->hasMany(Step::class);
    }

    public function matiere()
    {
        return $this->belongsTo(MatiereCentrale::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function niveau()
    {
        return $this->belongsTo(NiveauCentrale::class, 'niveau_id');
    }

    public function content()
    {
        return $this->belongsTo(Content::class, 'content_id');
    }

    public function chapters()
    {
        return $this->belongsToMany(Chapter::class, 'chapter_test', 'test_id', 'chapter_id');
    }

    public function etudiantTestStatuses()
    {
        return $this->hasMany(EtudiantTestStatus::class, 'test_id');
    }

    public function observations()
    {
        return $this->hasMany(Observation::class, 'test_id');
    }

    /**
     * Get the scoring rules for this test
     */
    public function scoringRules()
    {
        return $this->hasMany(TestScoringRule::class);
    }

}
