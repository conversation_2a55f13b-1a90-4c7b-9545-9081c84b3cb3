<?php

namespace App\Http\Controllers\MindBridge;

use App\Http\Controllers\Controller;
use App\Http\Resources\MindBridge\ContentResource;
use App\Http\Resources\MindBridge\EtudiantMindBridgeResource;
use App\Models\MindBridge\Content;
use App\Models\MindBridge\EtudiantTestAnswers;
use App\Models\MindBridge\EtudiantTestAssignment;
use App\Models\MindBridge\EtudiantTestStatus;
use App\Models\MindBridge\MindBridgeEtudiant;
use App\Models\MindBridge\Option;
use App\Models\MindBridge\Step;
use App\Models\MindBridge\Test;
use App\Models\MindBridge\UserMessageState;
use App\Models\School;
use App\Service\BadgePointService;
use App\Services\ScoringService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class MindBridgeEtudiantController extends Controller
{

    public function index(Request $request)
    {
        $school = $request->input('school');
        $niveau = $request->input('niveau');
        $limit = $request->input('limit', 10);
        $page = $request->input('page', 1);


        $query = MindBridgeEtudiant::with(["niveau", "users", "school"]);


        if ($niveau) {
            $query->where('niveau_id', $niveau);
        }

        if ($school) {
            $query->where('school_id', $school);
        }

        $total = $query->count();
        $etudiants = $query->orderBy('created_at', 'desc')
            ->skip(($page - 1) * $limit)
            ->take($limit)
            ->get();

        $totalPages = ceil($total / $limit);

        return response()->json([
            'data' => EtudiantMindBridgeResource::collection($etudiants),
            'current_page' => $page,
            'total_pages' => $totalPages,
            'per_page' => $limit,
            'total_items' => $total,
        ]);
    }


    public function schoolListe()
    {
        $schools = School::all();

        return response()->json([
            'data' => $schools
        ]);
    }

    public function etudiantTestProfiling()
    {
        try {
            $authUser = auth()->user();
            $etudiant = MindBridgeEtudiant::where('user_id', $authUser->id)->first();

            if (!$etudiant) {
                return response()->json(['error' => 'Etudiant not found'], 404);
            }

            if (!$etudiant->test_profiling_completed) {
                $test = Test::with('steps', 'steps.question', 'steps.question.options')->where('category_id', 3)->first();

                if (!$test) {
                    return response()->json(['error' => 'Test not found'], 404);
                }

                return response()->json([
                    'test' => $test
                ]);
            }
            return response()->json([
                'test' => null
            ]);
        } catch (\Exception $e) {
            \Log::error($e->getMessage());
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }



    public function getMatiersByCategory($category_id)
    {
        try {
            if ($category_id == 10) {
                return response()->json(['message' => "N'est pas dispo pour le moment"], 403);
            }

            if ($category_id == 9) {
                $authUser = auth()->user();
                $etudiant = MindBridgeEtudiant::where('user_id', $authUser->id)->first();

                if (!$etudiant) {
                    return response()->json(['error' => 'Etudiant not found'], 404);
                }

                $niveau = $etudiant->niveau;
                if (!$niveau) {
                    return response()->json(['error' => 'Etudiant Does not belong to a niveau'], 404);
                }

                $niveau->matieres->each(function ($matiere) {
                    $matiere->progress = 0;
                    $matiere->image_url = asset('storage/' . $matiere->image_url);

                    if ($matiere->progress < 25) {
                        $matiere->progress_color = '#FF0000'; // Rouge
                    } elseif ($matiere->progress < 50) {
                        $matiere->progress_color = '#FFA500'; // Orange
                    } elseif ($matiere->progress < 75) {
                        $matiere->progress_color = '#FFFF00'; // Jaune
                    } else {
                        $matiere->progress_color = '#00FF00'; // Vert
                    }
                });

                return response()->json([
                    'matiers' => $niveau->matieres
                ]);
            }

            return response()->json(['error' => 'Invalid category_id'], 400);
        } catch (\Exception $e) {
            \Log::error($e->getMessage());
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }



    public function getLessonByMatier(Request $request)
    {
        try {
            $matierId = $request->input('matiere_id');

            if (!$matierId) {
                return response()->json(['error' => 'matiere_id is required'], 422);
            }

            $authUser = auth()->user();
            $etudiant = MindBridgeEtudiant::where('user_id', $authUser->id)->first();

            $niveau = $etudiant->niveau;

            if (!$niveau) {
                return response()->json(['error' => 'Etudiant Does not belong to a niveau'], 404);
            }

            $contents = Content::where('matiere_id', $matierId)->where('niveau_id', $niveau->id)->get();

            return response()->json([
                'contents' => ContentResource::collection($contents)
            ]);
        } catch (\Exception $e) {
            \Log::error($e->getMessage());
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }
    public function getTestByLesson(Request $request)
    {
        try {
            $contentId = $request->input('content_id');
            if (!$contentId) {
                return response()->json(['error' => 'content_id is required'], 422);
            }

    
            $authUser = auth()->user();
            $etudiant = MindBridgeEtudiant::where('user_id', $authUser->id)->first();
    
            if (!$etudiant) {
                return response()->json(['error' => 'Student not found'], 404);
            }

            $badgePointService = new BadgePointService($etudiant);
            \Log::info("Lesson completed: " . $request->input('lesson_completed'));
            if ($request->input('lesson_completed')) {
                $badgePointService->processAction('lesson_viewed');
            }
    
            $niveau = $etudiant->niveau;
            $difficultyLevels = ['tres_facile', 'facile', 'intermediaire', 'difficile', 'tres_difficile'];
    
            $completedTestIds = EtudiantTestStatus::where('etudiant_id', $etudiant->id)
                ->where('status', 'TERMINE')
                ->pluck('test_id')
                ->toArray();
    
            $completedDifficultyLevels = Test::whereIn('id', $completedTestIds)
                ->where('content_id', $contentId)
                ->where('category_id', 4)
                ->pluck('difficulty_level')
                ->toArray();
    
            $test = null;
            $nextDifficultyLevel = null;
    
            foreach ($difficultyLevels as $level) {
                if (!in_array($level, $completedDifficultyLevels)) {
                    $testsForLevel = Test::where('content_id', $contentId)
                        ->with(['content', 'steps', 'steps.question', 'steps.question.options'])
                        ->where('category_id', 4)
                        ->where('difficulty_level', $level)
                        ->get();
    
                    if ($testsForLevel->isNotEmpty()) {
                        $test = $testsForLevel->first();
                        $remainingTestsForLevel = $testsForLevel->slice(1); // Exclure le premier test déjà sélectionné
    
                        if ($remainingTestsForLevel->isNotEmpty()) {
                            $nextDifficultyLevel = $level; // Il reste d'autres tests pour le même niveau
                        } else {
                            foreach ($difficultyLevels as $nextLevel) {
                                if ($nextLevel != $level && !in_array($nextLevel, $completedDifficultyLevels)) {
                                    $nextTestsForNextLevel = Test::where('content_id', $contentId)
                                        ->where('type', 'test_content')
                                        ->where('difficulty_level', $nextLevel)
                                        ->exists();
    
                                    if ($nextTestsForNextLevel) {
                                        $nextDifficultyLevel = $nextLevel;
                                        break;
                                    }
                                }
                            }
                        }
                        break;
                    }
                }
            }
    
            if (!$test) {
                $test = Test::with('content', 'steps', 'steps.question', 'steps.question.options')
                    ->where('content_id', $contentId)
                    ->where('category_id', 4)
                    ->inRandomOrder()
                    ->first();
    
                if (!$test) {
                    return response()->json(['error' => 'No tests available for this content'], 404);
                }
    
                return response()->json([
                    'test' => $test,
                    'next_difficulty_level' => null,
                    'restarted' => true,
                ], 200);
            }
    
            if ($test->content->niveau_id != $niveau->id) {
                return response()->json(['error' => 'Test content does not match student niveau'], 403);
            }
    
            return response()->json([
                'test' => $test,
                'next_difficulty_level' => $nextDifficultyLevel,
                'restarted' => false,
            ], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }
    


    public function etudiantTestStore(Request $request)
    {
        try {
            $user = auth()->user();
            $gainedPoints = 0;
    
            $request->validate([
                'test_id' => 'required|integer|exists:mind_bridge.tests,id',
                'data' => 'required|array|min:1',
                'data.*.question_id' => 'required|integer|exists:mind_bridge.questions,id',
                'data.*.selected_options' => [
                    'required',
                    'array',
                    'min:1',
                    function ($attribute, $value, $fail) use ($request) {
                        $index = explode('.', $attribute)[1] ?? null;
                        if ($index !== null && isset($request->data[$index]['question_id'])) {
                            $questionId = $request->data[$index]['question_id'];
                            $step = Step::where('question_id', $questionId)->first();
    
                            if ($step && $step->type === 'true_false') {
                                // Valider les réponses de type `true_false`
                                foreach ($value as $option) {
                                    if (!in_array($option, ['is_true', 'is_false'])) {
                                        $fail("Invalid response for true_false question with step ID {$step->id}. Only 'is_true' or 'is_false' are allowed.");
                                    }
                                }
                            } else {
                                // Valider les autres options
                                foreach ($value as $optionId) {
                                    if (!Option::where('id', $optionId)->exists()) {
                                        $fail("Option ID $optionId does not exist for question ID $questionId.");
                                    }
                                }
                            }
                        }
                    },
                ],
            ]);
    
            $etudiant = MindBridgeEtudiant::with(['points', 'badges'])->where('user_id', $user->id)->first();

            $test = Test::with('steps', 'steps.question', 'steps.question.options')->find($request->test_id);

            if (!$etudiant || !$test) {
                return response()->json(['error' => 'Etudiant or Test not found'], 404);
            }

            // Build answers array: [question_id => selected_options]
            $answers = [];
            foreach ($request->data as $questionData) {
                $step = Step::where('question_id', $questionData['question_id'])->first();
                if (!$step) {
                    return response()->json(['error' => "Step not found for question ID {$questionData['question_id']}"], 404);
                }

                $selectedOptions = $questionData['selected_options'];

                if ($step->type === 'true_false') {
                    $selectedOptions = array_filter($selectedOptions, function ($option) {
                        return in_array($option, ['is_true', 'is_false']);
                    });

                    if (empty($selectedOptions)) {
                        return response()->json(['error' => "No valid response provided for true_false step ID {$step->id}"], 422);
                    }
                }

                $answers[$questionData['question_id']] = $selectedOptions;

                EtudiantTestAnswers::updateOrCreate(
                    [
                        'etudiant_id' => $etudiant->id,
                        'question_id' => $step->question_id,
                    ],
                    [
                        'test_id' => $test->id,
                        'selected_options' => json_encode($selectedOptions),
                        'score' => 1, // Mark as answered
                    ]
                );
            }

            // Calculate scores using ScoringService
            $scoringService = new ScoringService();
            $testResults = $scoringService->calculateTestScore($test, $answers);

            // log test results
            \Log::info('Test results: ' . json_encode($testResults));


            $testStatus = EtudiantTestStatus::updateOrCreate(
                [
                    'etudiant_id' => $etudiant->id,
                    'test_id' => $test->id,
                ],
                [
                    'status' => 'TERMINE',
                    'score' => $testResults['total_score'],
                    'percentage' => $testResults['percentage'],
                    'feedback' => $testResults['feedback'],
                    'recommendation' => $testResults['recommendation'],
                    'interpretation' => $testResults['interpretation'],
                    'started_at' => $testStatus->started_at ?? now(),
                    'completed_at' => now(),
                ]
            );
    
            $testAssignment = EtudiantTestAssignment::where('etudiant_id', $etudiant->id)
                ->where('test_id', $test->id)
                ->where('status', 'ATTRIBUE')
                ->first();
    
            if ($testAssignment) {
                $testAssignment->update([
                    'status' => 'TERMINE',
                    'completed_at' => now(),
                ]);
            }
    
            if ($test->category_id === 3) {
                $etudiant->update(['test_profiling_completed' => true]);
            }
    
            $userMessageState = UserMessageState::firstOrCreate(
                [
                    'user_id' => $etudiant->user_id,
                    'category' => $test->category->parent_id ?? null,
                    'sub_category' => $test->category->id ?? null,
                ],
                [
                    'last_index' => -1,
                ]
            );

            $feedbackMessages = $this->getFeedbackMessages($test->category->parent_id ?? null, $test->category->id ?? null);
            $nextMessage = "Merci pour votre participation! Nous apprécions le temps que vous nous avez consacré.";

            if (!empty($feedbackMessages)) {
                $indexedFeedbackMessages = array_values($feedbackMessages);
                $count = count($indexedFeedbackMessages);

                if ($count > 0) {
                    $lastIndex = $userMessageState->last_index ?? -1;
                    $nextIndex = ($lastIndex + 1) % $count;
                    $nextMessage = $indexedFeedbackMessages[$nextIndex];
                    $userMessageState->update(['last_index' => $nextIndex]);
                }
            }


            $badgePpointsSercice = new BadgePointService($etudiant);
            $action_test_type = $request->input('action_test_type');
            \Log::info("Action test type: " . json_encode($action_test_type));

            if ($action_test_type === 'challenge_hebdomadaire') {
                $today = Carbon::today()->format('Y-m-d');
                $weeklyTests = Test::where('category_id', 8)
                    ->where('niveau_id', $etudiant->niveau_id)
                    ->whereDate('challenge_date_start', '<=', $today)
                    ->whereDate('challenge_date_end', '>=', $today)
                    ->get();

                $totalQuestions = 0;
                $correctAnswers = 0;

                $allCompleted = true;
                \Log::info("Weekly tests count: " . $weeklyTests->count());

                foreach ($weeklyTests as $weeklyTest) {
                    $status = EtudiantTestStatus::where(
                        ['etudiant_id' => $etudiant->id, 'test_id' => $weeklyTest->id]
                    )->first();
                    if (!$status || $status->status !== 'TERMINE') {
                        $allCompleted = false;
                        \Log::info("Test not completed: " . $weeklyTest->id);
                        break;
                    }

                    $steps = Step::with(['question', 'question.options'])->whereIn('question_id', $weeklyTest->steps->pluck('question_id'))->get();
                    foreach ($steps as $step) {
                        $totalQuestions++;
                        $answer = EtudiantTestAnswers::where([
                            'etudiant_id' => $etudiant->id,
                            'question_id' => $step->question_id
                        ])->first();

                        if (!$answer) {
                            continue;
                        }
                        \Log::info("Answer found for question ID: " . $step->question_id);

                        $selected = $answer->selected_options;    // now always an array
                        $correctOptions = Option::where('question_id', $step->question_id)
                            ->where('isCorrect', true)
                            ->pluck('id')
                            ->map(fn($id)=> (int)$id)            // ensure ints
                            ->sort()
                            ->values()
                            ->all();

                        if (is_string($selected)) {
                            $decoded = json_decode($selected, true);
                            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                $selected = $decoded;
                            } elseif (strpos($selected, ',') !== false) {
                                $selected = explode(',', $selected);
                            } else {
                                $selected = [$selected];
                            }
                        }
                        elseif (! is_array($selected)) {
                            $selected = (array) $selected;
                        }
                        
                        $selected = array_map('intval', $selected);
                        $correctOptions = array_map('intval', $correctOptions);

                        if ($step->type === 'many') {
                            sort($selected);
                            sort($correctOptions);
                        
                            if ($selected === $correctOptions) {
                                $correctAnswers++;
                            }
                        
                        } else if ($step->type === 'true_false') {
                            $sel = array_shift($selected);
                            if (! in_array($sel, ['is_true','is_false'], true)) {
                                \Log::warning("Invalid TF sel: {$sel}");
                            }
                        
                            if (in_array($sel, $correctOptions, true)) {
                                $correctAnswers++;
                            }
                        
                        } else {
                            $sel = array_shift($selected);
                        
                            if (in_array($sel, $correctOptions, true)) {
                                $correctAnswers++;
                            }
                        }
                            
                            
                    }
                }
                // If all tests were completed, then process actions
                if ($allCompleted) {
                    $gainedPoints += $badgePpointsSercice->processAction('weekly_test_completed');

                    if ($totalQuestions > 0) {
                        $pct = ($correctAnswers / $totalQuestions) * 100;
                        if ($pct >= 80) {
                          $gainedPoints +=  $badgePpointsSercice->processAction('weekly_test_score_over_80');
                        }
                    }
                }
            }

            if ($action_test_type && $action_test_type !== 'challenge_hebdomadaire') {
                $gainedPoints += $badgePpointsSercice->processAction($action_test_type);
            }

            if ($request->input('got_easy_question_right')) {
               $gainedPoints +=  $badgePpointsSercice->processAction('quiz_easy_passed');
            }

            if ($request->input('got_difficult_question_right')) {
               $gainedPoints +=  $badgePpointsSercice->processAction('quiz_hard_passed');
            }
            
    
            return response()->json([
                'message' => 'Tests stored successfully.',
                'feedback_message' => $gainedPoints > 0 ? $nextMessage . "\n Vous avez gagné $gainedPoints points." : $nextMessage,
                'results' => $testResults,
            ]);
        } catch (ValidationException $e) {
            Log::info("Validation error: " . json_encode($e->errors()));
            return response()->json(['errors' => $e->errors()], 422);
        } catch (Exception $e) {
            Log::error('Error in etudiantTestStore: ' . $e);
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    protected function getFeedbackMessages($categoryId, $subCategoryId = null)
    {
        $feedbackMessages = config('motivational_messages.feedback.success');

        if (isset($feedbackMessages[$categoryId])) {
            if ($subCategoryId && isset($feedbackMessages[$categoryId][$subCategoryId])) {
                return $feedbackMessages[$categoryId][$subCategoryId];
            }

            if (isset($feedbackMessages[$categoryId]['default'])) {
                return $feedbackMessages[$categoryId]['default'];
            }
        }

        \Log::info("No feedback messages found for category $categoryId and sub category $subCategoryId");
        return [];
    }

    public function getEtudiantInfos(Request $request, $id)
    {
        try {
            $etudiant = MindBridgeEtudiant::with(['points', 'badges'])->where('user_id', $id)->orWhere('id', $id)->first();
            if (!$etudiant) {
                return response()->json(['error' => 'Etudiant not found'], 404);
            }

            return new EtudiantMindBridgeResource($etudiant);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }

    public function getEtudiantInfoBo($id)
    {
        try {
            $etudiant = MindBridgeEtudiant::with('notes', 'notes.matiere')->where('id', $id)->first();
            if (!$etudiant) {
                return response()->json(['error' => 'Etudiant not found'], 404);
            }

            return (new EtudiantMindBridgeResource($etudiant))->response()->setStatusCode(200);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }
}
