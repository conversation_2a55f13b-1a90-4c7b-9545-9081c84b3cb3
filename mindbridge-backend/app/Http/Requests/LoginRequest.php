<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            "institue" => 'nullable|string',
            "email" => 'nullable|email|required_without:phone',
            "phone" => 'nullable|string|required_without:email',
            "password" => 'required',
            'fcm_token' => 'nullable|string'
        ];
    }

    /**
     * Get the custom validation error messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'email.required_without' => trans('globalConst.auth.required_email_or_phone'),
            'phone.required_without' => trans('globalConst.auth.required_phone_or_email'),
            'password.required' => trans('globalConst.auth.required_password'),
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param Validator $validator
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator): void
    {
        \Log::error("====== LoginRequest => Validation Errors ======\n" . json_encode($validator->errors()->toArray(), JSON_PRETTY_PRINT));
        throw new HttpResponseException(response()->json($validator->errors(), 400));
    }

    protected function isUpdating(): bool
    {
        return $this->isMethod('patch') || $this->isMethod('put') || ($this->isMethod('post') && $this->id);
    }
}
