<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('etudiant_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ep_etudiant_id')
                ->nullable()
                ->constrained('etudiants')
                ->cascadeOnDelete();
            
            $table->integer('ep_number_points')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('etudiant_points');
    }
};
