import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/core/models/recommandation.dart';
import 'package:mindbridge/core/utilities/extensions.dart';
import 'package:mindbridge/presentation/widgets/flat_button.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../widgets/btm_navigation_bar.dart';
import '../../../widgets/global_error.dart';
import '../../../widgets/global_header.dart';
import '../../../widgets/global_loader.dart';
import '../../../widgets/global_white_card.dart';
import '../../../widgets/gradient_button.dart';
import '../controller/recommandation_controller.dart';

class RecommandationView extends GetView<RecommandationController> {

  const RecommandationView({super.key});


  @override
  Widget build(BuildContext context) {
    final recoCtrl = Get.find<RecommandationController>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      recoCtrl.fetchRecommandationData();
    });

    return BaseScreen(
      child: Scaffold(
        appBar: const GlobalHeader(),
        body: Obx(() {
          return Stack(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 5.w),
                child: Column(
                  children: [
                    SizedBox(height: 2.h),
                    Flexible(
                      child: GlobalWhiteCard(
                        title: AppStrings.recommandationMindbridge,
                        onBackTap: recoCtrl.previousStep,
                        child: recoCtrl.isLoading.value
                            ? const Center(child: GlobalLoader())
                            : recoCtrl.isError.value
                                ? GlobalErrorHandler(
                                    onRetry: () {
                                      recoCtrl.fetchRecommandationData();
                                    },
                                  )
                                : recoCtrl.isEmptyData.value
                                    ? const SizedBox.shrink()
                                    : Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          LinearProgressIndicator(
                                              value: recoCtrl.totalSteps == 0 || 
                                                    recoCtrl.currentStep.value.isInfinite || 
                                                    recoCtrl.currentStep.value.isNaN
                                                  ? 0.0
                                                  : (recoCtrl.currentStep.value / recoCtrl.totalSteps).clamp(0.0, 1.0),
                                              backgroundColor: AppColors.primary.withOpacity(0.2),
                                              valueColor: const AlwaysStoppedAnimation(AppColors.primary),
),
                                          SizedBox(height: 3.h),
                                          Text(
                                            'Question ${recoCtrl.currentStep.value} sur ${recoCtrl.totalSteps}',
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.secondary,
                                            ),
                                          ),
                                          SizedBox(height: 1.h),
                                          Text(
                                            recoCtrl.currentQuestion,
                                            style: TextStyle(
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.black
                                                  .withOpacity(0.7),
                                            ),
                                          ),
                                          if (recoCtrl
                                              .currentQuestionDescription
                                              .isNotEmpty)
                                            Padding(
                                              padding:
                                                  EdgeInsets.only(top: 1.h),
                                              child: Text(
                                                recoCtrl
                                                    .currentQuestionDescription,
                                                style: TextStyle(
                                                  fontSize: 11.sp,
                                                  color: AppColors.black
                                                      .withOpacity(0.6),
                                                ),
                                              ),
                                            ),
                                          SizedBox(height: 2.h),
                                          Expanded(
                                            child:
                                                recoCtrl.currentQuestionType ==
                                                        'true_false'
                                                    ? _buildTrueFalseOptions(
                                                        recoCtrl)
                                                    : ListView(
                                                        children: recoCtrl
                                                            .currentChoices
                                                            .map((choice) =>
                                                                _buildOption(
                                                                    choice,
                                                                    recoCtrl))
                                                            .toList(),
                                                      ),
                                          ),
                                        ],
                                      ),
                      ),
                    ),
                    SizedBox(height: 2.h),
                    recoCtrl.isLoading.value ||
                            recoCtrl.isError.value ||
                            recoCtrl.isEmptyData.value
                        ? const SizedBox.shrink()
                        : Row(
                            children: [
                              Expanded(
                                child: FlatButtonGlobal(
                                  text: AppStrings.evaluationPrevious,
                                  onPressed: recoCtrl.previousStep,
                                  useGradient: true,
                                ),
                              ),
                              SizedBox(width: 4.w),
                              Expanded(
                                child: Obx(() {
                                  final isButtonEnabled =
                                      recoCtrl.isQuestionAnswered();
                                  final isLastQuestion =
                                      recoCtrl.currentStep.value ==
                                          recoCtrl.totalSteps;

                                  return GradientButton(
                                    text: isLastQuestion
                                        ? AppStrings.finish
                                        : '${AppStrings.next} →',
                                    onPressed: isButtonEnabled
                                        ? () {
                                            recoCtrl.revealAndGoNext();
                                          }
                                        : null,
                                  );
                                }),
                              ),
                            ],
                          ),
                    SizedBox(height: 7.h),
                  ],
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildTrueFalseOptions(RecommandationController recoCtrl) {
    final question = recoCtrl.currentQuestionObj?.question;
    final isRevealed = recoCtrl.isRevealedForCurrentQuestion;
    final selectedChoices = recoCtrl.getSelectedChoices();

    // use the actual answer boolean
    final correctAnswer = question?.answer ?? false;

    // true is correct if answer==true; false is correct if answer==false
    final isTrueCorrect = correctAnswer;
    final isFalseCorrect = !correctAnswer;

    final isTrueSelected = selectedChoices.contains(1);
    final isFalseSelected = selectedChoices.contains(0);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildTrueFalseOption(
          recoCtrl: recoCtrl,
          text: "Vrai",
          icon: Icons.check,
          isSelected: isTrueSelected,
          isCorrect: isRevealed && isTrueCorrect,
          isIncorrect: isRevealed && isTrueSelected && !isTrueCorrect,
          choiceId: 1,
        ),
        _buildTrueFalseOption(
          recoCtrl: recoCtrl,
          text: "Faux",
          icon: Icons.close,
          isSelected: isFalseSelected,
          isCorrect: isRevealed && isFalseCorrect,
          isIncorrect: isRevealed && isFalseSelected && !isFalseCorrect,
          choiceId: 0,
        ),
      ],
    );
  }

  Widget _buildTrueFalseOption({
    required RecommandationController recoCtrl,
    required String text,
    required IconData icon,
    required bool isSelected,
    required bool isCorrect,
    required bool isIncorrect,
    required int choiceId,
  }) {
    final defaultColor = choiceId == 1 ? Colors.green : Colors.red;
    final bgColor = isCorrect
        ? Colors.green.withOpacity(0.2)
        : isIncorrect
            ? Colors.red.withOpacity(0.2)
            : isSelected
                ? Colors.grey.shade300
                : Colors.transparent;

    return InkWell(
      onTap: recoCtrl.isRevealedForCurrentQuestion
          ? null
          : () {
              final questionId = recoCtrl.currentQuestionObj?.questionId ?? -1;
              recoCtrl.selectedAnswers.update(
                questionId,
                (existing) =>
                    existing.contains(choiceId) ? [] : [choiceId],
                ifAbsent: () => [choiceId],
              );
            },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 1.h, horizontal: 4.w),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(0.5.h),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: defaultColor, size: 18.sp),
            SizedBox(width: 2.w),
            Text(
              text,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
                color: defaultColor,
              ),
            ),
          ],
        ),
      ),
    );
  }


  Widget _buildOption(Option choice, RecommandationController recoCtrl) {
    return Obx(() {
      final questionId = recoCtrl.currentQuestionObj?.questionId ?? -1;
      final isRevealed = recoCtrl.revealedQuestions[questionId] ?? false;
      final selectedChoices = recoCtrl.getSelectedChoices();
      final isSelected = selectedChoices.contains(choice.id);

      bool showBorderOnly = false;
      bool highlightGreen = false;
      bool highlightRed = false;

      if (!isRevealed) {
        if (isSelected) {
          showBorderOnly = true;
        }
      } else {
        if (choice.isCorrect == 1) {
          highlightGreen = true;
        } else if (isSelected && (choice.isCorrect == 0)) {
          highlightRed = true;
        }
      }

      Color borderColor = Colors.grey.shade400;
      Color bgColor = Colors.transparent;

      if (highlightGreen) {
        borderColor = Colors.green;
        bgColor = Colors.green.withOpacity(0.2);
      } else if (highlightRed) {
        borderColor = Colors.red;
        bgColor = Colors.red.withOpacity(0.2);
      } else if (showBorderOnly) {
        borderColor = AppColors.primary;
      }

      return Container(
        margin: EdgeInsets.symmetric(vertical: 1.h),
        padding: EdgeInsets.symmetric(vertical: 0.5.h),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(1.h),
          border: Border.all(
            color: borderColor,
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: isRevealed
              ? null
              : () {
                  recoCtrl.selectChoice(choice.id!);
                },
          child: Row(
            children: [
              Checkbox(
                value: isSelected,
                onChanged: isRevealed
                    ? null
                    : (bool? checked) {
                        if (checked == true) {
                          recoCtrl.selectChoice(choice.id!);
                        }
                      },
                checkColor: AppColors.white,
                activeColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(0.5.h),
                ),
                side: BorderSide(
                  color: borderColor,
                  width: 0.2.h,
                ),
              ),
              if (choice.icon != null) ...[
                choice.icon!.toIconText(),
                SizedBox(width: 2.w),
              ],
              Expanded(
                child: Text(
                  choice.name ?? '',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.black,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
