
export type ContenueResponse = {
  id: number,
  type: string,
  title: string,
  niveau: Niveau,
  matiere: Matiere,
  chapter: Chapter,
  file: File,
  description: string
}

export type Niveau = {
  id: number,
  name: string,
  description: string,
  created_at: string,
  updated_at: string,
  deleted_at: string

};

export type Matiere = {
  id: number,
  name_fr: string,
  name_ar: string,
  description: string,
  created_at: string,
  updated_at: string,
  deleted_at: string
};

export interface Chapter {
  id: number,
  chapter: string,
  description: string,
  niveau_id: number,
  matiere_id: number,
  updated_at: string,
  created_at: string,
}
