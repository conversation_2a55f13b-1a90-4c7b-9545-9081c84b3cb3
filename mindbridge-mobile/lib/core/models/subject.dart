// To parse this JSON data, do
//
//     final subject = subjectFromJson(jsonString);

import 'dart:convert';

Subject subjectFromJson(String str) => Subject.fromJson(json.decode(str));

String subjectToJson(Subject data) => json.encode(data.toJson());

class Subject {
  List<Matier>? matiers;

  Subject({
    this.matiers,
  });

  factory Subject.fromJson(Map<String, dynamic> json) => Subject(
        matiers: json["matiers"] == null
            ? []
            : List<Matier>.from(
                json["matiers"]!.map((x) => Matier.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "matiers": matiers == null
            ? []
            : List<dynamic>.from(matiers!.map((x) => x.toJson())),
      };
}

class Matier {
  int? id;
  String? nameFr;
  String? nameAr;
  String? titleColor;
  dynamic description;
  String? imageUrl;
  String? gradientBackground;
  String? gradientBorder;
  dynamic createdAt;
  dynamic updatedAt;
  dynamic deletedAt;
  int? progress;
  String? progressColor;
  Pivot? pivot;

  Matier({
    this.id,
    this.nameFr,
    this.nameAr,
    this.titleColor,
    this.description,
    this.imageUrl,
    this.gradientBackground,
    this.gradientBorder,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.progress,
    this.progressColor,
    this.pivot,
  });

  factory Matier.fromJson(Map<String, dynamic> json) => Matier(
        id: json["id"],
        nameFr: json["name_fr"],
        nameAr: json["name_ar"],
        titleColor: json["title_color"],
        description: json["description"],
        imageUrl: json["image_url"],
        gradientBackground: json["gradient_background"],
        gradientBorder: json["gradient_border"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        deletedAt: json["deleted_at"],
        progress: json["progress"],
        progressColor: json["progress_color"],
        pivot: json["pivot"] == null ? null : Pivot.fromJson(json["pivot"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name_fr": nameFr,
        "name_ar": nameAr,
        "title_color": titleColor,
        "description": description,
        "image_url": imageUrl,
        "gradient_background": gradientBackground,
        "gradient_border": gradientBorder,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "deleted_at": deletedAt,
        "progress": progress,
        "progress_color": progressColor,
        "pivot": pivot?.toJson(),
      };
}

class Pivot {
  int? niveauId;
  int? matiereId;
  dynamic createdAt;
  dynamic updatedAt;

  Pivot({
    this.niveauId,
    this.matiereId,
    this.createdAt,
    this.updatedAt,
  });

  factory Pivot.fromJson(Map<String, dynamic> json) => Pivot(
        niveauId: json["niveau_id"],
        matiereId: json["matiere_id"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "niveau_id": niveauId,
        "matiere_id": matiereId,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}
