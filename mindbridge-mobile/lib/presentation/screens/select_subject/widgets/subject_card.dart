import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:mindbridge/app/routes.dart';
import 'package:mindbridge/core/utilities/extensions.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/subject.dart';

class SubjectCard extends StatelessWidget {
  final Matier subject;

  const SubjectCard({
    super.key,
    required this.subject,
  });

  @override
  Widget build(BuildContext context) {
    double progressValue = (subject.progress ?? 0).toDouble();
    Color progressColor = subject.progressColor?.toColor() ?? Colors.black;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: subject.gradientBackground.toGradient(),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: GradientBoxBorder(
          gradient: LinearGradient(colors: subject.gradientBorder.toGradient()),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Get.toNamed(Routes.selectLesson, arguments: {
            "subjectTitle": subject.nameFr ?? "Untitled",
            "matiereId": subject.id!
          });
        },
        child: Stack(
          children: [
            Positioned(
              top: 0,
              bottom: 0,
              right: 0,
              child: (subject.imageUrl ?? "").toImage(
                fit: BoxFit.contain,
              ),
            ),
            Row(
              children: [
                Expanded(
                  flex: 4,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Tooltip(
                        message: subject.nameFr ?? "Untitled",
                        child: Text(
                          subject.nameFr ?? "Untitled",
                          style: TextStyle(
                            fontSize: 10.sp,
                            fontWeight: FontWeight.bold,
                            color: subject.titleColor!.toColor(),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(height: 1.h),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: EdgeInsets.all(1.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppStrings.progress,
                              style: TextStyle(
                                fontSize: 8.sp,
                                color: Colors.black,
                              ),
                            ),
                            SizedBox(height: 0.5.h),
                            Row(
                              children: [
                                Expanded(
                                  child: LinearProgressIndicator(
                                    value: progressValue / 100,
                                    backgroundColor:
                                        Colors.white.withOpacity(0.5),
                                    valueColor: AlwaysStoppedAnimation(
                                      progressColor,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 1.w),
                                Text(
                                  '${subject.progress ?? 0}%',
                                  style: TextStyle(
                                    fontSize: 8.sp,
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 2.w),
                const Expanded(flex: 1, child: SizedBox.shrink())
              ],
            ),
          ],
        ),
      ),
    );
  }
}
