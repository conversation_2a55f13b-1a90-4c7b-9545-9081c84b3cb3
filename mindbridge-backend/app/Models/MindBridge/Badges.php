<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Badges extends Model
{
    use HasFactory;
    protected $connection = 'mind_bridge';

    protected $table = 'badges';

    protected $fillable = [
        'bdg_name',
        'bdg_description',
        'bdg_locked_icon_url',
        'bdg_unlocked_icon_url',
        'bdg_required_points',
    ];  
}