// To parse this JSON data, do
//
//     final profile = profileFromJson(jsonString);

import 'dart:convert';

Profile profileFromJson(String str) => Profile.fromJson(json.decode(str));

String profileToJson(Profile data) => json.encode(data.toJson());

class Profile {
    final int? id;
    final String? name;
    final String? firstName;
    final String? lastName;
    final String? email;
    final String? avatar;
    final dynamic identifiant;
    final int? tauxEngagement;
    final int? heureMoyen;
    final int? scores;
    final dynamic nameGamification;
    final dynamic nni;
    final String? testProfilingCompleted;
    final Niveau? niveau;
    final School? school;
    final int? classement;
    final int? countEtudiant;
    final List<Badge>? badges;
    final dynamic points;

    Profile({
        this.id,
        this.name,
        this.firstName,
        this.lastName,
        this.email,
        this.avatar,
        this.identifiant,
        this.tauxEngagement,
        this.heureMoyen,
        this.scores,
        this.nameGamification,
        this.nni,
        this.testProfilingCompleted,
        this.niveau,
        this.school,
        this.classement,
        this.countEtudiant,
        this.badges,
        this.points,
    });

    factory Profile.fromJson(Map<String, dynamic> json) => Profile(
        id: json["id"],
        name: json["name"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        email: json["email"],
        avatar: json["avatar"],
        identifiant: json["identifiant"],
        tauxEngagement: json["taux_engagement"],
        heureMoyen: json["heure_moyen"],
        scores: json["scores"],
        nameGamification: json["name_gamification"],
        nni: json["nni"],
        testProfilingCompleted: json["test_profiling_completed"],
        niveau: json["niveau"] == null ? null : Niveau.fromJson(json["niveau"]),
        school: json["school"] == null ? null : School.fromJson(json["school"]),
        classement: json["classement"],
        countEtudiant: json["count_etudiant"],
        badges: json["badges"] == null ? [] : List<Badge>.from(json["badges"]!.map((x) => Badge.fromJson(x))),
        points: json["points"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "first_name": firstName,
        "last_name": lastName,
        "email": email,
        "avatar": avatar,
        "identifiant": identifiant,
        "taux_engagement": tauxEngagement,
        "heure_moyen": heureMoyen,
        "scores": scores,
        "name_gamification": nameGamification,
        "nni": nni,
        "test_profiling_completed": testProfilingCompleted,
        "niveau": niveau?.toJson(),
        "school": school?.toJson(),
        "classement": classement,
        "count_etudiant": countEtudiant,
        "badges": badges == null ? [] : List<dynamic>.from(badges!.map((x) => x.toJson())),
        "points": points,
    };
}

class Badge {
    final int? id;
    final String? name;
    final String? iconUrl;
    final bool? unlocked;

    Badge({
        this.id,
        this.name,
        this.iconUrl,
        this.unlocked,
    });

    factory Badge.fromJson(Map<String, dynamic> json) => Badge(
        id: json["id"],
        name: json["name"],
        iconUrl: json["icon_url"],
        unlocked: json["unlocked"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "icon_url": iconUrl,
        "unlocked": unlocked,
    };
}

class Niveau {
    final int? id;
    final String? name;
    final String? color;
    final String? background;
    final dynamic description;
    final dynamic createdAt;
    final dynamic updatedAt;
    final dynamic deletedAt;

    Niveau({
        this.id,
        this.name,
        this.color,
        this.background,
        this.description,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
    });

    factory Niveau.fromJson(Map<String, dynamic> json) => Niveau(
        id: json["id"],
        name: json["name"],
        color: json["color"],
        background: json["background"],
        description: json["description"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        deletedAt: json["deleted_at"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "color": color,
        "background": background,
        "description": description,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "deleted_at": deletedAt,
    };
}

class School {
    final int? id;
    final dynamic organizationId;
    final String? name;
    final String? domain;
    final String? database;
    final DateTime? createdAt;
    final dynamic updatedAt;
    final String? migrationsPath;

    School({
        this.id,
        this.organizationId,
        this.name,
        this.domain,
        this.database,
        this.createdAt,
        this.updatedAt,
        this.migrationsPath,
    });

    factory School.fromJson(Map<String, dynamic> json) => School(
        id: json["id"],
        organizationId: json["organization_id"],
        name: json["name"],
        domain: json["domain"],
        database: json["database"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"],
        migrationsPath: json["migrations_path"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "organization_id": organizationId,
        "name": name,
        "domain": domain,
        "database": database,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt,
        "migrations_path": migrationsPath,
    };
}
