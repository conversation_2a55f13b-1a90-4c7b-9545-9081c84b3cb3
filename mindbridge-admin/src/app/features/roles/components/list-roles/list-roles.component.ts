import { Component, inject } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AddRoleComponent } from '../add-role/add-role.component';


@Component({
    selector: 'app-list-roles',
    templateUrl: './list-roles.component.html',
    styleUrl: './list-roles.component.scss',
    imports: []
})
export class ListRolesComponent {
  private dialog = inject(MatDialog);


  add() {
    this.dialog.open(AddRoleComponent, {
      data: {}
    });
  }
}
