import { TypeQuestionEnum } from './type-question.enum';
import { DifficultyLevelEnum } from './difficulty-level-enum';

export class TestUpdateRequestWrapperComplete {
  id: number;
  testUpdateRequestWrapper: TestUpdateRequestWrapper;
  constructor() {
    this.id = null;
    this.testUpdateRequestWrapper = new TestUpdateRequestWrapper();
  }
}

export class TestUpdateRequestWrapper {
  test: TestUpdateRequest;
  steps: TestUpdateRequestStep[];

  constructor() {
    this.test = new TestUpdateRequest();
    this.steps = [];
  }
}

export class TestUpdateRequest {
  title: string;
  description: string;
  category_id: number;
  matiere_id: number;
  niveau_id: number;
  content_id: number;
  timer: number;
  challenge_date: Date | string;
  difficulty_level: DifficultyLevelEnum;

  constructor() {
    this.title = '';
    this.description = '';
    this.category_id = null;
    this.matiere_id = null;
    this.niveau_id = null;
    this.content_id = null;
    this.timer = null;
    this.challenge_date = null;
    this.difficulty_level = null;
  }
}

export class TestUpdateRequestStep {
  type: TypeQuestionEnum;
  order: number;
  question?: TestUpdateRequestQuestion;

  constructor() {
    this.type = null;
    this.order = null;
    this.question = undefined;
  }
}

export class TestUpdateRequestQuestion {
  id?: number;
  type: string;
  content: string;
  description: string;
  image_path: Blob | string;
  required: boolean;
  answer?: boolean;
  is_true?: boolean;
  is_false?: boolean;
  options?: TestUpdateRequestQuestionOption[];

  constructor() {
    this.id = null;
    this.type = 'text';
    this.content = '';
    this.description = '';
    this.image_path = '';
    this.required = true;
    this.options = [new TestUpdateRequestQuestionOption()];
  }
}

export class TestUpdateRequestQuestionOption {
  id?: number;
  name: string;
  icon?: string;
  isCorrect?: boolean;

  constructor() {
    this.id = null;
    this.name = '';
    this.icon = null;
    this.isCorrect = false;
  }
}
