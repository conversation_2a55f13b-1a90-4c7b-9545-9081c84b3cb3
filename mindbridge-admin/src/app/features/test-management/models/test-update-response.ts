export interface TestUpdateResponseWrapper {
  message: string;
  object: TestUpdateResponse;
}

export interface TestUpdateResponse {
  id: 18;
  title: string;
  description: string;
  target: string;
  type: string;
  timer: Date;
  challenge_date: Date;
  difficulty_level: string;
  matiere_id: number;
  category_id: number;
  created_by: number;
  niveau_id: number;
  content_id: number;
  created_at: Date;
  updated_at: Date;
}
