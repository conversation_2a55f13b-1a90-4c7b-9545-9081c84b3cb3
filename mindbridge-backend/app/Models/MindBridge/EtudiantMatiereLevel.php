<?php

namespace App\Models\MindBridge;

use App\Models\MatiereCentrale;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EtudiantMatiereLevel extends Model
{
    use HasFactory;
    protected $connection = 'mind_bridge';

    protected $table = 'etudiant_matiere_level';

    protected $fillable = [
        'etudiant_id',
        'matiere_id',
        'level',
    ]; 

    public function etudiant()
    {
        return $this->belongsTo(MindBridgeEtudiant::class, 'etudiant_id');
    }
    public function matiere()
    {
        return $this->belongsTo(MatiereCentrale::class, 'matiere_id');
    }


}
