<div class="flex-center my-2 flex gap-4">
  <div
    class="flex h-max w-max flex-col items-start justify-start rounded-lg border border-gray-200 bg-white p-2"
  >
    @for (test of categoryTests(); track test) {
      <ng-container>
        <button
          class="w-44 cursor-pointer p-2 text-[#667085] transition duration-300 hover:bg-[#F9FAFB] hover:text-[#344054]"
          [ngClass]="{
            'bg-[#F9FAFB] text-[#344054]':
              activeTest.id === test.id && !modeCreation,
          }"
          [ngStyle]="{
            color:
              activeTest.id === test.id && !modeCreation
                ? '#344054'
                : '#667085',
          }"
          (click)="setActiveTest(test)"
        >
          {{ test?.title }}
        </button>
      </ng-container>
    }
    <button
      class="mt-1.5 flex w-44 gap-2 p-2 text-[#6BA4BD]"
      (click)="createTest()"
    >
      <img class="h-5 w-5" src="/assets/icones/plus_circle_white.png" alt="" />
      Add new test
    </button>
  </div>
  <div
    class="flex h-full w-full flex-col items-start justify-start rounded-lg border border-gray-200 bg-white p-2 max-md:w-screen"
  >
    <ng-container>
      @if (modeCreation) {
        <div class="w-full max-md:w-screen">



          <div>
            Code :  {{ category.code }}
          </div>

          <app-add-test [type]="category.code" [category_id]="category.id"></app-add-test>
        </div>
      } @else {
        @if (categoryTests()) {
          <div class="tab-pane w-full max-md:w-screen">
            <app-category-test-details
              [test]="activeTest"
            ></app-category-test-details>
          </div>
        }
      }
    </ng-container>
  </div>
</div>
