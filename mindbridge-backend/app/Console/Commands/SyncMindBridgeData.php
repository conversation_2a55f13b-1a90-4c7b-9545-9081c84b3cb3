<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\MindBridgeDataSyncSeeder;
use Illuminate\Support\Facades\Log;

class SyncMindBridgeData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mindbridge:sync-data 
                            {--force : Force synchronization without confirmation}
                            {--dry-run : Show what would be synchronized without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize MindBridge database data with the central database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 MindBridge Data Synchronization Tool');
        $this->info('=====================================');

        if ($this->option('dry-run')) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made');
            $this->performDryRun();
            return 0;
        }

        if (!$this->option('force')) {
            $this->warn('⚠️  This command will synchronize data from MindBridge database to the central database.');
            $this->warn('   This may create new records in the central database.');
            
            if (!$this->confirm('Do you want to continue?')) {
                $this->info('❌ Synchronization cancelled.');
                return 0;
            }
        }

        $this->info('🚀 Starting data synchronization...');

        try {
            // Run the seeder
            $seeder = new MindBridgeDataSyncSeeder();
            $seeder->run();

            $this->info('✅ Data synchronization completed successfully!');
            $this->info('📋 Summary:');
            $this->info('   - Users synchronized from MindBridge to Central');
            $this->info('   - Educational levels (Niveaux) synchronized');
            $this->info('   - Subjects (Matieres) synchronized');
            $this->info('   - Relationships updated');
            $this->info('   - MindBridge references updated');

        } catch (\Exception $e) {
            $this->error('❌ Error during synchronization: ' . $e->getMessage());
            Log::error('MindBridge sync command error: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Perform a dry run to show what would be synchronized
     */
    private function performDryRun()
    {
        $this->info('🔍 Analyzing MindBridge database...');

        try {
            // Check what data exists in MindBridge
            $this->checkMindBridgeData();
            
        } catch (\Exception $e) {
            $this->error('❌ Error during dry run: ' . $e->getMessage());
        }
    }

    /**
     * Check and display MindBridge data that would be synchronized
     */
    private function checkMindBridgeData()
    {
        // Check users
        $etudiantUsers = \DB::connection('mind_bridge')
            ->table('etudiants')
            ->whereNotNull('user_id')
            ->distinct()
            ->count('user_id');

        $testCreators = \DB::connection('mind_bridge')
            ->table('tests')
            ->whereNotNull('created_by')
            ->distinct()
            ->count('created_by');

        $this->info("👥 Users to sync: {$etudiantUsers} students + {$testCreators} test creators");

        // Check niveaux
        $niveauxCount = \DB::connection('mind_bridge')
            ->table('etudiants')
            ->whereNotNull('niveau_id')
            ->distinct()
            ->count('niveau_id');

        $this->info("📚 Educational levels to sync: {$niveauxCount}");

        // Check matieres
        $matieresCount = \DB::connection('mind_bridge')
            ->table('tests')
            ->whereNotNull('matiere_id')
            ->distinct()
            ->count('matiere_id');

        $this->info("📖 Subjects to sync: {$matieresCount}");

        // Check tests
        $testsCount = \DB::connection('mind_bridge')->table('tests')->count();
        $this->info("🧪 Tests in MindBridge: {$testsCount}");

        // Check questions
        $questionsCount = \DB::connection('mind_bridge')->table('questions')->count();
        $this->info("❓ Questions in MindBridge: {$questionsCount}");

        // Check contents
        $contentsCount = \DB::connection('mind_bridge')->table('contents')->count();
        $this->info("📄 Contents in MindBridge: {$contentsCount}");

        // Check etudiants
        $etudiantsCount = \DB::connection('mind_bridge')->table('etudiants')->count();
        $this->info("🎓 Students in MindBridge: {$etudiantsCount}");

        $this->info('');
        $this->info('📋 What would be synchronized:');
        $this->info('   ✓ Missing users would be created in central database');
        $this->info('   ✓ Missing educational levels would be created');
        $this->info('   ✓ Missing subjects would be created');
        $this->info('   ✓ Niveau-Matiere relationships would be established');
        $this->info('   ✓ MindBridge school reference would be ensured');
        $this->info('   ✓ User-school relationships would be created');
    }
}
