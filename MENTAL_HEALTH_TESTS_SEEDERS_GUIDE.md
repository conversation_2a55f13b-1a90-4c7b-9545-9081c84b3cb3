# Mental Health Tests - Seeders Guide

## 📋 Overview

MindBridge includes comprehensive database seeders that populate the mental health tests system with sample data. These seeders are essential for development, testing, and initial deployment.

---

## 🌱 Available Seeders

### 1. **MentalHealthTestsSeeder** ⭐ (Main Seeder)
**File:** `mindbridge-backend/database/seeders/mindBridge/MentalHealthTestsSeeder.php`

**Purpose:** Creates all mental health tests with their questions and options

**What it creates:**
- 4 main test categories (Cognitive, Emotional, Behavioral, Social)
- Multiple tests per category
- Questions with different types (one, many, true_false)
- Answer options for each question
- Steps/order for questions

**Test Categories Created:**
1. **Cognitive Tests** (Category ID: 19-20)
   - Test de Mémoire et Attention
   - Test de Résolution de Problèmes
   - Test de Créativité et Innovation

2. **Emotional Tests** (Category ID: 21-23)
   - Test de Gestion du Stress
   - Test de Gestion des Émotions
   - Test d'Estime de Soi

3. **Behavioral Tests** (Category ID: 24-26)
   - Test de Motivation et Engagement
   - Test de Discipline et Persévérance
   - Test de Gestion du Temps

4. **Social Tests** (Category ID: 27-29)
   - Test de Communication
   - Test d'Empathie et Compassion
   - Test d'Interactions Sociales

---

### 2. **CategoriesSeeder**
**File:** `mindbridge-backend/database/seeders/mindBridge/CategoriesSeeder.php`

**Purpose:** Creates the category hierarchy

**What it creates:**
- Root categories (MODÈLES DE TESTS, SANTÉ MENTALE)
- Subcategories for each test type
- Category metadata (icons, descriptions, visibility flags)

**Key Categories:**
```
MODÈLES DE TESTS (ID: 1)
├── Test profiling (ID: 3)
├── Diagnostic (ID: 4)
├── Quiz culture générale (ID: 6)
└── Recommandation MindBridge (ID: 10)

SANTÉ MENTALE (ID: 2)
├── Cognitive (ID: 19-20)
├── Emotional (ID: 21-23)
├── Behavioral (ID: 24-26)
└── Social (ID: 27-29)
```

---

### 3. **StepsSeeder**
**File:** `mindbridge-backend/database/seeders/mindBridge/StepsSeeder.php`

**Purpose:** Creates test profiling tests and their steps

**What it creates:**
- Test profiling questions
- Steps with questions
- Options for each question

---

### 4. **ContentSeeder**
**File:** `mindbridge-backend/database/seeders/mindBridge/ContentSeeder.php`

**Purpose:** Creates educational content

**What it creates:**
- Chapters
- Content modules
- Learning materials

---

### 5. **BadgetsSeeder**
**File:** `mindbridge-backend/database/seeders/mindBridge/BadgetsSeeder.php`

**Purpose:** Creates achievement badges

**What it creates:**
- 10 achievement badges
- Badge icons (locked/unlocked)
- Required points for each badge

**Badges:**
1. Explorateur du Savoir (100 points)
2. Savant en Herbe (500 points)
3. Conquérant des Connaissances (1000 points)
4. Maître Apprenant (2000 points)
5. Innovateur de la Pensée (3000 points)
6. Penseur Critique (4000 points)
7. Visionnaire de l'Apprentissage (5000 points)
8. Architecte du Succès (6000 points)
9. Ambassadeur du Savoir (8000 points)
10. Maître de l'École (10000 points)

---

### 6. **RecommandationQuestionsSeeder**
**File:** `mindbridge-backend/database/seeders/mindBridge/RecommandationQuestionsSeeder.php`

**Purpose:** Creates recommendation system questions

**What it creates:**
- Questions for different subjects (Français, Mathématiques, Anglais, Économie)
- Questions for different difficulty levels (facile, moyen, difficile)
- True/false questions
- Multiple choice questions

---

### 7. **MindBridgeDataSyncSeeder**
**File:** `mindbridge-backend/database/seeders/MindBridgeDataSyncSeeder.php`

**Purpose:** Synchronizes data between central and MindBridge databases

**What it does:**
- Syncs users from central database
- Syncs schools and institutions
- Syncs subjects and levels
- Syncs test data

---

## 🚀 How to Run Seeders

### Run All Seeders
```bash
php artisan db:seed --database=mind_bridge
```

### Run Specific Seeder
```bash
php artisan db:seed --seeder=MentalHealthTestsSeeder --database=mind_bridge
```

### Run with Specific Type
```bash
# For tenant database (MindBridge)
php artisan db:seed --database=mind_bridge --seeder-type=tenant

# For central database
php artisan db:seed --database=central

# For data synchronization
php artisan db:seed --database=mind_bridge --seeder-type=sync
```

### Fresh Database with Seeds
```bash
php artisan migrate:fresh --database=mind_bridge --seed
```

---

## 📊 Seeder Execution Order

The `DatabaseSeeder.php` controls the execution order:

```
1. MindBridgeUsersSeeder
   ↓
2. CategoriesSeeder
   ↓
3. StepsSeeder
   ↓
4. ContentSeeder
   ↓
5. MentalHealthTestsSeeder ⭐
   ↓
6. RecommandationQuestionsSeeder
   ↓
7. BadgetsSeeder
```

---

## 🔍 MentalHealthTestsSeeder Structure

### Data Structure
```php
$mentalHealthSubCategories = [
    [
        'category_id'    => 19,
        'title'          => 'Test Name',
        'description'    => 'Test Description',
        'steps'          => [
            [
                'order'     => 1,
                'type'      => 'one',        // one, many, true_false
                'condition' => null,
                'question'  => [
                    'type'        => 'text',
                    'content'     => 'Question text',
                    'description' => 'Help text',
                    'options'     => [
                        ['name' => 'Option 1'],
                        ['name' => 'Option 2'],
                    ],
                ],
            ],
        ],
    ],
];
```

### Question Types
- **one:** Single choice (radio button)
- **many:** Multiple choice (checkboxes)
- **true_false:** True/False question

### Seeder Logic
```php
foreach ($mentalHealthSubCategories as $testData) {
    // 1. Create test
    $test = Test::create([
        'title'       => $testData['title'],
        'description' => $testData['description'],
        'category_id' => $testData['category_id'],
        'created_by'  => 1,
    ]);

    // 2. Create steps and questions
    foreach ($testData['steps'] as $stepData) {
        // Create question
        $question = Question::create($questionData);
        
        // Create options
        foreach ($options as $optionData) {
            $question->options()->create($optionData);
        }
        
        // Create step
        $test->steps()->create($newStep);
    }
}
```

---

## 📈 Sample Data Created

### Tests Created
- **12 Mental Health Tests** (3 per category)
- **36+ Questions** (3-4 per test)
- **100+ Options** (2-4 per question)

### Categories Created
- **2 Root Categories**
- **12 Subcategories**
- **4 Mental Health Categories**

### Badges Created
- **10 Achievement Badges**

### Questions Created
- **100+ Recommendation Questions**
- **Multiple difficulty levels**
- **Multiple subjects**

---

## 🔧 Customizing Seeders

### Add New Mental Health Test

Edit `MentalHealthTestsSeeder.php`:

```php
[
    'category_id'    => 19,
    'title'          => 'Your Test Title',
    'description'    => 'Your Test Description',
    'steps'          => [
        [
            'order'     => 1,
            'type'      => 'one',
            'condition' => null,
            'question'  => [
                'type'        => 'text',
                'content'     => 'Your question?',
                'description' => 'Help text',
                'options'     => [
                    ['name' => 'Option A'],
                    ['name' => 'Option B'],
                    ['name' => 'Option C'],
                ],
            ],
        ],
    ],
],
```

### Add New Category

Edit `CategoriesSeeder.php`:

```php
DB::table('categories')->insert([
    'name'                  => 'Your Category',
    'parent_id'             => 2,  // Parent category ID
    'icon'                  => '/path/to/icon.png',
    'code'                  => 'your_category_code',
    'action_type'           => 'test',
    'image_url'             => '/path/to/image.png',
    'button_text'           => 'Start Test',
    'description'           => 'Category description',
    'is_mobile'             => true,
    'is_bo'                 => true,
    'is_active'             => true,
    'position'              => 0,
    'created_at'            => now(),
    'updated_at'            => now(),
]);
```

---

## 🐛 Troubleshooting

### Seeder Not Running
```bash
# Clear cache
php artisan cache:clear

# Regenerate autoload
composer dump-autoload

# Try again
php artisan db:seed --database=mind_bridge
```

### Foreign Key Constraint Error
- Ensure categories exist before running MentalHealthTestsSeeder
- Check category IDs in seeder match database
- Run seeders in correct order

### Duplicate Data
```bash
# Fresh database
php artisan migrate:fresh --database=mind_bridge --seed

# Or truncate tables
php artisan tinker
>>> DB::table('tests')->truncate();
>>> DB::table('questions')->truncate();
>>> DB::table('options')->truncate();
```

### Check Seeded Data
```bash
# In Laravel Tinker
php artisan tinker

>>> Test::count()
>>> Category::count()
>>> Question::count()
>>> Badges::count()
```

---

## 📝 Seeder Checklist

- [ ] Database migrations run successfully
- [ ] CategoriesSeeder executed
- [ ] MentalHealthTestsSeeder executed
- [ ] BadgetsSeeder executed
- [ ] Verify tests in database
- [ ] Verify questions created
- [ ] Verify options created
- [ ] Verify badges created
- [ ] Test API endpoints
- [ ] Test mobile app

---

## 🔗 Related Files

- **DatabaseSeeder:** `mindbridge-backend/database/seeders/DatabaseSeeder.php`
- **Models:** `mindbridge-backend/app/Models/MindBridge/`
- **Migrations:** `mindbridge-backend/database/migrations/mindBridge/`

---

## 💡 Best Practices

1. **Always backup database** before running seeders
2. **Run seeders in order** - don't skip steps
3. **Verify data** after seeding
4. **Use fresh database** for clean state
5. **Document custom seeders** you create
6. **Test seeders** in development first
7. **Keep seeders updated** with schema changes

---

## 🎯 Next Steps

1. Run seeders to populate database
2. Verify data in database
3. Test API endpoints with seeded data
4. Test admin panel with seeded tests
5. Test mobile app with seeded tests
6. Create custom seeders for your needs

---

**Last Updated:** October 2025
**Status:** Production Ready

