import { Component, OnInit, inject } from '@angular/core';
import { MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';

@Component({
    selector: 'app-toaster',
    templateUrl: './toaster.component.html',
    styleUrls: ['./toaster.component.scss'],
    standalone: true,
})
export class ToasterComponent implements OnInit {
  data = inject<{
    status: string;
    message: string;
}>(MAT_SNACK_BAR_DATA);


  ngOnInit(): void {}

  closeToast() {
  }
}
