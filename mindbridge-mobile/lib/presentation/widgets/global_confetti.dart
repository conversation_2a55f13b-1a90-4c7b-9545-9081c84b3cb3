import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';

class GlobalConfetti extends StatefulWidget {
  final List<Color> colors; // Customizable colors
  final Duration duration; // Duration of the confetti
  final Alignment alignment; // Alignment of the confetti
  final double blastDirection; // Direction of the confetti blast
  final double emissionFrequency; // Frequency of particle emission
  final double gravity; // Gravity for the particles
  final int numberOfParticles; // Number of particles

  const GlobalConfetti({
    super.key,
    required this.colors,
    this.duration = const Duration(seconds: 3),
    this.alignment = Alignment.topCenter,
    this.blastDirection = 3.14 / 2,
    this.emissionFrequency = 0.07,
    this.gravity = 0.6,
    this.numberOfParticles = 20,
  });

  @override
  State<GlobalConfetti> createState() => _GlobalConfettiState();
}

class _GlobalConfettiState extends State<GlobalConfetti> {
  late ConfettiController _confettiController;

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(duration: widget.duration);
    _confettiController.play();
  }

  @override
  void dispose() {
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: widget.alignment,
      child: ConfettiWidget(
        confettiController: _confettiController,
        blastDirection: widget.blastDirection,
        emissionFrequency: widget.emissionFrequency,
        numberOfParticles: widget.numberOfParticles,
        gravity: widget.gravity,
        shouldLoop: false,
        colors: widget.colors,
      ),
    );
  }
}
