# Scoring System Implementation Guide

## Quick Reference: What Needs to Be Built

### For Your WISC-V Test Example:

```
Test: "Test Cognitif - WISC-V"
├── Section 1: Compréhension Verbale
│   ├── Q1: Image + Multiple Choice (Elephant)
│   ├── Q2: Synonym question
│   └── Q3: Word meaning
│   └── Score: 8/10 (80%)
│
├── Section 2: Raisonnement Perceptif
│   ├── Q1: Logic series
│   ├── Q2: Puzzle completion
│   └── Q3: Image series
│   └── Score: 7/10 (70%)
│
├── Section 3: Mémoire de Travail
│   ├── Q1: Number sequence recall
│   └── Q2: Shape sequence recall
│   └── Score: 6/10 (60%)
│
└── Section 4: Vitesse de Traitement
    ├── Q1: Shape matching
    └── Q2: Text reading speed
    └── Score: 9/10 (90%)

FINAL RESULTS:
├── Overall Score: 30/40 (75%)
├── Interpretation: "Capacités cognitives globalement satisfaisantes"
└── Recommendation: "Proposer des exercices supplémentaires"
```

---

## Database Implementation

### 1. Create TestSection Model
```php
// app/Models/MindBridge/TestSection.php
class TestSection extends Model {
    protected $connection = 'mind_bridge';
    protected $table = 'test_sections';
    
    protected $fillable = [
        'test_id', 'title', 'description', 'order', 'max_score'
    ];
    
    public function test() {
        return $this->belongsTo(Test::class);
    }
    
    public function steps() {
        return $this->hasMany(Step::class, 'section_id');
    }
    
    public function scoringRules() {
        return $this->hasMany(TestScoringRule::class, 'section_id');
    }
}
```

### 2. Create TestScoringRule Model
```php
// app/Models/MindBridge/TestScoringRule.php
class TestScoringRule extends Model {
    protected $connection = 'mind_bridge';
    protected $table = 'test_scoring_rules';
    
    protected $fillable = [
        'test_id', 'section_id', 'min_score', 'max_score',
        'interpretation', 'feedback', 'recommendation'
    ];
    
    public function test() {
        return $this->belongsTo(Test::class);
    }
    
    public function section() {
        return $this->belongsTo(TestSection::class, 'section_id');
    }
}
```

### 3. Update Test Model
```php
// Add to Test.php
public function sections() {
    return $this->hasMany(TestSection::class);
}

public function scoringRules() {
    return $this->hasMany(TestScoringRule::class);
}
```

### 4. Update Step Model
```php
// Add to Step.php
protected $fillable = [
    'order', 'required', 'condition', 'type', 
    'test_id', 'question_id', 'section_id', 'points_value'
];

public function section() {
    return $this->belongsTo(TestSection::class, 'section_id');
}
```

---

## Scoring Service Implementation

### Create ScoringService
```php
// app/Services/ScoringService.php
class ScoringService {
    
    public function calculateTestScore($test, $answers) {
        $sectionScores = [];
        $totalScore = 0;
        $totalMaxScore = 0;
        
        foreach ($test->sections as $section) {
            $sectionScore = $this->calculateSectionScore($section, $answers);
            $sectionScores[$section->id] = $sectionScore;
            $totalScore += $sectionScore['score'];
            $totalMaxScore += $sectionScore['max_score'];
        }
        
        return [
            'total_score' => $totalScore,
            'total_max_score' => $totalMaxScore,
            'percentage' => ($totalMaxScore > 0) ? 
                round(($totalScore / $totalMaxScore) * 100, 2) : 0,
            'section_scores' => $sectionScores,
            'feedback' => $this->generateFeedback($test, $sectionScores),
            'recommendations' => $this->generateRecommendations($test, $sectionScores)
        ];
    }
    
    private function calculateSectionScore($section, $answers) {
        $score = 0;
        $maxScore = 0;
        
        foreach ($section->steps as $step) {
            $maxScore += $step->points_value ?? 1;
            
            if (isset($answers[$step->question_id])) {
                if ($this->isAnswerCorrect($step, $answers[$step->question_id])) {
                    $score += $step->points_value ?? 1;
                }
            }
        }
        
        return [
            'score' => $score,
            'max_score' => $maxScore,
            'percentage' => ($maxScore > 0) ? 
                round(($score / $maxScore) * 100, 2) : 0
        ];
    }
    
    private function isAnswerCorrect($step, $selectedOptions) {
        $correctOptions = Option::where('question_id', $step->question_id)
            ->where('isCorrect', true)
            ->pluck('id')
            ->toArray();
        
        $selected = is_array($selectedOptions) ? 
            $selectedOptions : [$selectedOptions];
        
        return count(array_intersect($selected, $correctOptions)) === 
               count($correctOptions);
    }
    
    private function generateFeedback($test, $sectionScores) {
        $feedback = [];
        
        foreach ($test->sections as $section) {
            $score = $sectionScores[$section->id];
            $rule = $this->findApplicableRule($section, $score['percentage']);
            
            if ($rule) {
                $feedback[$section->id] = $rule->feedback;
            }
        }
        
        return $feedback;
    }
    
    private function generateRecommendations($test, $sectionScores) {
        $recommendations = [];
        
        foreach ($test->sections as $section) {
            $score = $sectionScores[$section->id];
            $rule = $this->findApplicableRule($section, $score['percentage']);
            
            if ($rule && $rule->recommendation) {
                $recommendations[$section->id] = $rule->recommendation;
            }
        }
        
        return $recommendations;
    }
    
    private function findApplicableRule($section, $percentage) {
        return TestScoringRule::where('section_id', $section->id)
            ->where('min_score', '<=', $percentage)
            ->where('max_score', '>=', $percentage)
            ->first();
    }
}
```

---

## API Endpoint Update

### Fix submitTestAnswers in MindBridgeEtudiantController
```php
public function submitTestAnswers(Request $request) {
    // ... existing validation ...
    
    $scoringService = new ScoringService();
    $testResults = $scoringService->calculateTestScore($test, $request->data);
    
    $testStatus = EtudiantTestStatus::updateOrCreate(
        ['etudiant_id' => $etudiant->id, 'test_id' => $test->id],
        [
            'status' => 'TERMINE',
            'score' => $testResults['total_score'],
            'section_scores' => json_encode($testResults['section_scores']),
            'feedback' => json_encode($testResults['feedback']),
            'recommendations' => json_encode($testResults['recommendations']),
            'completed_at' => now(),
        ]
    );
    
    return response()->json([
        'message' => 'Test completed successfully',
        'results' => $testResults
    ]);
}
```

---

## Admin Panel Updates Needed

1. **Test Creation**: Add section management
2. **Question Assignment**: Assign questions to sections
3. **Scoring Rules**: Configure thresholds and feedback
4. **Results View**: Display section-by-section results

---

## Mobile App Updates Needed

1. **Test Display**: Show section titles and descriptions
2. **Results Screen**: Display section scores separately
3. **Feedback**: Show personalized feedback per section
4. **Recommendations**: Display professional consultation suggestions


