import 'dart:io';

class ApiRoutes {
  // static const String mainBaseUrl = 'https://api.mindbridge.awlyg.tech';

  // Use ******** for Android emulator, localhost for iOS simulator and other platforms
  static String get mainBaseUrl {
    if (Platform.isAndroid) {
      return 'http://********:7562';
    }
    return 'http://localhost:7562';
  }

  static String get apiBaseUrl => '$mainBaseUrl/api';

  // Auth Routes
  static String get login => '$apiBaseUrl/mind_bridge/auth/etudiant/login';

  // Test Profiling Routes
  static String get testProfiling => '$apiBaseUrl/mind_bridge/etudiant/tests';

  // Home Routes
  static String get etudiantInfo => '$apiBaseUrl/mind_bridge/etudiant/infos';
  static String get categoryInfo =>
      '$apiBaseUrl/mind_bridge/etudiant/categories/board';

  // Matiers Routes
  static String get matiers => '$apiBaseUrl/mind_bridge/etudiant/matiers';

  // Lesson Routes
  static String get lessons => '$apiBaseUrl/mind_bridge/etudiant/contents';

  // Evaluation Routes
  static String get evaluation =>
      '$apiBaseUrl/mind_bridge/etudiant/contents/test';
  static String get evaluationSubmit =>
      '$apiBaseUrl/mind_bridge/etudiant/tests';

  static String get challengeHebdomadaire =>
      '$apiBaseUrl/mind_bridge/etudiant/tests/hebdomadaire';

  static String get recommandation =>
      '$apiBaseUrl/mind_bridge/etudiant/tests/recommandation';

  static String get submitRecommandationResult =>
      '$apiBaseUrl/mind_bridge/etudiant/tests/recommandation';
}
