<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Model;

class EtudiantTestAssignment extends Model
{
    protected $connection = 'mind_bridge';
    protected $table = 'etudiant_test_assignments';
    protected $fillable = [
        'etudiant_id',
        'test_id',
        'status',
        'assigned_at',
        'completed_at'
    ];

    public function etudiant()
    {
        return $this->belongsTo(MindBridgeEtudiant::class, 'etudiant_id');
    }

    public function test()
    {
        return $this->belongsTo(Test::class, 'test_id');
    }

}
