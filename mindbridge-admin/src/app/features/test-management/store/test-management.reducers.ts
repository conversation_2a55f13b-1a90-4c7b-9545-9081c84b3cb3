import { Action, createReducer, on } from "@ngrx/store";
import * as ListsTestsManagement from "../store/test-management.actions";

export interface TestManagementState {
    testsManagement: any,
    testManagement: any
}
export const initialTestManagementState: TestManagementState = {
    testsManagement: null,
    testManagement: null
}

const featureReducer = createReducer(
    initialTestManagementState,
    on(
        ListsTestsManagement.AllTestsManagement,
        ListsTestsManagement.AddTestManagement,
        (state, action) => ({ ...state, ...action })
    )
)

export function testManagementReducer(state: TestManagementState | undefined, action: Action) {
    return featureReducer(state, action);
}