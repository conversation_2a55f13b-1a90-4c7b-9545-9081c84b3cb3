import {
  Component,
  computed,
  effect,
  inject,
  OnInit,
  Signal,
  untracked,
} from '@angular/core';
import { NgStyle } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient } from '@angular/common/http';

import { SetLoading } from '../../../shared/store/shared.actions';
import { EtudiantsStore } from '../../etudiants.store';
import { Router } from '@angular/router';
import {
  EtudiantSearchFormGroup,
  IEtudiantSearchForm,
  IEtudiantSearchFormGroup,
} from '../../models/etudiant-search-request';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../core/app.state';
import { NiveauMini } from '../../../matieres/models/matiere-response.model';
import { EtudiantResponse } from '../../models/etudiant-response';
import { MatieresStore } from '../../../matieres/matieres.store';
import { SchoolResponse } from '../../models/school-response';
import { OPTION_ALL } from '../../../shared/global.config';
import { MentalHealthTriggerModalComponent } from '../mental-health-trigger-modal/mental-health-trigger-modal.component';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-liste-etudiants',
  imports: [NgStyle, FormsModule, ReactiveFormsModule],
  templateUrl: './liste-etudiants.component.html',
  styleUrl: './liste-etudiants.component.scss',
  standalone: true,
  providers: [EtudiantsStore, MatieresStore],
})
export class ListeEtudiantsComponent implements OnInit {
  private router = inject(Router);
  private store = inject<Store<AppState>>(Store);
  private fb = inject(FormBuilder);
  private dialog = inject(MatDialog);
  private http = inject(HttpClient);

  form: FormGroup<IEtudiantSearchFormGroup> = this.fb.group(
    new EtudiantSearchFormGroup()
  );

  #etudiantsStore = inject(EtudiantsStore);
  #matieresStore = inject(MatieresStore);

  allEtudiants: Signal<EtudiantResponse[]> =
    this.#etudiantsStore.state.etudiants;
  etudiantsSearchForm: Signal<IEtudiantSearchForm> =
    this.#etudiantsStore.state.etudiantsSearchForm;
  allSchools: Signal<SchoolResponse[]> = this.#etudiantsStore.state.schools;
  allNiveaux: Signal<NiveauMini[]> = this.#matieresStore.state.niveaux;

  current_page: Signal<number> = this.#etudiantsStore.state.current_page;
  total_pages: Signal<number> = computed(() =>
    Math.max(this.#etudiantsStore.state.total_pages(), 1)
  );

  constructor() {
    effect(() => {
      const searchFormValue = this.etudiantsSearchForm();
      untracked(() => this.form.patchValue(searchFormValue));
    });
  }

  ngOnInit() {
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.searchEtudiants();
    this.#matieresStore.getAllNiveaux();
    this.#etudiantsStore.getAllSchools();
  }

  searchEtudiants() {
    this.#etudiantsStore.setSearchForm(this.form.getRawValue());
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.#etudiantsStore.getEtudiants();
  }

  goToPage(page: number) {
    this.#etudiantsStore.setCurrentPage(page);
    this.searchEtudiants();
  }

  getEtudiant(id: number) {
    this.router.navigate(['/etudiant', id]);
  }

  openMentalHealthTrigger(etudiant: EtudiantResponse) {
    const dialogRef = this.dialog.open(MentalHealthTriggerModalComponent, {
      width: '600px',
      data: { etudiantId: etudiant.id },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleTriggerResult(result, etudiant);
      }
    });
  }

  private handleTriggerResult(result: any, etudiant: EtudiantResponse) {
    const { triggerType, observation } = result;

    if (triggerType === 'parent' || triggerType === 'teacher') {
      // Call runMentalHealthTestBySubCategory with observation's test_id
      if (observation && observation.test_id) {
        this.runMentalHealthTest(observation.test_id, etudiant.id);
      }
    } else if (triggerType === 'simulated_grade_shutdown') {
      // Handle simulated grade shutdown trigger
      console.log('Simulated grade shutdown triggered for student:', etudiant.id);
      // You can add additional logic here for simulated grade shutdown
    }
  }

  private runMentalHealthTest(subcategoryId: number, etudiantId: number) {
    this.store.dispatch(SetLoading({ isAppLoading: true }));

    const payload = {
      subcategory_id: subcategoryId,
      etudiant_id: etudiantId,
    };

    this.http
      .post(`${environment.BASE_URL_API}mind_bridge/run_test`, payload)
      .subscribe({
        next: (response: any) => {
          console.log('Mental health test started:', response);
          this.store.dispatch(SetLoading({ isAppLoading: false }));
          alert('Test de santé mentale lancé avec succès');
        },
        error: (error) => {
          console.error('Error starting mental health test:', error);
          this.store.dispatch(SetLoading({ isAppLoading: false }));
          alert('Erreur lors du lancement du test');
        },
      });
  }

  protected readonly OPTION_ALL = OPTION_ALL;
  protected readonly Number = Number;
}
