<?php




namespace App\Service;

use App\Jobs\ResetPasswordAndPreparNotif;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Google\Client as GoogleClient;
use Google\Exception as GoogleException;
use Google\Service\FirebaseCloudMessaging;

/**
 * @deprecated use Notifications or Jobs
 */
class TemplateNotificationService
{

    public static function configureClient()
    {
        $client = new GoogleClient();
        try {
            $serviceAccountKeyFilePath = config_path(env('GOOGLE_APPLICATION_CREDENTIALS', ''));

            if (!file_exists($serviceAccountKeyFilePath)) {
                throw new \Exception('Service account key file not found: ' . $serviceAccountKeyFilePath);
            }

            $client->setAuthConfig($serviceAccountKeyFilePath);
            $client->addScope(FirebaseCloudMessaging::CLOUD_PLATFORM);

            // Retrieve the saved OAuth token if it exists
            $savedTokenJson = self::readFile();

            if ($savedTokenJson != null) {
                $client->setAccessToken($savedTokenJson);
                if ($client->isAccessTokenExpired()) {
                    $accessToken = self::generateToken($client);
                    $client->setAccessToken($accessToken);
                }
            } else {
                $accessToken = self::generateToken($client);
                $client->setAccessToken($accessToken);
            }

            $oauthToken = $client->getAccessToken()["access_token"];
            return $oauthToken;
        } catch (GoogleException $e) {
            \Log::error('Google Exception: ' . $e);
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Exception: ' . $e);
            throw $e;
        }
    }

    private static function generateToken(GoogleClient $client)
    {
        $client->fetchAccessTokenWithAssertion();
        $accessToken = $client->getAccessToken();

        // Save the OAuth token JSON
        $tokenJson = json_encode($accessToken);
        self::saveFile($tokenJson);

        return $accessToken;
    }

    private static function saveFile($data)
    {
        file_put_contents(config_path('oauth_token.json'), $data);
    }

    private static function readFile()
    {
        $path = config_path('oauth_token.json');
        if (file_exists($path)) {
            return file_get_contents($path);
        }
        return null;
    }

    static function sendNotification($token, $message, $title, $payload = null)
    {
        try {
            $accessToken = self::configureClient();

            $data = [
                "message" => [
                    "token" => $token,
                    "notification" => [
                        "title" => $title,
                        "body" => $message,
                    ],
                    "data" => $payload ?? [],
                    "android" => [
                        "priority" => "high",
                        "notification" => [
                            "sound" => "default",
                        ],
                    ],
                    "apns" => [
                        "headers" => [
                            "apns-priority" => "10"
                        ],
                        "payload" => [
                            "aps" => [
                                "alert" => [
                                    "title" => $title,
                                    "body" => $message
                                ],
                                "badge" => 1,
                                "sound" => "default",
                            ]
                        ]
                    ],
                    "webpush" => [
                        "headers" => [
                            "Urgency" => "high"
                        ]
                    ],
                ],
            ];

            $url = "https://fcm.googleapis.com/v1/projects/mindbridge-728f4/messages:send";

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->withOptions([
                        'verify' => false
                    ])->post($url, $data);

            if ($response->failed()) {
                \Log::error('Failed to send notification. Response: ' . $response->body());
                throw new \Exception('Failed to send notification. Response: ' . $response->body());
            }

            \Log::info('Notification sent successfully. Response: ' . $response->body());

        } catch (\Exception $e) {
            \Log::error('Error in sendNotification: ' . $e);
        }
    }


    static function sendWhatsAppNotif($templateType, $data)
    {
        $notificationServiceUrl = env('SERVICE_NOTIF_URL');
        $attempt = 0;
        $sentStatus = false;

        $endpoint = match ($templateType) {
            'account_creation' => 'new-account-access',
            'absence' => 'absence-notification',
            'otp' => 'reset-password-otp',
            'account_creation_prof' => 'new-account-access-prof',
            "payment_reminder" => "payment-reminder",
            default => throw new \Exception("Invalid template type"),
        };

        $url = $notificationServiceUrl . "/notification-whatsapp/" . $endpoint;
        \Log::info($url);
        \Log::info($data);

        while ($attempt < 2) {
            try {
                Log::info("Attempt #" . ($attempt + 1));

                $response = Http::timeout(10)->post($url, $data);

                Log::info("Response status: " . $response->status());
                Log::info("Response body: " . $response->body());

                $responseData = json_decode($response->body(), true);
                $sentStatus = $responseData['sent'] ?? false;

                if ($response->successful() && $sentStatus) {
                    break;
                }
            } catch (\Exception $e) {
                Log::error("Exception occurred on attempt #" . ($attempt + 1) . ": " . $e->getMessage());
            }

            $attempt++;
        }

        return [
            'status' => $sentStatus,
        ];
    }

    static function sendAccountCreationNotification($user, $password, $phone , $type = null)
    {

        $data = [
            'phoneAccess' => $user->phone,
            'password' => $password,
            'userPhone' => $phone,
        ];

        TemplateNotificationService::sendWhatsAppNotif($type, $data);
    }

    public function resetPasswordsAndPrepareNotifications()
    {
        \Log::info("resetPasswordsAndPrepareNotifications");
        ResetPasswordAndPreparNotif::dispatch();
        \Log::info("resetPasswordsAndPrepareNotifications dispatched");
        return response()->json(['message' => 'The job has been dispatched.']);
    }


}
