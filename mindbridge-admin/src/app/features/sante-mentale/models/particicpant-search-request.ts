import { FormControl } from '@angular/forms';
import { OPTION_ALL } from '../../shared/global.config';

export interface IParticipantSearchForm {
  category_id: number;
  niveau: number;
  etudiant: string;
}

export class ParticipantSearchForm implements IParticipantSearchForm {
  category_id: number;
  niveau: number;
  etudiant: string;
  constructor() {
    this.category_id = OPTION_ALL;
    this.niveau = OPTION_ALL;
    this.etudiant = '';
  }
}

export interface IParticipantSearchFormGroup {
  category_id: FormControl<number>;
  niveau: FormControl<number>;
  etudiant: FormControl<string>;
}

export class ParticipantSearchFormGroup implements IParticipantSearchFormGroup {
  category_id: FormControl<number>;
  niveau: FormControl<number>;
  etudiant: FormControl<string>;

  constructor(
    participantSearchForm: IParticipantSearchForm = new ParticipantSearchForm()
  ) {
    this.category_id = new FormControl<number>(
      participantSearchForm.category_id
    );
    this.niveau = new FormControl<number>(participantSearchForm.niveau);
    this.etudiant = new FormControl<string>(participantSearchForm.etudiant);
  }
}
