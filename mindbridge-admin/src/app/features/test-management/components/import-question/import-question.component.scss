.content_import {
    width: 100%;
    height: 670px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .head_import {
        width: 100%;
        padding: 0 20px;
        display: flex;
        align-items: center;

        .icon_import {
            width: 40px;
            height: 40px;
        }

        .close_import {
            all:unset;
            width: 40px;
            height: 40px;
            font-size: 17px;
            font-family: sans-serif;
            color: #98A2B3;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            img {
                width: 12px;
                height: 12px;
            }
        }
    }

    .input_import {
        width: 100%;
        height: 45px;
        padding: 0 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .search_import {
            height: 40px;
            position: relative;

            input {
                width: 225px;
                height: 100%;
                border-radius: 7px;
                border: none;
                padding: 0 10px;
                font-size: 15px;
                font-family: sans-serif;
                color: #98A2B3;
                border: 1px solid #D0D5DD;
            }

            img {
                position: absolute;
                top: 50%;
                right: 10px;
                transform: translateY(-50%);
            }
        }
    }

    .body_import {
        width: 100%;
        height: 460px;
        overflow-y: auto;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .questions_answers {
            width: 100%;
            display: flex;
            flex-direction: column;
            border: 1px solid #6BA4BD;
            border-radius: 8px;
            padding: 20px 15px 10px 15px;
            position: relative;

            h3 {
                color: #344054;
                font-size: 16px;
                font-weight: 700;
            }

            .answers {
                width: 100%;
                display: flex;
                align-items: center;
            }

            .check_question {
                position: absolute;
                top: 0;
                right: 0;
                width: 40px;
                height: 40px;
            }
        }
    }

    .actions_import {
        width: 100%;
        height: 80px;
        padding: 0 20px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;

        .cancel_import {
            all: unset;
            width: 120px;
            height: 40px;
            border-radius: 8px;
            border: 1px solid #98A2B3;
            color: #98A2B3;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 15px;
            font-family: sans-serif;
            cursor: pointer;
        }

        .save_import {
            all: unset;
            width: 120px;
            height: 40px;
            border-radius: 8px;
            background-color: #6BA4BD;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 15px;
            font-family: sans-serif;
            cursor: pointer;
        }
    }
}
