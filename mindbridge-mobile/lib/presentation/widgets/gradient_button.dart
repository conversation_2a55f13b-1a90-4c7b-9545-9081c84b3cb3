import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../app/theme/colors.dart';

class GradientButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;

  const GradientButton({
    super.key,
    required this.text,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final bool isEnabled = onPressed != null;

    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.symmetric(vertical: 0.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(1.5.h),
        ),
        backgroundColor: isEnabled ? Colors.transparent : Colors.grey.shade300,
        shadowColor: Colors.transparent,
      ),
      child: Ink(
        decoration: isEnabled
            ? BoxDecoration(
                gradient: AppColors.gradient,
                borderRadius: BorderRadius.circular(1.5.h),
              )
            : BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(1.5.h),
              ),
        child: Container(
          alignment: Alignment.center,
          height: 6.h,
          child: Text(
            text,
            style: TextStyle(
              color: isEnabled ? AppColors.white : Colors.grey.shade600,
              fontWeight: FontWeight.bold,
              fontSize: 12.sp,
            ),
          ),
        ),
      ),
    );
  }
}
