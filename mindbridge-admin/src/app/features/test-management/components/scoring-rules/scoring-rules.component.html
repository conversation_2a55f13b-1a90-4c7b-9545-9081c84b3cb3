<div class="scoring-rules-container">
  <div class="scoring-rules-header">
    <h3>Règles de Notation</h3>
    <button
      type="button"
      class="btn btn-primary"
      (click)="openForm()"
      [disabled]="showForm"
    >
      <mat-icon>add</mat-icon>
      Ajouter une Règle
    </button>
  </div>

  <!-- Form Section -->
  @if (showForm) {
    <div class="scoring-rule-form">
      <h4>{{ editingRuleId ? 'Modifier' : 'Ajouter' }} une Règle de Notation</h4>
      <form [formGroup]="scoringRuleForm" class="form-grid">
        <mat-form-field class="full-width">
          <mat-label>Pourcentage Minimum (%)</mat-label>
          <input
            matInput
            type="number"
            formControlName="min_percentage"
            min="0"
            max="100"
          />
          @if (scoringRuleForm.get('min_percentage')?.hasError('required')) {
            <mat-error>Ce champ est requis</mat-error>
          }
          @if (scoringRuleForm.get('min_percentage')?.hasError('min')) {
            <mat-error>La valeur doit être >= 0</mat-error>
          }
          @if (scoringRuleForm.get('min_percentage')?.hasError('max')) {
            <mat-error>La valeur doit être <= 100</mat-error>
          }
        </mat-form-field>

        <mat-form-field class="full-width">
          <mat-label>Pourcentage Maximum (%)</mat-label>
          <input
            matInput
            type="number"
            formControlName="max_percentage"
            min="0"
            max="100"
          />
          @if (scoringRuleForm.get('max_percentage')?.hasError('required')) {
            <mat-error>Ce champ est requis</mat-error>
          }
          @if (scoringRuleForm.get('max_percentage')?.hasError('min')) {
            <mat-error>La valeur doit être >= 0</mat-error>
          }
          @if (scoringRuleForm.get('max_percentage')?.hasError('max')) {
            <mat-error>La valeur doit être <= 100</mat-error>
          }
        </mat-form-field>

        <mat-form-field class="full-width">
          <mat-label>Interprétation</mat-label>
          <input
            matInput
            type="text"
            formControlName="interpretation"
            placeholder="Ex: Excellent, Bon, Moyen, Faible"
          />
          @if (scoringRuleForm.get('interpretation')?.hasError('required')) {
            <mat-error>Ce champ est requis</mat-error>
          }
        </mat-form-field>

        <mat-form-field class="full-width">
          <mat-label>Feedback (Optionnel)</mat-label>
          <textarea
            matInput
            formControlName="feedback"
            rows="3"
            placeholder="Message de feedback pour l'étudiant"
          ></textarea>
        </mat-form-field>

        <mat-form-field class="full-width">
          <mat-label>Recommandation (Optionnel)</mat-label>
          <textarea
            matInput
            formControlName="recommendation"
            rows="3"
            placeholder="Recommandation professionnelle"
          ></textarea>
        </mat-form-field>

        <div class="form-actions">
          <button
            type="button"
            class="btn btn-secondary"
            (click)="closeForm()"
          >
            Annuler
          </button>
          <button
            type="button"
            class="btn btn-primary"
            (click)="saveRule()"
            [disabled]="scoringRuleForm.invalid"
          >
            {{ editingRuleId ? 'Mettre à jour' : 'Créer' }}
          </button>
        </div>
      </form>
    </div>
  }

  <!-- Rules Table -->
  <div class="scoring-rules-table">
    @if (isLoading) {
      <p class="loading">Chargement...</p>
    } @else if (scoringRules.length === 0) {
      <p class="no-data">Aucune règle de notation définie</p>
    } @else {
      <table mat-table [dataSource]="scoringRules" class="rules-table">
        <!-- Min Percentage Column -->
        <ng-container matColumnDef="min_percentage">
          <th mat-header-cell *matHeaderCellDef>Min %</th>
          <td mat-cell *matCellDef="let element">{{ element.min_percentage }}%</td>
        </ng-container>

        <!-- Max Percentage Column -->
        <ng-container matColumnDef="max_percentage">
          <th mat-header-cell *matHeaderCellDef>Max %</th>
          <td mat-cell *matCellDef="let element">{{ element.max_percentage }}%</td>
        </ng-container>

        <!-- Interpretation Column -->
        <ng-container matColumnDef="interpretation">
          <th mat-header-cell *matHeaderCellDef>Interprétation</th>
          <td mat-cell *matCellDef="let element">{{ element.interpretation }}</td>
        </ng-container>

        <!-- Feedback Column -->
        <ng-container matColumnDef="feedback">
          <th mat-header-cell *matHeaderCellDef>Feedback</th>
          <td mat-cell *matCellDef="let element">
            {{ element.feedback ? (element.feedback | slice: 0:50) + '...' : '-' }}
          </td>
        </ng-container>

        <!-- Recommendation Column -->
        <ng-container matColumnDef="recommendation">
          <th mat-header-cell *matHeaderCellDef>Recommandation</th>
          <td mat-cell *matCellDef="let element">
            {{ element.recommendation ? (element.recommendation | slice: 0:50) + '...' : '-' }}
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let element">
            <button
              type="button"
              class="btn-icon btn-edit"
              (click)="openForm(element)"
              title="Modifier"
            >
              <mat-icon>edit</mat-icon>
            </button>
            <button
              type="button"
              class="btn-icon btn-delete"
              (click)="deleteRule(element.id)"
              title="Supprimer"
            >
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    }
  </div>
</div>

