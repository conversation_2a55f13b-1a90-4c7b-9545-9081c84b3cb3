import { Component, OnInit, inject, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BaseChartDirective } from 'ng2-charts';
import { ChartConfiguration, ChartData, ChartType } from 'chart.js';

import { SanteMentaleStore } from '../../sante-mentale.store';
import { 
  AtRiskStudent, 
  DashboardFilters, 
  DashboardFiltersForm,
  RISK_LEVEL_COLORS,
  RISK_LEVEL_LABELS,
  CHART_COLORS
} from '../../models/mental-health-risk-dashboard';

@Component({
  selector: 'app-mental-health-risk-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    BaseChartDirective
  ],
  templateUrl: './mental-health-risk-dashboard.component.html',
  styleUrl: './mental-health-risk-dashboard.component.scss'
})
export class MentalHealthRiskDashboardComponent implements OnInit {
  private store = inject(SanteMentaleStore);

  // State signals
  dashboardData = this.store.state.dashboardData;
  dashboardLoading = this.store.state.dashboardLoading;
  dashboardError = this.store.state.dashboardError;
  dashboardFilters = this.store.state.dashboardFilters;

  // Local state
  selectedRiskFilter = signal<'high' | 'medium' | 'low' | undefined>(undefined);
  
  // Computed values
  summary = computed(() => this.dashboardData()?.summary);
  students = computed(() => {
    const allStudents = this.dashboardData()?.students || [];
    const riskFilter = this.selectedRiskFilter();
    if (!riskFilter) return allStudents;
    return allStudents.filter(student => student.risk_level === riskFilter);
  });
  charts = computed(() => this.dashboardData()?.charts);

  // Constants
  readonly RISK_LEVEL_COLORS = RISK_LEVEL_COLORS;
  readonly RISK_LEVEL_LABELS = RISK_LEVEL_LABELS;
  readonly CHART_COLORS = CHART_COLORS;

  // Table configuration
  displayedColumns: string[] = [
    'student_name',
    'niveau',
    'test_count',
    'average_score',
    'lowest_score',
    'risk_level',
    'last_test_date',
    'actions'
  ];

  // Chart configurations
  chartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  lineChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        position: 'left',
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        beginAtZero: true,
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  pieChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'right',
      },
    },
  };

  ngOnInit() {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.store.getDashboardData();
  }

  onFiltersChange(filters: DashboardFilters) {
    this.store.setDashboardFilters(filters);
    this.store.getDashboardData(filters);
  }

  onRiskFilterChange(riskLevel: 'high' | 'medium' | 'low' | undefined) {
    this.selectedRiskFilter.set(riskLevel);
  }

  refreshData() {
    this.store.getDashboardData();
  }

  getRiskLevelClass(riskLevel: string): string {
    switch (riskLevel) {
      case 'high':
        return 'risk-high';
      case 'medium':
        return 'risk-medium';
      case 'low':
        return 'risk-low';
      default:
        return '';
    }
  }

  getRiskLevelColor(riskLevel: string): string {
    return RISK_LEVEL_COLORS[riskLevel as keyof typeof RISK_LEVEL_COLORS] || '#666';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR');
  }

  // Chart data computed properties
  studentsByNiveauChartData = computed((): ChartData<'bar'> => {
    const chartData = this.charts()?.students_by_niveau || [];
    return {
      labels: chartData.map(item => item.niveau),
      datasets: [
        {
          label: 'Risque Élevé',
          data: chartData.map(item => item.high_risk),
          backgroundColor: RISK_LEVEL_COLORS.high,
        },
        {
          label: 'Risque Moyen',
          data: chartData.map(item => item.medium_risk),
          backgroundColor: RISK_LEVEL_COLORS.medium,
        },
        {
          label: 'Risque Faible',
          data: chartData.map(item => item.low_risk),
          backgroundColor: RISK_LEVEL_COLORS.low,
        },
      ],
    };
  });

  testsByCategoryChartData = computed((): ChartData<'bar'> => {
    const chartData = this.charts()?.tests_by_category || [];
    return {
      labels: chartData.map(item => item.category),
      datasets: [
        {
          label: 'Nombre de Tests',
          data: chartData.map(item => item.count),
          backgroundColor: CHART_COLORS.primary,
        },
      ],
    };
  });

  triggerDistributionChartData = computed((): ChartData<'pie'> => {
    const chartData = this.charts()?.trigger_distribution || [];
    return {
      labels: chartData.map(item => item.trigger_type),
      datasets: [
        {
          data: chartData.map(item => item.count),
          backgroundColor: [
            CHART_COLORS.primary,
            CHART_COLORS.secondary,
            CHART_COLORS.warning,
          ],
        },
      ],
    };
  });

  scoreTrendsChartData = computed((): ChartData<'line'> => {
    const chartData = this.charts()?.score_trends || [];
    return {
      labels: chartData.map(item => item.month),
      datasets: [
        {
          label: 'Score Moyen',
          data: chartData.map(item => item.average_score),
          borderColor: CHART_COLORS.primary,
          backgroundColor: 'transparent',
          tension: 0.4,
        },
        {
          label: 'Nombre de Tests',
          data: chartData.map(item => item.test_count),
          borderColor: CHART_COLORS.secondary,
          backgroundColor: 'transparent',
          tension: 0.4,
          yAxisID: 'y1',
        },
      ],
    };
  });

  // Chart types
  barChartType: ChartType = 'bar';
  pieChartType: ChartType = 'pie';
  lineChartType: ChartType = 'line';
}
