import { Pipe, PipeTransform } from '@angular/core';
import { TestCategoryEnum } from '../../test-management/models/test-category-enum';

@Pipe({
  name: 'testCategoryName',
})
export class TestCategoryNamePipe implements PipeTransform {
  transform(testCategory: TestCategoryEnum): string {
    switch (testCategory) {
      case TestCategoryEnum.TestProfiling:
        return 'Test profiling';
      case TestCategoryEnum.TestContenu:
        return 'Test de contenu';
      case TestCategoryEnum.ExamenSimule:
        return 'Examen simulé';
      case TestCategoryEnum.QuizCultureGenerale:
        return 'Quiz culture générale';
      case TestCategoryEnum.Cognitifs:
        return 'Cognitifs';
      case TestCategoryEnum.NeuroDeveloppement:
        return 'Neuro Développement';
      case TestCategoryEnum.Apprentissage:
        return 'Apprentissage';
      case TestCategoryEnum.Personnalite:
        return 'Personnalité';
      case TestCategoryEnum.EmotionnelEtComportemental:
        return 'Émotionnel et comportemental';
      case TestCategoryEnum.MotivationEtEstimeDeSoi:
        return 'Motivation et estime de soi';
      case TestCategoryEnum.AttentionExecutif:
        return 'Attention/exécutif';
      case TestCategoryEnum.RelationsFamilialesSociales:
        return 'Relations familiales/sociales';
      case TestCategoryEnum.ChallengeHebdomadaire:
        return 'Challenge Hebdomadaire';
      case TestCategoryEnum.Sondage:
        return 'Sondage test';
      default:
        return 'Unknown category';
    }
  }
}
