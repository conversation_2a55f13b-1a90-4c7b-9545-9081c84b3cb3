<?php

namespace App\Models\MindBridge;

use App\Models\MatiereCentrale;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MindeBridgeStudentNotes extends Model
{
    use HasFactory;
    
    protected $connection = 'mind_bridge';
    protected $table = 'etudiant_notes';

    protected $fillable = [
        'etudiant_id',
        'enseignant_id',
        'matiere_id',
        'trimestre',
        'moyenne',
        'notes',
        'appreciation',
    ];
    
    protected $casts = [
        'notes' => 'array',
    ];

    public function etudiant()
    {
        return $this->belongsTo(MindBridgeEtudiant::class);
    }

    public function matiere()
    {
        return $this->belongsTo(MatiereCentrale::class);
    }
}
