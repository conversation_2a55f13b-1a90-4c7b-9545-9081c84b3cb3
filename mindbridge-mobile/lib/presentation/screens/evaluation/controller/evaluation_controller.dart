import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/app/routes.dart';
import 'package:mindbridge/core/models/test_action_type.dart';
import 'package:mindbridge/core/utilities/app_images.dart';

import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/evaluation.dart' as eval;
import '../../../../core/utilities/loading_utils.dart';
import '../../../../core/utilities/validators.dart';
import '../../../../data/repositories/evaluation_repository.dart';
import '../../../widgets/global_confetti.dart';
import '../../../widgets/global_dialog.dart';
import '../../../widgets/global_loader.dart';

class EvaluationController extends GetxController {
  final EvaluationRepository _evaluationRepository;

  EvaluationController({required EvaluationRepository evaluationRepository})
      : _evaluationRepository = evaluationRepository;

  RxBool isLoading = false.obs;
  RxBool isError = false.obs;
  RxBool isEmptyData = false.obs;
  int totalHebdomadaireCount = 0;
  int totalHebdomadaireResponseCount = 0;
  RxString testType = "".obs;

  Rx<eval.Evaluation?> evaluationData = Rx<eval.Evaluation?>(null);
  RxList<eval.Evaluation> challengeHebdomadaires = <eval.Evaluation>[].obs;

  
  void goToNextChallengeHebdomadaire() {
    
    if (challengeHebdomadaires.isEmpty) {
      evaluationData.value = null;
      return;
    }

    if (evaluationData.value == null) {
      print("No current challenge selected");
      evaluationData.value = null;
      return;
    }

    final currentIndex = challengeHebdomadaires.indexOf(evaluationData.value!);

    if (currentIndex == -1) {
      evaluationData.value = null;
      return;
    }

    totalHebdomadaireCount += evaluationData.value?.steps?.length ?? 0;
    totalHebdomadaireResponseCount += _calculateCorrectAnswers();

    if (currentIndex >= challengeHebdomadaires.length - 1) {
      evaluationData.value = null;
    } else {
      final nextIndex = currentIndex + 1;
      evaluationData.value = challengeHebdomadaires[nextIndex];
      if (evaluationData.value?.steps == null ||
          evaluationData.value?.steps?.isEmpty == true) {
            isEmptyData.value = true;
      } else {
        currentStep.value = 1;
        revealedQuestions.clear();
        selectedAnswers.clear();
        
      }
    }
}

  RxInt currentStep = 1.obs;
  RxMap<int, bool> revealedQuestions = <int, bool>{}.obs;
  RxMap<int, List<int>> selectedAnswers = <int, List<int>>{}.obs;

  List<eval.Step> get steps => evaluationData.value?.steps ?? [];
  int get totalSteps => steps.length;

  eval.Step? get currentQuestionObj =>
      steps.isNotEmpty && currentStep.value <= steps.length
          ? steps[currentStep.value - 1]
          : null;

  String get currentQuestion {
    final question = currentQuestionObj?.question;
    return question?.content ?? '';
  }

  String get currentQuestionDescription {
    return currentQuestionObj?.question?.description ?? '';
  }

  String? get currentQuestionImagePath {
    return currentQuestionObj?.question?.imagePath;
  }

  List<eval.Option> get currentChoices {
    final question = currentQuestionObj?.question;
    return question?.options ?? <eval.Option>[];
  }

  String get currentQuestionType {
    return currentQuestionObj?.type ?? 'one';
  }

  bool isQuestionAnswered() {
    final selected = getSelectedChoices();
    return Validators.validateSelection(selected);
  }

  void selectChoice(int choiceId) {
    if (isRevealedForCurrentQuestion) return;

    final questionId = currentQuestionObj?.questionId ?? -1;
    selectedAnswers.putIfAbsent(questionId, () => []);
    final choices = selectedAnswers[questionId]!;

    if (currentQuestionType == "one") {
      choices.clear();
      choices.add(choiceId);
    } else {
      if (choices.contains(choiceId)) {
        choices.remove(choiceId);
      } else {
        choices.add(choiceId);
      }
    }
    selectedAnswers[questionId] = choices;
  }

  List<int> getSelectedChoices() {
    final questionId = currentQuestionObj?.questionId ?? -1;
    return selectedAnswers[questionId] ?? [];
  }

  bool get isRevealedForCurrentQuestion {
    return revealedQuestions[currentQuestionObj?.questionId ?? -9999] ?? false;
  }

  void revealAndGoNext() {
    if (isRevealedForCurrentQuestion) {
      _goNextImmediately();
    } else {
      _revealWithDelay();
    }
  }

  void _goNextImmediately() {
    goToNextQuestionOrFinish();
  }

  void _revealWithDelay() {
    if (currentQuestionObj?.questionId != null) {
      revealedQuestions[currentQuestionObj!.questionId!] = true;
    }

    Future.delayed(const Duration(milliseconds: 1000), () {
      goToNextQuestionOrFinish();
    });
  }

  void goToNextQuestionOrFinish() async {
    if (currentStep.value < totalSteps) {
      currentStep.value++;
    } else {
      int correctCount = _calculateCorrectAnswers();
      int total = totalSteps;
      final payload = {
        "test_id": evaluationData.value?.id,
        "data": selectedAnswers.entries.map((entry) {
          final question = steps.firstWhere(
              (step) => step.questionId == entry.key,
              orElse: () => eval.Step());
          final isTrueSelected = question.question?.isTrue ?? false;
          final isFalseSelected = question.question?.isFalse ?? false;

          if (isTrueSelected) {
            return {
              "question_id": entry.key,
              "selected_options": ["is_true"],
            };
          } else if (isFalseSelected) {
            return {
              "question_id": entry.key,
              "selected_options": ["is_false"],
            };
          } else {
            return {
              "question_id": entry.key,
              "selected_options": entry.value,
            };
          }
        }).toList(),
      };

      Get.dialog(
        const PopScope(canPop: false, child: Center(child: GlobalLoader())),
        barrierDismissible: false,
      );


      if (correctCount == total && evaluationData.value?.difficultyLevel == "difficile") {
        payload["got_difficult_question_right"] = ActionTestTypes.quizHardPassed.value;
      } else if (correctCount == total && evaluationData.value?.difficultyLevel == "facile") {
        payload["got_easy_question_right"] = ActionTestTypes.quizEasyPassed.value;
      }

      if (testType.value == "content_test" && evaluationData.value != null) {
        payload["action_test_type"] = ActionTestTypes.microlearningSessionCompleted.value;
      }

      if (testType.value == "challenge_hebdomadaire" && evaluationData.value != null) {
        payload["action_test_type"] ="challenge_hebdomadaire";
      }

      try {
        final response = await _evaluationRepository.submitEvaluation(payload);

        Get.back();

        if (response != null && response.containsKey("feedback_message")) {
          final feedbackMessage = response["feedback_message"] ?? "Bien joué";
          goToNextChallengeHebdomadaire();
          if (testType.value == "challenge_hebdomadaire" && evaluationData.value != null) {
            return;
          }

          if (testType.value == "challenge_hebdomadaire" && evaluationData.value == null) {
              total = totalHebdomadaireCount;
              correctCount = totalHebdomadaireResponseCount;
          }
          
          GlobalDialog.showTheDialog(
            iconTap: () => Get.offAllNamed(Routes.btmNavigationBar),
            imagePath: AppImages.bigOwl,
            title: feedbackMessage,
            subtitle: 'Vous avez réussi $correctCount/$total réponses',
            buttonText: 'Retour au dashboard',
            onButtonTap: () => Get.offAllNamed(Routes.btmNavigationBar),
            barrierDismissible: false,
          );



          if (correctCount == total) {
            Future.delayed(const Duration(milliseconds: 1000), () {
              _showConfetti();
            });
          }
        } else {
          LoadingUtils.showError(AppStrings.errorMessage);
        }
      } catch (e) {
        Get.back();
        debugPrint("Error submitting evaluation: $e");
        LoadingUtils.showError(AppStrings.errorMessage);
      }
    }
  }

  void _showConfetti() {
    final overlay = Overlay.of(Get.context!);

    final overlayEntry = OverlayEntry(
      builder: (context) {
        return const Stack(
          children: [
            Positioned(
              left: 0,
              child: GlobalConfetti(
                colors: [
                  Colors.blue,
                  Colors.pink,
                  Colors.purple,
                  Colors.green,
                  Colors.orange,
                ],
                alignment: Alignment.centerLeft,
                blastDirection: 0,
                emissionFrequency: 0.05,
                numberOfParticles: 40,
                gravity: 0.8,
                duration: Duration(seconds: 5),
              ),
            ),
            Positioned(
              right: 0,
              child: GlobalConfetti(
                colors: [
                  Colors.blue,
                  Colors.pink,
                  Colors.purple,
                  Colors.green,
                  Colors.orange,
                ],
                alignment: Alignment.centerRight,
                blastDirection: 3.14,
                emissionFrequency: 0.05,
                numberOfParticles: 40,
                gravity: 0.8,
                duration: Duration(seconds: 5),
              ),
            ),
          ],
        );
      },
    );

    overlay.insert(overlayEntry);

    Future.delayed(const Duration(seconds: 6), () {
      overlayEntry.remove();
    });
  }

  int _calculateCorrectAnswers() {
    int count = 0;

    for (final step in steps) {
      final question = step.question;
      if (question == null) continue;

      final userSelected = selectedAnswers[step.questionId] ?? [];

      if (step.type == 'true_false') {
        final isTrueSelected = userSelected.contains(1);
        final isFalseSelected = userSelected.contains(0);
        final isTrueCorrect = question.isTrue;
        final isFalseCorrect = question.isFalse;

        if ((isTrueSelected && isTrueCorrect) ||
            (isFalseSelected && isFalseCorrect)) {
          count++;
        }
      } else {
        final correctOptions = question.options
            ?.where((opt) => opt.isCorrect == 1)
            .map((opt) => opt.id)
            .whereType<int>()
            .toList();

        if (correctOptions == null || correctOptions.isEmpty) continue;

        final userSet = userSelected.toSet();
        final correctSet = correctOptions.toSet();

        if (step.type == 'one') {
          if (userSelected.isNotEmpty &&
              correctSet.contains(userSelected.first)) {
            count++;
          }
        } else if (step.type == 'many') {
          if (userSet.length == correctSet.length &&
              userSet.containsAll(correctSet)) {
            count++;
          }
        }
      }
    }

    return count;
  }

  void previousStep() {
    if (currentStep.value > 1) {
      currentStep.value--;
    }
  }

  Future<void> fetchEvaluationData(int contentId, bool isLesonCompleted) async {
    testType.value = "content_test";
    isLoading.value = true;
    isError.value = false;
    isEmptyData.value = false;

    try {
      final response = await _evaluationRepository.fetchEvaluation(contentId, isLesonCompleted);
      if (response.isError) {
        isError.value = true;
      } else if (response.isEmpty) {
        GlobalDialog.showTheDialog(
          iconTap: () => Get.offAllNamed(Routes.btmNavigationBar),
          imagePath: AppImages.nothingFound,
          title: "Aucun challenge disponible pour ce contenu!",
          subtitle: '',
          buttonText: AppStrings.continuer,
          onButtonTap: () => Get.offAllNamed(Routes.btmNavigationBar),
          barrierDismissible: true,
        );
        isEmptyData.value = true;
      } else {
        evaluationData.value = response.data;
      }
    } catch (e) {
      debugPrint("Error fetching evaluation: $e");
      isError.value = true;
    } finally {
      isLoading.value = false;
    }
  }



  Future<void> fetchChallengeHebdomadaireData() async {
    isLoading.value = true;
    isError.value = false;
    isEmptyData.value = false;
    testType.value = "challenge_hebdomadaire";

    try {
      final response = await _evaluationRepository.fetchChallengeHebdomadaire();
      if (response.isError) {
        isError.value = true;
      } else if (response.isEmpty) {
        GlobalDialog.showTheDialog(
          iconTap: () => Get.offAllNamed(Routes.btmNavigationBar),
          imagePath: AppImages.nothingFound,
          title: "Aucun challenge disponible!",
          subtitle: '',
          buttonText: AppStrings.continuer,
          onButtonTap: () => Get.offAllNamed(Routes.btmNavigationBar),
          barrierDismissible: true,
        );
        isEmptyData.value = true;
      } else {
        challengeHebdomadaires.value = response.dataList ?? [];
        evaluationData.value = response.dataList?.first;
        if (evaluationData.value?.steps == null ||
            evaluationData.value?.steps?.isEmpty == true) {
          isEmptyData.value = true;
        }
      }
    } catch (e) {
      debugPrint("Error fetching evaluation: $e");
      isError.value = true;
    } finally {
      isLoading.value = false;
    }
  }
}
