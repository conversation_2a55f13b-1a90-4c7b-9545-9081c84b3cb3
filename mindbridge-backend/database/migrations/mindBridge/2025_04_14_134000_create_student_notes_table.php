<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('etudiant_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('etudiant_id')->nullable();
            $table->foreign('etudiant_id')
                  ->references('id')
                  ->on(config('database.connections.mind_bridge.database') . '.etudiants')
                  ->onDelete('cascade');

            $table->unsignedBigInteger('enseignant_id')->nullable();

            $table->string('trimestre')->nullable();
            $table->string('moyenne')->nullable();
            $table->json('notes')->nullable();
            $table->string('appreciation')->nullable();

            $table->unsignedBigInteger('matiere_id')->nullable();
            $table->foreign('matiere_id')
                  ->references('id')
                  ->on(config('database.connections.centrale.database') . '.matieres')
                  ->onDelete('cascade');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_notes');
    }
};
