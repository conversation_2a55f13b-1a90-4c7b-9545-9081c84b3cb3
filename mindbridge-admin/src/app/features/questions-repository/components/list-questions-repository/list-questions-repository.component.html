<div class="content_main">
  <div class="filter">
    <div class="filde" [formGroup]="searchForm">
      <img class="search" src="assets/icones/search.png" alt="" />
      <input
        class="input bg-white"
        type="text"
        formControlName="title"
        placeholder="Recherche de questions"
        (change)="searchQuestions()"
      />
    </div>
    <div class="filde">
      <button class="button bg-white" (click)="filterAppeared()">
        <img src="/assets/icones/filters.png" alt="" />
        <span>Filtres</span>
      </button>
    </div>
    <!--<div class="filde">
      <a class="add" (click)="addQuestion()">
        <img src="/assets/icones/add_circle_element.png" alt="" />
        <span>Ajouter questions</span>
      </a>
    </div>-->
  </div>
  <div class="add_question" *ngIf="toggleAddQuestion">
    <div
      class="mi-auto flex w-full place-content-between gap-4"
      [formGroup]="form"
    >
      <div
        class="flex w-full flex-col gap-4 rounded-lg bg-white px-4 pb-4 pb-6 pt-4"
      >
        <div class="actions">
          <div class="left_actions">
            <button>
              <img src="assets/icones/imagesmode.png" alt="" />
            </button>
            <button>
              <img src="assets/icones/content_copy.png" alt="" />
            </button>
            <button (click)="deleteQuestion(isUpdateQuestionId)">
              <img src="assets/icones/delete_forever_black.png" alt="" />
            </button>
            <mat-slide-toggle class="example-margin" color="#fff">
              <span style="padding-left: 10px">Obligatoire</span>
            </mat-slide-toggle>
          </div>
          <div class="left_actions">
            <button class="btn_right_actions" (click)="censel()">
              Annuler
            </button>
            <button class="btn_right_actions" (click)="validateForm()">
              <!-- [disabled]="form.invalid" -->
              <!-- <img src="assets/icones/edit_question.png" alt="">
                            <span>Modifier question</span> -->
              <img
                [src]="
                  isUpdateQuestionId
                    ? 'assets/icones/check_update.png'
                    : 'assets/icones/add_circle.png'
                "
                alt=""
              />
              <span>
                {{
                  isUpdateQuestionId
                    ? 'Modifier question'
                    : 'Enregistrer question'
                }}
              </span>
              <!-- <span>Enregistrer modification</span> -->
            </button>
          </div>
        </div>
        <div class="flex flex-row gap-4">
          <div class="flex w-3/4 flex-col gap-2">
            <input
              class="border_b box-border h-16 w-full px-3"
              type="text"
              placeholder="Question"
              formControlName="content"
              [class.required]="requiredInput && form.get('content')?.invalid"
            />
          </div>
          <div class="flex w-1/4 flex-col gap-1">
            <label class="font-medium" for="input6">Question form</label>
            <select
              class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
              id="input6"
            >
              <option value="one">Un choix</option>
              <option value="many">Multiple choix</option>
            </select>
          </div>
        </div>
        <mat-slide-toggle
          class="example-margin"
          color="#fff"
          (click)="checked = !checked"
          [checked]="checked || form.get('description')?.value"
        >
          <span style="padding-left: 10px">Description</span>
        </mat-slide-toggle>
        <div class="flex w-full flex-row">
          <div class="flex w-full flex-col gap-2">
            <textarea
              class="box-border min-h-32 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
              placeholder="Entrer une description..."
              *ngIf="checked || form.get('description')?.value"
              formControlName="description"
              [class.required]="
                requiredInput && form.get('description')?.invalid
              "
            ></textarea>
          </div>
        </div>
<!-- 
        <div class="flex w-full flex-col gap-3 rounded-lg border border-gray-200 p-4">
          <div class="font-medium">Question Image (Optional)</div>

          <div class="flex flex-col gap-2">
            <label class="flex cursor-pointer items-center gap-2 rounded-lg bg-blue-50 px-4 py-2 text-blue-600 hover:bg-blue-100">
              <span>📤 Add Image (Optional)</span>
              <input
                type="file"
                accept="image/*"
                (change)="onImageSelected($event)"
                class="hidden"
              />
            </label>
            <span class="text-xs text-gray-500">Max 10MB (All image types)</span>
          </div>

          <div *ngIf="getImagePreviewUrl()" class="flex flex-col gap-2">
            <img
              [src]="getImagePreviewUrl()"
              alt="Question image preview"
              class="max-h-64 w-full rounded-lg object-contain"
            />
            <button
              type="button"
              (click)="removeImage()"
              class="rounded-lg bg-red-50 px-3 py-2 text-sm text-red-600 hover:bg-red-100"
            >
              ✕ Remove Image
            </button>
          </div>
        </div> -->

        <ng-container formArrayName="options">
          <div
            *ngFor="let option of options.controls; let i = index"
            [formGroupName]="i"
            class="input_answer flex flex-row"
          >
            <input
              class="border_b box-border min-h-10 w-full px-3"
              placeholder="Option {{ i + 1 }}"
              id="name-{{ i }}"
              type="text"
              formControlName="name"
            />
            <img
              class="icon_answer"
              [src]="
                i !== options.controls.length - 1
                  ? 'assets/icones/delete.svg'
                  : 'assets/icones/add.svg'
              "
              alt=""
              (click)="
                i !== options.controls.length - 1
                  ? deleteOption(i)
                  : addOption()
              "
            />
          </div>
        </ng-container>
      </div>
    </div>
  </div>
  @if (filters) {
    <form
      class="flex w-full flex-row justify-center rounded-lg border border-[#D5D7DA] bg-white p-2"
      [formGroup]="searchForm"
      (ngSubmit)="searchQuestions()"
    >
      <div class="flex w-full flex-col pl-2">
        <div class="my-4 flex w-full items-center justify-center">
          <div
            class="mr-2 h-[44px] w-1/3 rounded-lg border border-[#D5D7DA] bg-white p-2"
          >
            <select class="w-full pt-1" formControlName="category">
              <option [ngValue]="OPTION_ALL" selected>
                Toutes les catégories
              </option>
              @for (category of testCategories(); track category.id) {
                <option [ngValue]="category.id">{{ category.name }}</option>
              }
            </select>
          </div>
          <div
            class="mr-2 h-[44px] w-1/3 rounded-lg border border-[#D5D7DA] bg-white p-2"
          >
            <select class="w-full pt-1" formControlName="matiere">
              <option [ngValue]="OPTION_ALL" selected>
                Toutes les matières
              </option>
              @for (matiere of matieres(); track matiere.id) {
                <option [ngValue]="matiere.id">{{ matiere.name_fr }}</option>
              }
            </select>
          </div>
          <div
            class="mr-2 h-[44px] w-1/3 rounded-lg border border-[#D5D7DA] bg-white p-2"
          >
            <select class="w-full pt-1" formControlName="niveau">
              <option [ngValue]="OPTION_ALL" selected>Tous les niveaux</option>
              @for (niveau of allNiveau(); track niveau.id) {
                <option [ngValue]="niveau.id">{{ niveau.name }}</option>
              }
            </select>
          </div>
        </div>

        <div class="flex w-full items-center justify-between py-2 pl-1 pr-2">
          <button
            type="button"
            (click)="initiateSearchQuestions()"
            class="w-32 text-[#6BA4BD]"
          >
            Réinitialiser filtres
          </button>
          <button
            type="button"
            (click)="searchQuestions()"
            class="rounded-lg bg-gradient-to-b from-[#F0F6F8] via-[#E1EDF2] to-[#F2EEF4] px-4 py-2 text-[#568397]"
          >
            Apply filters
          </button>
        </div>
      </div>
    </form>
  }

  <div class="list_questions">
    @for (question of questions(); track question.id) {
      <app-question-display-read-only [question]="question" />

      <!--<div class="actions">
        <div class="left_actions" style="margin-left: auto">
          <button class="btn_right_actions" (click)="updateQuestion(question)">
            <img src="assets/icones/edit_question.png" alt="" />
            <span>Modifier la question</span>
          </button>
        </div>
      </div>-->
    }
    <div class="flex items-start justify-start gap-2 px-4 py-4">
      <div class="rounded-lg border border-gray-300 p-1.5">
        <button
          class="w-16 text-center font-medium"
          [disabled]="current_page() == 1"
          (click)="goToPage(Number(current_page()) - 1)"
        >
          Précédent
        </button>
      </div>
      <div class="rounded-lg border border-gray-300 bg-white p-1.5">
        <button
          class="w-14 text-center font-medium"
          [disabled]="current_page() == total_pages()"
          (click)="goToPage(Number(current_page()) + 1)"
        >
          Suivant
        </button>
      </div>
      <p class="flex-1 p-2 text-right font-medium">
        Page {{ current_page() }} de {{ total_pages() }}
      </p>
    </div>
  </div>
</div>
