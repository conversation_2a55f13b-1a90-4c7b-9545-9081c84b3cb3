import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'unicodeToEmoji',
})
export class UnicodeToEmojiPipe implements PipeTransform {
  transform(value: string): string {
    if (!value) return '';

    // Check if the input is already an emoji
    const emojiRegex =
      /[\uD800-\uDBFF][\uDC00-\uDFFF]|[\u2600-\u26FF\u2700-\u27BF\uFE0F]|[\u0023\u002A\u0030-\u0039]\uFE0F\u20E3/;
    if (emojiRegex.test(value)) {
      return value;
    }

    // If it's a Unicode code point, convert it to an emoji
    if (value.startsWith('U+')) {
      try {
        return String.fromCodePoint(parseInt(value.replace('U+', ''), 16));
      } catch {
        return ''; // Return an empty string if parsing fails
      }
    }

    return value; // Return as is if it's not a Unicode or an emoji
  }
}
