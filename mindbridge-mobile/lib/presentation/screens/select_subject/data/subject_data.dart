// import 'package:flutter/material.dart';

// import '../../../../core/utilities/app_images.dart';
// import '../model/subject_model.dart';

// final List<Subject> subjectsData = [
//   Subject(
//     title: 'Maths',
//     titleColor: const Color(0xFF406271),
//     gradientColors: [
//       const Color(0xFFC4DBE5),
//       const Color(0xFFC4DBE5),
//       const Color(0xFF6BA4BD)
//     ],
//     borderColors: [
//       const Color(0xFF6BA4BD),
//       const Color(0xFFC4DBE5),
//       Colors.white,
//     ],
//     progress: '80',
//     imagePath: AppImages.maths,
//   ),
//   Subject(
//     title: 'Chimie',
//     titleColor: const Color(0xFF654672),
//     gradientColors: [
//       const Color(0xFFE5DEE8),
//       const Color(0xFFCBBCD2),
//       const Color(0xFF7E588E)
//     ],
//     borderColors: [
//       const Color(0xFFB29BBB),
//       const Color(0xFFCBBCD2),
//       Colors.white,
//     ],
//     progress: '80',
//     imagePath: AppImages.chemistry,
//   ),
//   Subject(
//     title: 'Géométrie',
//     titleColor: const Color(0xFF4595A4),
//     gradientColors: [
//       const Color(0xFFECFAFD),
//       const Color(0xFFCDF2F9),
//       const Color(0xFF7ABCC7)
//     ],
//     borderColors: [
//       const Color(0xFFC1EFF7),
//       const Color(0xFFDAF5FA),
//       Colors.white,
//     ],
//     progress: '5',
//     imagePath: AppImages.geometry,
//   ),
//   Subject(
//     title: 'Science',
//     titleColor: const Color(0xFF365672),
//     gradientColors: [
//       const Color(0xFFE0E8F0),
//       const Color(0xFF9CAEBE),
//       const Color(0xFF58738B)
//     ],
//     borderColors: [
//       const Color(0xFFADBCCA),
//       const Color(0xFFCFD9E3),
//       Colors.white,
//     ],
//     progress: '80',
//     imagePath: AppImages.science,
//   ),
//   Subject(
//     title: 'Français',
//     titleColor: const Color(0xFFBE1A87),
//     gradientColors: [
//       const Color(0xFFF9E8F3),
//       const Color(0xFFECBADB),
//       const Color(0xFFCB489F)
//     ],
//     borderColors: [
//       const Color(0xFFE5A3CF),
//       const Color(0xFFF2D1E7),
//       Colors.white,
//     ],
//     progress: '18',
//     imagePath: AppImages.french,
//   ),
//   Subject(
//     title: 'Finance',
//     titleColor: const Color(0xFF292F3E),
//     gradientColors: [
//       const Color(0xFFBCC3D3),
//       const Color(0xFF9FA5B5),
//       const Color(0xFF464D5C)
//     ],
//     borderColors: [
//       const Color(0xFFADBCCA),
//       const Color(0xFFCFD9E3),
//       Colors.white,
//     ],
//     progress: '80',
//     imagePath: AppImages.finance,
//   ),
//   Subject(
//     title: 'Gestion',
//     titleColor: Colors.white,
//     gradientColors: [
//       const Color(0xFFADB4C4),
//       const Color(0xFF646A7A),
//       const Color(0xFF212632)
//     ],
//     borderColors: [
//       const Color(0xFFC1EFF7),
//       const Color(0xFFDAF5FA),
//       Colors.white,
//     ],
//     progress: '5',
//     imagePath: AppImages.management,
//   ),
// ];
