import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import '../../../../core/utilities/app_images.dart';
import '../../../../core/utilities/constants.dart';
import '../../../widgets/avatar_btm_sheet.dart';
import '../../../widgets/avatar_clipper.dart';
import '../../../widgets/profile_info_section.dart';
import '../../home/<USER>/home_controller.dart';

class EspaceHeader extends StatelessWidget {
  final double avatarSize;
  final String avatarId;
  final Function(String) onAvatarChange;
  const EspaceHeader({
    super.key,
    required this.avatarSize,
    required this.avatarId,
    required this.onAvatarChange,
  });

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find<HomeController>();

    return Obx(() {
      final profile = controller.profile.value;

      return Column(
        children: [
          Stack(
            alignment: Alignment.center,
            clipBehavior: Clip.none,
            children: [
              ClipPath(
                clipper: AvatarClipper(fromTop: false),
                child: Container(
                  padding: EdgeInsets.all(2.w),
                  height: 27.h,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(AppImages.univers),
                      fit: BoxFit.fill,
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Image.asset(
                                AppImages.logo,
                                height: 4.h,
                              ),
                              SizedBox(width: 2.w),
                              Text(
                                appName,
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          IconButton(
                            onPressed: () {},
                            icon: Image.asset(
                              AppImages.notification,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                bottom: -avatarSize * 0.5,
                child: AvatarBottomSheet(
                  avatarSize: avatarSize,
                  avatarId: avatarId,
                  onAvatarChange: onAvatarChange,
                ),
              ),
            ],
          ),
          Padding(
            padding:
                EdgeInsets.only(top: avatarSize * 0.6, left: 4.w, right: 4.w),
            child: ProfileInfoSection(
              name: profile?.name ?? "...",
              gamificationName: profile?.nameGamification ?? "...",
              levelName: profile?.niveau?.name ?? "...",
              balance: "${profile?.points ?? 0} pts",
              ranking:
                  "${profile?.classement ?? 0} / ${profile?.countEtudiant ?? 0}",
              bgGradient: AppColors.backgroundAiGradient,
              isEspaceHeader: true,
            ),
          ),
        ],
      );
    });
  }
}
