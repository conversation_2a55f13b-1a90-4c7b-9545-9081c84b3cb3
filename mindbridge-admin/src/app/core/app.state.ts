import { ActionReducerMap } from "@ngrx/store";
import { sharedReducer, SharedState } from "../features/shared/store/shared.reducer";
import { testManagementReducer, TestManagementState } from "../features/test-management/store/test-management.reducers";
import { QuestionRepositoryState, questionRepositoryStateReducer } from "../features/questions-repository/store/test-management.reducers";
import { matiereReducer, MatiereState } from "../features/matieres/store/matieres.reducers";

export interface AppState {
    shared: SharedState,
    testManagement: TestManagementState,
    questionsRepository: QuestionRepositoryState,
    matieres: MatiereState
}
export const reducers: ActionReducerMap<AppState> = {
    shared: sharedReducer,
    testManagement: testManagementReducer,
    questionsRepository: questionRepositoryStateReducer,
    matieres: matiereReducer
};
