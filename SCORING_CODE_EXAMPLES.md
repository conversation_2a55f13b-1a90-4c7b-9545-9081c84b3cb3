# Scoring System - Ready-to-Use Code Examples

## 1. Database Migrations

### Migration: Create test_sections table
```php
// database/migrations/mindBridge/2024_12_20_create_test_sections_table.php
Schema::create('test_sections', function (Blueprint $table) {
    $table->id();
    $table->foreignId('test_id')
        ->constrained('tests')
        ->cascadeOnDelete();
    $table->string('title');
    $table->text('description')->nullable();
    $table->integer('order')->default(0);
    $table->integer('max_score')->default(10);
    $table->timestamps();
});
```

### Migration: Create test_scoring_rules table
```php
// database/migrations/mindBridge/2024_12_20_create_test_scoring_rules_table.php
Schema::create('test_scoring_rules', function (Blueprint $table) {
    $table->id();
    $table->foreignId('test_id')
        ->constrained('tests')
        ->cascadeOnDelete();
    $table->foreignId('section_id')
        ->nullable()
        ->constrained('test_sections')
        ->nullOnDelete();
    $table->integer('min_score');
    $table->integer('max_score');
    $table->string('interpretation');
    $table->text('feedback')->nullable();
    $table->text('recommendation')->nullable();
    $table->timestamps();
});
```

### Migration: Modify steps table
```php
// database/migrations/mindBridge/2024_12_20_modify_steps_table.php
Schema::table('steps', function (Blueprint $table) {
    $table->foreignId('section_id')
        ->nullable()
        ->after('test_id')
        ->constrained('test_sections')
        ->nullOnDelete();
    $table->integer('points_value')
        ->default(1)
        ->after('section_id');
});
```

### Migration: Modify etudiant_test_status table
```php
// database/migrations/mindBridge/2024_12_20_modify_etudiant_test_status_table.php
Schema::table('etudiant_test_status', function (Blueprint $table) {
    $table->json('section_scores')->nullable()->after('score');
    $table->json('feedback')->nullable()->after('section_scores');
    $table->json('recommendations')->nullable()->after('feedback');
});
```

---

## 2. Models

### TestSection Model
```php
// app/Models/MindBridge/TestSection.php
<?php
namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Model;

class TestSection extends Model {
    protected $connection = 'mind_bridge';
    protected $table = 'test_sections';
    
    protected $fillable = [
        'test_id', 'title', 'description', 'order', 'max_score'
    ];
    
    public function test() {
        return $this->belongsTo(Test::class);
    }
    
    public function steps() {
        return $this->hasMany(Step::class, 'section_id');
    }
    
    public function scoringRules() {
        return $this->hasMany(TestScoringRule::class, 'section_id');
    }
}
```

### TestScoringRule Model
```php
// app/Models/MindBridge/TestScoringRule.php
<?php
namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Model;

class TestScoringRule extends Model {
    protected $connection = 'mind_bridge';
    protected $table = 'test_scoring_rules';
    
    protected $fillable = [
        'test_id', 'section_id', 'min_score', 'max_score',
        'interpretation', 'feedback', 'recommendation'
    ];
    
    public function test() {
        return $this->belongsTo(Test::class);
    }
    
    public function section() {
        return $this->belongsTo(TestSection::class, 'section_id');
    }
}
```

### Update Test Model
```php
// Add to app/Models/MindBridge/Test.php
public function sections() {
    return $this->hasMany(TestSection::class);
}

public function scoringRules() {
    return $this->hasMany(TestScoringRule::class);
}
```

### Update Step Model
```php
// Modify app/Models/MindBridge/Step.php
protected $fillable = [
    'order', 'required', 'condition', 'type', 
    'test_id', 'question_id', 'section_id', 'points_value'
];

public function section() {
    return $this->belongsTo(TestSection::class, 'section_id');
}
```

---

## 3. Scoring Service

```php
// app/Services/ScoringService.php
<?php
namespace App\Services;

use App\Models\MindBridge\{Option, TestSection, TestScoringRule};

class ScoringService {
    
    public function calculateTestScore($test, $answers) {
        $sectionScores = [];
        $totalScore = 0;
        $totalMaxScore = 0;
        
        foreach ($test->sections as $section) {
            $sectionScore = $this->calculateSectionScore($section, $answers);
            $sectionScores[$section->id] = $sectionScore;
            $totalScore += $sectionScore['score'];
            $totalMaxScore += $sectionScore['max_score'];
        }
        
        $percentage = ($totalMaxScore > 0) ? 
            round(($totalScore / $totalMaxScore) * 100, 2) : 0;
        
        return [
            'total_score' => $totalScore,
            'total_max_score' => $totalMaxScore,
            'percentage' => $percentage,
            'section_scores' => $sectionScores,
            'feedback' => $this->generateFeedback($test, $sectionScores),
            'recommendations' => $this->generateRecommendations($test, $sectionScores),
            'interpretation' => $this->getInterpretation($test, null, $percentage)
        ];
    }
    
    private function calculateSectionScore($section, $answers) {
        $score = 0;
        $maxScore = 0;
        
        foreach ($section->steps as $step) {
            $pointsValue = $step->points_value ?? 1;
            $maxScore += $pointsValue;
            
            if (isset($answers[$step->question_id])) {
                if ($this->isAnswerCorrect($step, $answers[$step->question_id])) {
                    $score += $pointsValue;
                }
            }
        }
        
        $percentage = ($maxScore > 0) ? 
            round(($score / $maxScore) * 100, 2) : 0;
        
        return [
            'score' => $score,
            'max_score' => $maxScore,
            'percentage' => $percentage
        ];
    }
    
    private function isAnswerCorrect($step, $selectedOptions) {
        $correctOptions = Option::where('question_id', $step->question_id)
            ->where('isCorrect', true)
            ->pluck('id')
            ->map(fn($id) => (int)$id)
            ->toArray();
        
        $selected = is_array($selectedOptions) ? 
            array_map('intval', $selectedOptions) : [(int)$selectedOptions];
        
        if (empty($correctOptions)) {
            return false;
        }
        
        $intersect = array_intersect($selected, $correctOptions);
        return count($intersect) === count($correctOptions);
    }
    
    private function generateFeedback($test, $sectionScores) {
        $feedback = [];
        
        foreach ($test->sections as $section) {
            $score = $sectionScores[$section->id];
            $rule = $this->findApplicableRule($section, $score['percentage']);
            
            if ($rule) {
                $feedback[$section->id] = $rule->feedback;
            }
        }
        
        return $feedback;
    }
    
    private function generateRecommendations($test, $sectionScores) {
        $recommendations = [];
        
        foreach ($test->sections as $section) {
            $score = $sectionScores[$section->id];
            $rule = $this->findApplicableRule($section, $score['percentage']);
            
            if ($rule && $rule->recommendation) {
                $recommendations[$section->id] = $rule->recommendation;
            }
        }
        
        return $recommendations;
    }
    
    private function findApplicableRule($section, $percentage) {
        return TestScoringRule::where('section_id', $section->id)
            ->where('min_score', '<=', $percentage)
            ->where('max_score', '>=', $percentage)
            ->first();
    }
    
    private function getInterpretation($test, $section, $percentage) {
        $rule = TestScoringRule::where(function($q) use ($test, $section) {
            $q->where('test_id', $test->id);
            if ($section) {
                $q->where('section_id', $section->id);
            } else {
                $q->whereNull('section_id');
            }
        })
        ->where('min_score', '<=', $percentage)
        ->where('max_score', '>=', $percentage)
        ->first();
        
        return $rule ? $rule->interpretation : 'À évaluer';
    }
}
```

---

## 4. Controller Update

```php
// Update in app/Http/Controllers/MindBridge/MindBridgeEtudiantController.php
use App\Services\ScoringService;

public function submitTestAnswers(Request $request) {
    // ... existing validation code ...
    
    $scoringService = new ScoringService();
    
    // Build answers array: [question_id => selected_options]
    $answers = [];
    foreach ($request->data as $questionData) {
        $answers[$questionData['question_id']] = 
            $questionData['selected_options'];
    }
    
    // Calculate scores
    $testResults = $scoringService->calculateTestScore($test, $answers);
    
    // Store answers
    foreach ($request->data as $questionData) {
        EtudiantTestAnswers::updateOrCreate(
            [
                'etudiant_id' => $etudiant->id,
                'question_id' => $questionData['question_id'],
            ],
            [
                'test_id' => $test->id,
                'selected_options' => json_encode($questionData['selected_options']),
                'score' => 1, // Mark as answered
            ]
        );
    }
    
    // Update test status with detailed results
    $testStatus = EtudiantTestStatus::updateOrCreate(
        [
            'etudiant_id' => $etudiant->id,
            'test_id' => $test->id,
        ],
        [
            'status' => 'TERMINE',
            'score' => $testResults['total_score'],
            'section_scores' => json_encode($testResults['section_scores']),
            'feedback' => json_encode($testResults['feedback']),
            'recommendations' => json_encode($testResults['recommendations']),
            'started_at' => $testStatus->started_at ?? now(),
            'completed_at' => now(),
        ]
    );
    
    return response()->json([
        'message' => 'Test completed successfully',
        'results' => $testResults
    ], 200);
}
```

---

## 5. API Response Example

```json
{
  "message": "Test completed successfully",
  "results": {
    "total_score": 30,
    "total_max_score": 40,
    "percentage": 75,
    "interpretation": "Capacités cognitives globalement satisfaisantes",
    "section_scores": {
      "1": {
        "score": 8,
        "max_score": 10,
        "percentage": 80
      },
      "2": {
        "score": 7,
        "max_score": 10,
        "percentage": 70
      },
      "3": {
        "score": 6,
        "max_score": 10,
        "percentage": 60
      },
      "4": {
        "score": 9,
        "max_score": 10,
        "percentage": 90
      }
    },
    "feedback": {
      "1": "Tes compétences verbales sont très bonnes...",
      "2": "Tu as montré une bonne capacité...",
      "3": "Tu peux améliorer un peu ta mémoire...",
      "4": "Ta rapidité est impressionnante !"
    },
    "recommendations": {
      "1": "Continue à lire et découvrir de nouveaux mots",
      "2": "Super !",
      "3": "Joue à des jeux de mémoire",
      "4": "Continue comme ça !"
    }
  }
}
```


