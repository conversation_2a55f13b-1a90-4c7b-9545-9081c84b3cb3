// To parse this JSON data, do
//
//     final lesson = lessonFromJson(jsonString);

import 'dart:convert';

Lesson lessonFromJson(String str) => Lesson.fromJson(json.decode(str));

String lessonToJson(Lesson data) => json.encode(data.toJson());

class Lesson {
  List<Content>? contents;

  Lesson({
    this.contents,
  });

  factory Lesson.fromJson(Map<String, dynamic> json) => Lesson(
        contents: json["contents"] == null
            ? []
            : List<Content>.from(
                json["contents"]!.map((x) => Content.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "contents": contents == null
            ? []
            : List<dynamic>.from(contents!.map((x) => x.toJson())),
      };
}

class Content {
  int? id;
  String? type;
  String? title;
  String? description;
  String? content;
  Niveau? niveau;
  Matiere? matiere;
  Chapter? chapter;

  Content({
    this.id,
    this.type,
    this.title,
    this.description,
    this.content,
    this.niveau,
    this.matiere,
    this.chapter,
  });

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        id: json["id"],
        type: json["type"],
        title: json["title"],
        description: json["description"],
        content: json["content"],
        niveau: json["niveau"] == null ? null : Niveau.fromJson(json["niveau"]),
        matiere:
            json["matiere"] == null ? null : Matiere.fromJson(json["matiere"]),
        chapter:
            json["chapter"] == null ? null : Chapter.fromJson(json["chapter"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "type": type,
        "title": title,
        "description": description,
        "content": content,
        "niveau": niveau?.toJson(),
        "matiere": matiere?.toJson(),
        "chapter": chapter?.toJson(),
      };
}

class Chapter {
  int? id;
  String? chapter;
  String? description;
  int? matiereId;
  int? niveauId;
  DateTime? createdAt;
  DateTime? updatedAt;

  Chapter({
    this.id,
    this.chapter,
    this.description,
    this.matiereId,
    this.niveauId,
    this.createdAt,
    this.updatedAt,
  });

  factory Chapter.fromJson(Map<String, dynamic> json) => Chapter(
        id: json["id"],
        chapter: json["chapter"],
        description: json["description"],
        matiereId: json["matiere_id"],
        niveauId: json["niveau_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "chapter": chapter,
        "description": description,
        "matiere_id": matiereId,
        "niveau_id": niveauId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class Matiere {
  int? id;
  String? nameFr;
  String? nameAr;
  dynamic description;
  String? imageUrl;
  String? gradientBackground;
  String? gradientBorder;
  dynamic createdAt;
  dynamic updatedAt;
  dynamic deletedAt;

  Matiere({
    this.id,
    this.nameFr,
    this.nameAr,
    this.description,
    this.imageUrl,
    this.gradientBackground,
    this.gradientBorder,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  factory Matiere.fromJson(Map<String, dynamic> json) => Matiere(
        id: json["id"],
        nameFr: json["name_fr"],
        nameAr: json["name_ar"],
        description: json["description"],
        imageUrl: json["image_url"],
        gradientBackground: json["gradient_background"],
        gradientBorder: json["gradient_border"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        deletedAt: json["deleted_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name_fr": nameFr,
        "name_ar": nameAr,
        "description": description,
        "image_url": imageUrl,
        "gradient_background": gradientBackground,
        "gradient_border": gradientBorder,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "deleted_at": deletedAt,
      };
}

class Niveau {
  int? id;
  String? name;
  String? color;
  String? background;
  dynamic description;
  dynamic createdAt;
  dynamic updatedAt;
  dynamic deletedAt;

  Niveau({
    this.id,
    this.name,
    this.color,
    this.background,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  factory Niveau.fromJson(Map<String, dynamic> json) => Niveau(
        id: json["id"],
        name: json["name"],
        color: json["color"],
        background: json["background"],
        description: json["description"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        deletedAt: json["deleted_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "color": color,
        "background": background,
        "description": description,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "deleted_at": deletedAt,
      };
}
