import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:random_avatar/random_avatar.dart';
import 'package:sizer/sizer.dart';

class AvatarBottomSheet extends StatelessWidget {
  final double avatarSize;
  final String avatarId;
  final Function(String) onAvatarChange;

  const AvatarBottomSheet({
    super.key,
    required this.avatarSize,
    required this.avatarId,
    required this.onAvatarChange,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.bottomSheet(
          _AvatarSelectionDialog(onAvatarSelected: onAvatarChange),
          isScrollControlled: true,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(35),
              topRight: Radius.circular(35),
            ),
          ),
          backgroundColor: Colors.white,
        );
      },
      child: CircleAvatar(
        radius: avatarSize / 2,
        backgroundColor: Colors.transparent,
        child: ClipOval(
          child: SizedB<PERSON>(
            width: avatarSize,
            height: avatarSize,
            child: RandomAvatar(avatarId, trBackground: true),
          ),
        ),
      ),
    );
  }
}

class _AvatarSelectionDialog extends StatefulWidget {
  final Function(String) onAvatarSelected;

  const _AvatarSelectionDialog({required this.onAvatarSelected});

  @override
  State<_AvatarSelectionDialog> createState() => _AvatarSelectionDialogState();
}

class _AvatarSelectionDialogState extends State<_AvatarSelectionDialog> {
  final List<String> avatarIds = List.generate(1000, (index) => 'user_$index');

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 60.h,
      child: GridView.builder(
        padding: EdgeInsets.all(4.w),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 2.w,
          mainAxisSpacing: 2.h,
        ),
        itemCount: avatarIds.length,
        itemBuilder: (context, index) {
          final avatarId = avatarIds[index];
          return GestureDetector(
            onTap: () {
              widget.onAvatarSelected(avatarId);
              Get.back();
            },
            child: RandomAvatar(
              avatarId,
              trBackground: false,
            ),
          );
        },
      ),
    );
  }
}
