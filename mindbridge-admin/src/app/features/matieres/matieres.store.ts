import { patchState, signalState } from '@ngrx/signals';
import { inject, Injectable } from '@angular/core';
import { Observable, pipe, take } from 'rxjs';
import { finalize, map, switchMap, tap } from 'rxjs/operators';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { MatieresService } from './matieres.service';
import {
  MatiereResponse,
  Niveau,
  NiveauMini,
} from './models/matiere-response.model';
import { ContenueResponse } from './models/contenue-response.model';
import { SetLoading } from '../shared/store/shared.actions';
import { Store } from '@ngrx/store';
import { AppState } from '../../core/app.state';
import { ChapterResponse } from './models/chapter-response.model';
import { ChapterRequest } from './models/chapter-request.model';
import {
  ApiResponse,
  ApiResponseNoPagination,
} from '../../core/models/api-response';
import { MatieresByNiveauResponseWrapper } from './models/niveau-response.model';
import {
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from '../shared/global.config';
import {
  ContentSearchForm,
  IContentSearchForm,
} from './models/content-search-request';
import { Pagination } from '../shared/helpers/query.helper';

export type MatieresState = {
  matieres: MatiereResponse[];
  allMatieres: MatiereResponse[];
  contenues: ContenueResponse[];
  contentsSearchForm: IContentSearchForm;
  niveaux: NiveauMini[];
  chapters: ChapterResponse[];
  current_page: number;
  total_pages: number;
  per_page: number;
  total_items: number;
  allChapters: ChapterResponse[];
};

export const initialMatieresState: MatieresState = {
  matieres: [],
  allMatieres: [],
  contenues: [],
  contentsSearchForm: new ContentSearchForm(),
  niveaux: [],
  chapters: [],
  current_page: DEFAULT_CURRENT_PAGE,
  total_pages: 1,
  per_page: DEFAULT_PAGE_SIZE,
  total_items: 0,
  allChapters: [],
};

@Injectable()
export class MatieresStore {
  store = inject<Store<AppState>>(Store);

  readonly state = signalState<MatieresState>(initialMatieresState);
  #matierseService = inject(MatieresService);

  setSearchForm = (contentsSearchForm: IContentSearchForm) =>
    patchState(this.state, { contentsSearchForm });

  setCurrentPage = (current_page: number) =>
    patchState(this.state, { current_page });

  getMatieres = rxMethod<any>(
    pipe(
      take(1),
      switchMap(() =>
        this.#matierseService.getListeMatieres().pipe(
          map((response: ApiResponse<MatiereResponse>) => response.data),
          tap((matieres: MatiereResponse[]) => {
            patchState(this.state, { matieres });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      ),
      finalize(() => {})
    )
  );
  getAllMatieres = rxMethod<void>(
    pipe(
      take(1),
      switchMap(() =>
        this.#matierseService.allMatieres().pipe(
          map(
            (allMatieres: ApiResponseNoPagination<MatiereResponse>) =>
              allMatieres.data
          ),
          tap((allMatieres: MatiereResponse[]) => {
            patchState(this.state, { allMatieres });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      ),
      finalize(() => {})
    )
  );
  getAllNiveaux = rxMethod<void>(
    pipe(
      take(1),
      switchMap(() =>
        this.#matierseService.allNiveaus().pipe(
          map((response: ApiResponseNoPagination<NiveauMini>) => response.data),
          tap((niveaux: NiveauMini[]) => {
            patchState(this.state, { niveaux });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      ),
      finalize(() => {})
    )
  );

  matiereContents = rxMethod<number>(
    pipe(
      switchMap((matiere: number) => {
        const pagination: Pagination = {
          current_page: this.state.current_page(),
          per_page: this.state.per_page(),
        };
        return this.#matierseService
          .matiereContents(matiere, this.state.contentsSearchForm(), pagination)
          .pipe(
            tap((response: ApiResponse<ContenueResponse>) => {
              patchState(this.state, {
                contenues: response.data,

                current_page: response.current_page,
                per_page: response.per_page,
                total_pages: response.total_pages,
                total_items: response.total_items,
              });
            }),
            finalize(() => {
              this.store.dispatch(SetLoading({ isAppLoading: false }));
            })
          );
      }),
      finalize(() => {})
    )
  );

  matiereContentsJustByMatiereId = rxMethod<number>(
    pipe(
      switchMap((matiere: number) =>
        this.#matierseService.matiereContentsByJustMatriereId(matiere).pipe(
          map((response: any) => response.data),
          tap((contenues: ContenueResponse[]) => {
            patchState(this.state, { contenues });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      ),
      finalize(() => {})
    )
  );

  getNiveauxByMatiere = rxMethod<number>(
    pipe(
      switchMap((matiere: number) =>
        this.#matierseService.allNiveauxByMatiere(matiere).pipe(
          map((response: MatiereResponse) => response.niveaux),
          tap((niveaux: Niveau[]) => {
            patchState(this.state, { niveaux });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      )
    )
  );
  getMatieresByNiveau = rxMethod<number>(
    pipe(
      switchMap((niveau_id: number) =>
        this.#matierseService.getAllMatieresByNiveau(niveau_id).pipe(
          map((response: MatieresByNiveauResponseWrapper) =>
            response.matieres.map((m) => {
              const matiereResponse: MatiereResponse = {
                id: m.id,
                name_fr: m.name_fr,
                name_ar: m.name_ar,
                description: m.description,
                niveaux: [],
              };
              return matiereResponse;
            })
          ),
          tap((matieres: MatiereResponse[]) => {
            patchState(this.state, { matieres });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      )
    )
  );

  chapterByMatiereAndNiveau = rxMethod<ChapterRequest>(
    pipe(
      switchMap((chapterRequest: ChapterRequest) =>
        this.#matierseService.chapterByMatiereAndNiveau(chapterRequest).pipe(
          map((response: any) => response),
          tap((chapters: ChapterResponse[]) => {
            patchState(this.state, { chapters });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      )
    )
  );

  getAllChapters = rxMethod<void>(
    pipe(
      take(1),
      switchMap(() =>
        this.#matierseService.allChapitres().pipe(
          map((response: ChapterResponse[]) => response),
          tap((allChapters: ChapterResponse[]) => {
            patchState(this.state, { allChapters });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      ),
      finalize(() => {})
    )
  );
}
