services:
  mindbridge_bo:
    build: .
    container_name: mindbridge_bo
    restart: always
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mindbridge_bo.rule=Host(`mind-bridge-admin.awlyg.tech`)"
      - "traefik.http.routers.mindbridge_bo.entrypoints=websecure"
      - "traefik.http.routers.mindbridge_bo.tls.certresolver=myresolver"
    networks:
      - traefik

networks:
  traefik:
    external: true