import { Component, inject, OnInit, Signal } from '@angular/core';
import { Router } from '@angular/router';
import { MatieresStore } from '../../matieres.store';
import { MatiereResponse, Niveau } from '../../models/matiere-response.model';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../core/app.state';
import { SetLoading } from '../../../shared/store/shared.actions';
import { NgStyle, SlicePipe } from '@angular/common';
import { MatTooltip } from '@angular/material/tooltip';

@Component({
  selector: 'app-liste-matieres',
  templateUrl: './liste-matieres.component.html',
  styleUrl: './liste-matieres.component.scss',
  providers: [MatieresStore],
  imports: [NgStyle, SlicePipe, MatTooltip],
})
export class ListMatieresComponent implements OnInit {
  private router = inject(Router);
  private store = inject<Store<AppState>>(Store);

  #matieresStore = inject(MatieresStore);
  allMatieres: Signal<MatiereResponse[]> =
    this.#matieresStore.state.allMatieres;

  ngOnInit() {
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.#matieresStore.getAllMatieres();
  }

  getContenus(matiere: any) {
    this.router.navigate(['/list-contenues', matiere.id]);
  }

  getTooltipMessage(niveaux: Niveau[]) {
    return niveaux.map((niveau) => niveau.name).join(', ');
  }
}
