<?php

namespace Database\Seeders\centrale;

use Illuminate\Database\Seeder;
use DB;


class NiveauxMatiereSeederCentrale extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $data = [
            // CE2
            $this->createEntry(1, 1), // Français
            $this->createEntry(1, 2), // Mathématiques
            $this->createEntry(1, 3), // Sciences Naturelles
            $this->createEntry(1, 4), // Finances






            
            // // ******************** École Primaire *********************
            // // CP
            // $this->createEntry(1, 1), // Français
            // $this->createEntry(1, 2), // Mathématiques
            // $this->createEntry(1, 3), // Histoire-Géographie
            // $this->createEntry(1, 6), // Sciences Naturelles
            // $this->createEntry(1, 7), // Anglais
            // $this->createEntry(1, 9), // EPS
            // $this->createEntry(1, 10), // Éducation Civique

            // // CE1
            // $this->createEntry(2, 1), // Français
            // $this->createEntry(2, 2), // Mathématiques
            // $this->createEntry(2, 3), // Histoire-Géographie
            // $this->createEntry(2, 6), // Sciences Naturelles
            // $this->createEntry(2, 7), // Anglais
            // $this->createEntry(2, 9), // EPS
            // $this->createEntry(2, 10), // Éducation Civique


            // // CM1
            // $this->createEntry(4, 1), // Français
            // $this->createEntry(4, 2), // Mathématiques
            // $this->createEntry(4, 3), // Histoire-Géographie
            // $this->createEntry(4, 4), // Sciences Physiques
            // $this->createEntry(4, 6), // Sciences Naturelles
            // $this->createEntry(4, 7), // Anglais
            // $this->createEntry(4, 9), // EPS

            // // CM2
            // $this->createEntry(5, 1), // Français
            // $this->createEntry(5, 2), // Mathématiques
            // $this->createEntry(5, 3), // Histoire-Géographie
            // $this->createEntry(5, 4), // Sciences Physiques
            // $this->createEntry(5, 6), // Sciences Naturelles
            // $this->createEntry(5, 7), // Anglais
            // $this->createEntry(5, 9), // EPS

            // // ******************** Collège *********************
            // // 6e
            // $this->createEntry(6, 1), // Français
            // $this->createEntry(6, 2), // Mathématiques
            // $this->createEntry(6, 3), // Histoire-Géographie
            // $this->createEntry(6, 4), // Sciences Physiques
            // $this->createEntry(6, 5), // Chimie
            // $this->createEntry(6, 6), // Sciences Naturelles
            // $this->createEntry(6, 7), // Anglais
            // $this->createEntry(6, 9), // EPS

            // // 5e
            // $this->createEntry(7, 1), // Français
            // $this->createEntry(7, 2), // Mathématiques
            // $this->createEntry(7, 3), // Histoire-Géographie
            // $this->createEntry(7, 4), // Sciences Physiques
            // $this->createEntry(7, 5), // Chimie
            // $this->createEntry(7, 6), // Sciences Naturelles
            // $this->createEntry(7, 7), // Anglais
            // $this->createEntry(7, 9), // EPS

            // // 4e
            // $this->createEntry(8, 1), // Français
            // $this->createEntry(8, 2), // Mathématiques
            // $this->createEntry(8, 3), // Histoire-Géographie
            // $this->createEntry(8, 4), // Sciences Physiques
            // $this->createEntry(8, 5), // Chimie
            // $this->createEntry(8, 6), // Sciences Naturelles
            // $this->createEntry(8, 7), // Anglais
            // $this->createEntry(8, 9), // EPS

            // // 3e
            // $this->createEntry(9, 1), // Français
            // $this->createEntry(9, 2), // Mathématiques
            // $this->createEntry(9, 3), // Histoire-Géographie
            // $this->createEntry(9, 4), // Sciences Physiques
            // $this->createEntry(9, 5), // Chimie
            // $this->createEntry(9, 6), // Sciences Naturelles
            // $this->createEntry(9, 7), // Anglais
            // $this->createEntry(9, 9), // EPS

            // // ******************** Lycée *********************
            // // 2nde
            // $this->createEntry(10, 1), // Français
            // $this->createEntry(10, 2), // Mathématiques
            // $this->createEntry(10, 3), // Histoire-Géographie
            // $this->createEntry(10, 4), // Sciences Physiques
            // $this->createEntry(10, 5), // Chimie
            // $this->createEntry(10, 6), // Sciences Naturelles
            // $this->createEntry(10, 7), // Anglais
            // $this->createEntry(10, 8), // Informatique

            // // 1ère
            // $this->createEntry(11, 1), // Français
            // $this->createEntry(11, 2), // Mathématiques
            // $this->createEntry(11, 3), // Histoire-Géographie
            // $this->createEntry(11, 4), // Sciences Physiques
            // $this->createEntry(11, 5), // Chimie
            // $this->createEntry(11, 6), // Sciences Naturelles
            // $this->createEntry(11, 7), // Anglais
            // $this->createEntry(11, 8), // Informatique
            // $this->createEntry(11, 13), // Philosophie

            // // Terminale
            // $this->createEntry(12, 1), // Français
            // $this->createEntry(12, 2), // Mathématiques
            // $this->createEntry(12, 3), // Histoire-Géographie
            // $this->createEntry(12, 4), // Sciences Physiques
            // $this->createEntry(12, 5), // Chimie
            // $this->createEntry(12, 6), // Sciences Naturelles
            // $this->createEntry(12, 7), // Anglais
            // $this->createEntry(12, 8), // Informatique
            // $this->createEntry(12, 13), // Philosophie
        ];

        DB::table('niveaux_matiere')->insert($data);
    }

    
    function createEntry($niveauId, $matiereId) {
        return ['niveau_id' => $niveauId, 'matiere_id' => $matiereId];
    }
}
