import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

extension CapitalizeString on String {
  String capitalize() {
    return isEmpty ? '' : this[0].toUpperCase() + substring(1);
  }
}

extension HexColorExtension on String {
  Color toColor() {
    final hex = replaceAll("#", "");
    if (hex.length == 6) {
      return Color(int.parse("0xFF$hex"));
    } else if (hex.length == 8) {
      return Color(int.parse("0x$hex"));
    }
    return Colors.grey.shade300;
  }
}

extension KeyboardExtensions on BuildContext {
  void hideKeyboard() {
    FocusScope.of(this).unfocus();
  }
}

extension GradientParsing on String? {
  List<Color> toGradient() {
    if (this == null || this!.isEmpty || this == "[]") {
      return [Colors.grey, Colors.grey];
    }
    try {
      final colors = RegExp(r'0xff[0-9A-Fa-f]+')
          .allMatches(this!)
          .map((match) =>
              Color(int.parse(match.group(0)!.replaceAll('0x', '0xFF'))))
          .toList();
      if (colors.length < 2) {
        colors.add(Colors.grey);
      }
      return colors;
    } catch (_) {
      return [Colors.grey, Colors.grey];
    }
  }
}

extension ImageExtensions on String? {
  Widget toImage({
    double? height,
    double? width,
    BoxFit fit = BoxFit.cover,
    Widget? errorWidget,
    BorderRadius? borderRadius,
  }) {
    if (this == null || this!.isEmpty) {
      return errorWidget ??
          Container(
            height: height,
            width: width,
            color: Colors.transparent,
            child: Icon(Icons.broken_image,
                size: height ?? 50.0, color: Colors.grey),
          );
    }
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: CachedNetworkImage(
        imageUrl: this!,
        height: height,
        width: width,
        fit: fit,
        placeholder: (_, __) => Container(
          height: height,
          width: width,
          color: Colors.grey.shade200,
        ),
        fadeOutDuration: const Duration(milliseconds: 300),
        fadeInDuration: const Duration(milliseconds: 300),
        errorWidget: (_, __, ___) =>
            errorWidget ??
            Container(
              height: height,
              width: width,
              color: Colors.transparent,
              child: Icon(Icons.broken_image,
                  size: height ?? 50, color: Colors.grey),
            ),
        errorListener: (dynamic e) {
          if (e is HttpException && e.message.contains('404')) {
            debugPrint('Image not found : 404');
            return;
          }
        },
      ),
    );
  }
}

extension IconParsing on String? {
  Widget toIconText({
    double fontSize = 16.0,
    Color color = Colors.black,
    TextStyle? style,
  }) {
    if (this == null || this!.isEmpty || !this!.startsWith('U+')) {
      return const SizedBox.shrink();
    }

    try {
      final iconCode = int.parse(this!.substring(2), radix: 16);
      return Text(
        String.fromCharCode(iconCode),
        style: style ?? TextStyle(fontSize: fontSize, color: color),
      );
    } catch (_) {
      return const SizedBox.shrink();
    }
  }
}
