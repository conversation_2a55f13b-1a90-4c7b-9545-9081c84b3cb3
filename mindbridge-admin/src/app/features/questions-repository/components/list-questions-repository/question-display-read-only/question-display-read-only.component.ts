import { Component, Input } from '@angular/core';
import { MatRadioButton, MatRadioGroup } from '@angular/material/radio';
import { NgI<PERSON>, Ng<PERSON>ty<PERSON>, SlicePipe } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import {
  Category,
  Matiere,
  Niveaux,
  QuestionResponse,
} from '../../../models/questions-response';
import { MatTooltip } from '@angular/material/tooltip';
import { UnicodeToEmojiPipe } from '../../../../shared/pipes/unicode-to-emoji.pipe';

@Component({
  selector: 'app-question-display-read-only',
  imports: [
    MatRadioButton,
    MatRadioGroup,
    NgIf,
    ReactiveFormsModule,
    SlicePipe,
    MatTooltip,
    NgStyle,
    UnicodeToEmojiPipe,
  ],
  templateUrl: './question-display-read-only.component.html',
  styleUrl: './question-display-read-only.component.scss',
})
export class QuestionDisplayReadOnlyComponent {
  @Input({ required: true }) question: QuestionResponse;
  getTooltipMessageCategories(categories: Category[]) {
    return categories.map((category) => category.name).join(', ');
  }
  getTooltipMessageMatieres(matieres: Matiere[]) {
    return matieres.map((matiere) => matiere.name_fr).join(', ');
  }
  getTooltipMessageNiveaux(niveaux: Niveaux[]) {
    return niveaux.map((niveau) => niveau.name).join(', ');
  }
}
