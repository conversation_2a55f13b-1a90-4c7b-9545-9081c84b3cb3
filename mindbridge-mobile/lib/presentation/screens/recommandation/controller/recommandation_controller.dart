import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/app/routes.dart';
import 'package:mindbridge/core/models/recommandation.dart' as recommandation;
import 'package:mindbridge/core/utilities/app_images.dart';
import 'package:mindbridge/data/repositories/recommandation_repository.dart';

import '../../../../core/constants/app_strings.dart';
import '../../../../core/utilities/loading_utils.dart';
import '../../../../core/utilities/validators.dart';
import '../../../widgets/global_confetti.dart';
import '../../../widgets/global_dialog.dart';
import '../../../widgets/global_loader.dart';

class RecommandationController extends GetxController {
  final RecommandationRepository _recommandationRepository;

  RecommandationController({required RecommandationRepository recommandationRepository})
      : _recommandationRepository = recommandationRepository;

  RxBool isLoading = false.obs;
  RxBool isError = false.obs;
  RxBool isEmptyData = false.obs;
  int currectResponseCount = 0;

  Rx<recommandation.Recommandation?> recommandationData = Rx<recommandation.Recommandation?>(null);

  RxInt currentStep = 1.obs;
  RxMap<int, bool> revealedQuestions = <int, bool>{}.obs;
  RxMap<int, List<int>> selectedAnswers = <int, List<int>>{}.obs;

  List<recommandation.Step> get steps => recommandationData.value?.steps ?? [];
  int get totalSteps => steps.length;

  recommandation.Step? get currentQuestionObj =>
      steps.isNotEmpty && currentStep.value <= steps.length
          ? steps[currentStep.value - 1]
          : null;

  String get currentQuestion {
    final question = currentQuestionObj?.question;
    return question?.content ?? '';
  }

  String get currentQuestionDescription {
    return currentQuestionObj?.question?.description ?? '';
  }

  List<recommandation.Option> get currentChoices {
    final question = currentQuestionObj?.question;
    return question?.options ?? <recommandation.Option>[];
  }

  String get currentQuestionType {
    return currentQuestionObj?.question?.responseType ?? 'one';
  }

  bool isQuestionAnswered() {
    final selected = getSelectedChoices();
    return Validators.validateSelection(selected);
  }

  void selectChoice(int choiceId) {
    if (isRevealedForCurrentQuestion) return;

    final questionId = currentQuestionObj?.questionId ?? -1;
    selectedAnswers.putIfAbsent(questionId, () => []);
    final choices = selectedAnswers[questionId]!;

    if (currentQuestionType == "one") {
      choices.clear();
      choices.add(choiceId);
    } else {
      if (choices.contains(choiceId)) {
        choices.remove(choiceId);
      } else {
        choices.add(choiceId);
      }
    }
    selectedAnswers[questionId] = choices;
  }

  List<int> getSelectedChoices() {
    final questionId = currentQuestionObj?.questionId ?? -1;
    return selectedAnswers[questionId] ?? [];
  }

  bool get isRevealedForCurrentQuestion {
    return revealedQuestions[currentQuestionObj?.questionId ?? -9999] ?? false;
  }

  void revealAndGoNext() {
    if (isRevealedForCurrentQuestion) {
      _goNextImmediately();
    } else {
      _revealWithDelay();
    }
  }

  void _goNextImmediately() {
    goToNextQuestionOrFinish();
  }

  void _revealWithDelay() {
    if (currentQuestionObj?.questionId != null) {
      revealedQuestions[currentQuestionObj!.questionId!] = true;
    }

    Future.delayed(const Duration(milliseconds: 1000), () {
      goToNextQuestionOrFinish();
    });
  }

  void goToNextQuestionOrFinish() async {

    if (currentStep.value < totalSteps) {
      currentStep.value++;
    } else {
      final correctCount = _calculateCorrectAnswers();
      final total = totalSteps;
      final payload = {
        "matiere_id": recommandationData.value?.matiereId,
        'correct_answers_count': correctCount,
      };

      Get.dialog(
        const PopScope(canPop: false, child: Center(child: GlobalLoader())),
        barrierDismissible: false,
      );

      try {
        final response = await _recommandationRepository.submitRecommandationResult(payload);

        Get.back();

        if (response != null && response.containsKey("feedback_message")) {
          final feedbackMessage = response["feedback_message"] ?? "Bien joué";
          GlobalDialog.showTheDialog(
            iconTap: () => Get.offAllNamed(Routes.btmNavigationBar),
            imagePath: AppImages.bigOwl,
            title: feedbackMessage,
            subtitle: 'Vous avez réussi $correctCount/$total réponses',
            buttonText: 'Retour au dashboard',
            onButtonTap: () => Get.offAllNamed(Routes.btmNavigationBar),
            barrierDismissible: false,
          );



          if (correctCount == total) {
            Future.delayed(const Duration(milliseconds: 1000), () {
              _showConfetti();
            });
          }
        } else {
          LoadingUtils.showError(AppStrings.errorMessage);
        }
      } catch (e) {
        Get.back();
        debugPrint("Error submitting evaluation: $e");
        LoadingUtils.showError(AppStrings.errorMessage);
      }
    }
  }

  void _showConfetti() {
    final overlay = Overlay.of(Get.context!);

    final overlayEntry = OverlayEntry(
      builder: (context) {
        return const Stack(
          children: [
            Positioned(
              left: 0,
              child: GlobalConfetti(
                colors: [
                  Colors.blue,
                  Colors.pink,
                  Colors.purple,
                  Colors.green,
                  Colors.orange,
                ],
                alignment: Alignment.centerLeft,
                blastDirection: 0,
                emissionFrequency: 0.05,
                numberOfParticles: 40,
                gravity: 0.8,
                duration: Duration(seconds: 5),
              ),
            ),
            Positioned(
              right: 0,
              child: GlobalConfetti(
                colors: [
                  Colors.blue,
                  Colors.pink,
                  Colors.purple,
                  Colors.green,
                  Colors.orange,
                ],
                alignment: Alignment.centerRight,
                blastDirection: 3.14,
                emissionFrequency: 0.05,
                numberOfParticles: 40,
                gravity: 0.8,
                duration: Duration(seconds: 5),
              ),
            ),
          ],
        );
      },
    );

    overlay.insert(overlayEntry);

    Future.delayed(const Duration(seconds: 6), () {
      overlayEntry.remove();
    });
  }

  int _calculateCorrectAnswers() {
    int count = 0;

    for (final step in steps) {
      final question = step.question;
      if (question == null) continue;

      final userSelected = selectedAnswers[step.questionId] ?? [];

      if (step.question?.responseType == 'true_false') {
        // Handle true/false questions
        final correctAnswerIsTrue = question.answer == true;
        final isTrueSelected = userSelected.contains(1);
        final isFalseSelected = userSelected.contains(0);

        // Check if the selection matches the correct answer
        if ((isTrueSelected && correctAnswerIsTrue) ||
            (isFalseSelected && !correctAnswerIsTrue)) {
          count++;
        }
      } else {
        // Handle single/multiple choice questions
        final correctOptions = question.options
            ?.where((opt) => opt.isCorrect == 1)
            .map((opt) => opt.id)
            .whereType<int>()
            .toList();

        if (correctOptions == null || correctOptions.isEmpty) continue;

        final userSet = userSelected.toSet();
        final correctSet = correctOptions.toSet();

        if (step.question?.responseType == 'one') {
          if (userSelected.isNotEmpty &&
              correctSet.contains(userSelected.first)) {
            count++;
          }
        } else if (step.question?.responseType == 'many') {
          if (userSet.length == correctSet.length &&
              userSet.containsAll(correctSet)) {
            count++;
          }
        }
      }
    }

    return count;
  }

  void previousStep() {
    if (currentStep.value > 1) {
      currentStep.value--;
    }
  }



  Future<void> fetchRecommandationData() async {
    isLoading.value = true;
    isError.value = false;
    isEmptyData.value = false;

    try {
      final response = await _recommandationRepository.fetchRecommandation();
      if (response.isError) {
        isError.value = true;
      } else if (response.isEmpty) {
        GlobalDialog.showTheDialog(
          iconTap: () => Get.offAllNamed(Routes.btmNavigationBar),
          imagePath: AppImages.nothingFound,
          title: "Aucune recommandation disponible!",
          subtitle: '',
          buttonText: AppStrings.continuer,
          onButtonTap: () => Get.offAllNamed(Routes.btmNavigationBar),
          barrierDismissible: true,
        );
        isEmptyData.value = true;
      } else {
        recommandationData.value = response.data;
      }

      debugPrint("Recommandation data: ${recommandationData.value?.steps?.length}");
      
    } catch (e) {
      debugPrint("Error fetching evaluation: $e");
      isError.value = true;
    } finally {
      isLoading.value = false;
    }
  }
}
