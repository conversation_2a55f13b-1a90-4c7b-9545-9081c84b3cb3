import 'package:get/get.dart';
import 'package:mindbridge/app/routes.dart';
import 'package:mindbridge/core/utilities/loading_utils.dart';

import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/user.dart';
import '../../../../data/repositories/auth_repository.dart';

class LoginController extends GetxController {
  final AuthRepository _authRepository;
  var isRememberMeChecked = false.obs;

  LoginController({required AuthRepository authRepository})
      : _authRepository = authRepository;

  void toggleRememberMe() {
    isRememberMeChecked.value = !isRememberMeChecked.value;
  }

  Future<void> login(String email, String password) async {
    LoadingUtils.showLoading(AppStrings.loginInProgress);
    
    try {
      final result = await _authRepository.login(email, password);


      print("Login result: $result");
      if (result['success']) {
        final user = result['user'] as User;
        LoadingUtils.dismiss();

        if (user.etudiant?.testProfilingCompleted == "1") {
          Get.offAllNamed(Routes.btmNavigationBar);
        } else {
          Get.offAllNamed(Routes.completeProfile);
        }
      } else {
        LoadingUtils.showError(result['message']);
      }
    } catch (e) {
      LoadingUtils.showError(AppStrings.errorMessage);
    } finally {
      LoadingUtils.dismiss();
    }
  }

  Future<void> checkLoggedInStatus() async {
    final storedUser = await _authRepository.getStoredUser();

    if (storedUser != null) {
      _navigateBasedOnProfiling(storedUser.etudiant?.testProfilingCompleted);
    } else {
      Get.offAllNamed(Routes.login);
    }
  }

  void _navigateBasedOnProfiling(String? testProfilingCompleted) {
    if (testProfilingCompleted == "1") {
      Get.offAllNamed(Routes.btmNavigationBar);
    } else {
      Get.offAllNamed(Routes.completeProfile);
    }
  }
}
