<?php

namespace App\Http\Controllers\MindBridge;

use App\Http\Controllers\Controller;
use App\Models\MindBridge\Chapter;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class ChaperController extends Controller
{
    public function index(Request $request)
    {
        $chapters = Chapter::with('matiere', 'niveau')->orderBy('created_at', 'desc')->get();
        return response()->json($chapters, 200);
    }

    public function store(Request $request)
    {
        try {
            Log::info("ChapterController::store => Start");
            Log::info($request->all());

            $request->validate([
                'chapter' => 'required:string',
                "description" => 'nullable|string',
                'niveau_id' => 'required|exists:niveaux,id',
                'matiere_id' => 'required|exists:niveaux,id',
            ]);
            return Chapter::create($request->all());
        } catch (ValidationException $e) {
            Log::info("ChapterController::store => ValidationException: {$e}");
            return response()->json($e->validator->errors(), 422);
        } catch (ModelNotFoundException $e) {
            Log::info("ChapterController::store => Error: {$e}");
            return response()->json($e->getMessage(), 0);
        }
    }

    public function byNiveauMatiere(Request $request)
    {
        try {
            Log::info("ChapterController::byNiveauMatiere => Start");
            $niveau = $request->input('niveauId');
            $matiere = $request->input('matiereId');

            Log::info("ChapterController::byNiveauMatiere => Niveau: {$niveau}, Matiere: {$matiere}");

            $query = Chapter::with('matiere', 'niveau');

            if (!is_null($niveau)) {
                $query->where('niveau_id', $niveau);
            }

            if (!is_null($matiere)) {
                $query->where('matiere_id', $matiere);
            }

            $chapters = $query->get();

            if ($chapters->isEmpty()) {
                return response()->json([
                    'message' => 'No results found for the given filters.'
                ], 404);
            }

            return response()->json($chapters, 200);
        } catch (Exception $e) {
            Log::error("ChapterController::byNiveauMatiere => Error: {$e}");
            return response()->json([
                'error' => 'An unexpected error occurred.'
            ], 500);
        }
    }


    public function show($id)
    {
        try {
            $option = Chapter::with('matiere', 'niveau')->findOrFail($id);
            return response()->json($option, 200);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'message' => 'Option not found'
            ], 404);
        } catch (Exception $e) {
            Log::error($e);
            return response()->json([
                'error' => 'An unexpected error occurred.'
            ], 500);
        }
    }

    public function update($id, Request $request)
    {
        try {
            $rules = [
                'chapter' => 'nullable|string',
                'description' => 'nullable|string',
                'niveau_id' => 'nullable|exists:niveaux,id',
                'matiere_id' => 'nullable|exists:niveaux,id',
            ];

            $validatedData = $request->only(array_keys($rules));
            $request->validate(array_intersect_key($rules, $validatedData));

            $chapter = Chapter::findOrFail($id);

            $chapter->update($validatedData);

            return response()->json($chapter, 200);
        } catch (ModelNotFoundException $e) {
            \Log::error("Chapter not found: ID {$id}", ['exception' => $e]);
            return response()->json([
                'error' => 'Chapter not found.',
            ], 404);
        } catch (Exception $e) {
            \Log::error("Unexpected error in ChapterController@update: ", ['exception' => $e]);
            return response()->json([
                'error' => 'An unexpected error occurred.',
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $chapter = Chapter::find($id);
            $chapter->delete();
            return response()->json([
                'message' => 'Chapter deleted successfully'
            ], 204);
        } catch (Exception $e) {
            Log::error($e);
        }
    }
}
