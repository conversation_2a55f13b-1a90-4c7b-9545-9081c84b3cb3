<?php


namespace App\Http\Controllers\MindBridge;

use App\Http\Requests\MindBridge\ImportNotesRequest;
use App\Models\MatiereCentrale;
use App\Models\MindBridge\MindBridgeEtudiant;
use App\Models\MindBridge\MindeBridgeStudentNotes;
use DB;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Str;
use Exception;



class MindBridgeNotesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $notes = MindeBridgeStudentNotes::with('etudiant', 'matiere')->get();
            return response()->json($notes, 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Notes introuvable'], 404);
        }
    }
    
    public function importNotesFromCsv(ImportNotesRequest $request)
    {
        try {
            $file = $request->file('file');
            $content = file_get_contents($file->getRealPath());
            $rows = explode("\n", $content);
            
            $notesData = [];
            $header = [];
            
            foreach ($rows as $rowIndex => $row) {
                $row = trim($row);
                if (empty($row)) {
                    continue;
                }
                
                $columns = str_getcsv($row, ';');
                
                if ($rowIndex === 0) {
                    $header = $columns;
                    $requiredKeys = ['Eleve', 'Matricule', 'Classe', 'Trimestre', 'Matiere', 'Professeur', 'Moyenne', 'Notes', 'Appreciation'];
                    foreach ($requiredKeys as $req) {
                        if (!in_array($req, $header)) {
                            return response()->json(['error' => "Colonne nécessaire absente: $req"], 400);
                        }
                    }
                    continue;
                }
                
                if (count($columns) < count($header)) {
                    continue;
                }
                
                $rowAssoc = array_combine($header, $columns);
                // The CSV is now expected to have the Notes field with comma separated values e.g. "[10,9.5,6.0,8.5]"
                if (isset($rowAssoc['Notes'])) {
                    $rawNotes = trim($rowAssoc['Notes']);
                    if (substr($rawNotes, 0, 1) === '[' && substr($rawNotes, -1) === ']') {
                        $innerNotes = substr($rawNotes, 1, -1);
                        $notesArray = array_map('trim', explode(',', $innerNotes));
                        // Convert numeric strings to numbers if applicable
                        $notesArray = array_map(function ($value) {
                            return is_numeric($value) ? $value + 0 : $value;
                        }, $notesArray);
                        $rowAssoc['Notes'] = $notesArray;
                    }
                }
                
                $notesData[] = $rowAssoc;
            }
            
            $etudiantCache = [];
            $matiereCache = [];
            
            foreach ($notesData as $row) {
                $matricule = $row['Matricule'];
                if (isset($etudiantCache[$matricule])) {
                    $etudiant = $etudiantCache[$matricule];
                } else {
                    $etudiant = MindBridgeEtudiant::where('id', $matricule)->first();
                    if (!$etudiant) {
                        return response()->json(['error' => "Étudiant introuvable: $matricule"], 404);
                    }
                    $etudiantCache[$matricule] = $etudiant;
                }
                
                $matiereName = $row['Matiere'];
                if (isset($matiereCache[$matiereName])) {
                    $matiere = $matiereCache[$matiereName];
                } else {
                    $matiere = MatiereCentrale::where('name_fr', $matiereName)->first();
                    if (!$matiere) {
                        return response()->json(['error' => "Matière introuvable: $matiereName"], 404);
                    }
                    $matiereCache[$matiereName] = $matiere;
                }
                $notes = MindeBridgeStudentNotes::where('etudiant_id', $etudiant->id)
                ->where('matiere_id', $matiere->id)
                ->where('trimestre', $row['Trimestre'])
                ->first();

                if ($notes) {
                    $notes->update([
                        'moyenne'       => $row['Moyenne'],
                        'notes'         => $row['Notes'],
                        'appreciation'  => $row['Appreciation'],
                    ]);
                }else{
                    MindeBridgeStudentNotes::create([
                        'etudiant_id'   => $etudiant->id,
                        'enseignant_id' => null,
                        'matiere_id'    => $matiere->id,
                        'trimestre'     => $row['Trimestre'],
                        'moyenne'       => $row['Moyenne'],
                        'notes'         => $row['Notes'],
                        'appreciation'  => $row['Appreciation'],
                    ]);
                }
            }
            
            return response()->json([
                'message' => 'Notes importées avec succès',
            ], 201);
        } catch (Exception $e) {
            \Log::error("Error in MindBridgeNotesController@importNotesFromCsv: ", ['exception' => $e]);
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }


    
}
