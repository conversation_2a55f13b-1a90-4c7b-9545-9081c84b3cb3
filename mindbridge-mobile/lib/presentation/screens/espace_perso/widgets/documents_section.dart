import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import '../../../../core/constants/app_strings.dart';

class DocumentsSection extends StatelessWidget {
  const DocumentsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Colors.grey.withOpacity(0.2))),
      child: Column(
        children: [
          SizedBox(
            height: 2.h,
          ),
          TabBar(
            indicator: BoxDecoration(
              color: AppColors.flatButtongradient.colors[1],
              borderRadius: BorderRadius.circular(10),
            ),
            labelPadding: EdgeInsets.symmetric(horizontal: 2.w),
            labelColor: AppColors.primary,
            unselectedLabelColor: AppColors.black.withOpacity(0.5),
            dividerHeight: 0,
            tabs: [
              Tab(
                  child: Container(
                padding: EdgeInsets.all(2.w),
                child: Text(
                  AppStrings.myDocuments,
                  style: TextStyle(fontSize: 11.sp),
                ),
              )),
              Tab(
                child: Container(
                  padding: EdgeInsets.all(2.w),
                  child: Text(
                    AppStrings.myRecordings,
                    style: TextStyle(fontSize: 11.sp),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 2.h,
          ),
          SizedBox(
            height: 25.h,
            child: TabBarView(
              children: [
                _DocumentsList(),
                Center(
                  child: Text(
                    AppStrings.noRecordings,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.black.withOpacity(0.5),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _DocumentsList extends StatelessWidget {
  final documents = const [
    {'title': 'Les classes grammaticales.pdf', 'size': '200 KB'},
    {'title': 'Les Fonctions Syntaxiques.pdf', 'size': '200 KB'},
  ];

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const BouncingScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      itemCount: documents.length,
      itemBuilder: (context, index) {
        final doc = documents[index];
        return Card(
          color: Colors.white,
          margin: EdgeInsets.symmetric(vertical: 1.h),
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: AppColors.primary.withOpacity(0.2))),
          elevation: 0,
          child: Padding(
            padding: EdgeInsets.all(2.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Icon(Icons.file_present_rounded,
                          size: 7.w, color: AppColors.secondary),
                      SizedBox(width: 2.w),
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              doc['title']!,
                              style: TextStyle(
                                fontSize: 10.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColors.black,
                              ),
                            ),
                            SizedBox(height: 0.5.h),
                            Text(
                              doc['size']!,
                              style: TextStyle(
                                fontSize: 9.sp,
                                color: AppColors.black.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    _ActionButton(
                      icon: Icons.file_download_outlined,
                      onPressed: () {
                        // Add download logic
                      },
                    ),
                    SizedBox(width: 2.w),
                    _ActionButton(
                      icon: Icons.delete_outlined,
                      onPressed: () {
                        // Add delete logic
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;

  const _ActionButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
            color: AppColors.secondary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8)),
        padding: EdgeInsets.all(1.5.w),
        child: Icon(icon, size: 5.w, color: AppColors.secondary),
      ),
    );
  }
}
