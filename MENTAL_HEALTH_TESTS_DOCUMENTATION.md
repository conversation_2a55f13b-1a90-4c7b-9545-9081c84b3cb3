# Mental Health Tests (Sant<PERSON> Mentale) - Complete Documentation

## Overview
The MindBridge application includes a comprehensive mental health testing system ("Sante Mentale") that allows students to take psychological and cognitive assessments. This document explains how these tests are structured, fetched, managed, and processed across the backend, admin panel, and mobile app.

---

## 1. Database Architecture

### 1.1 Core Models

#### **Test Model** (`mindbridge-backend/app/Models/MindBridge/Test.php`)
The main test entity that stores test metadata:

```
Properties:
- id: Unique identifier
- title: Test name
- description: Test description
- type: Test type (e.g., "sante_mentale", "test_profiling", "sondage")
- category_id: Foreign key to Category (mental health subcategory)
- matiere_id: Subject/Matter ID (optional)
- niveau_id: Student level/grade
- created_by: User who created the test
- timer: Time limit in minutes
- difficulty_level: "facile", "intermediaire", "difficile"
- challenge_date_start/end: Date range for challenge tests
- content_id: Associated content

Relationships:
- creator(): belongsTo(User)
- steps(): hasMany(Step) - Test questions/steps
- category(): belongsTo(Category)
- etudiantTestStatuses(): hasMany(EtudiantTestStatus)
```

#### **Category Model** (`mindbridge-backend/app/Models/MindBridge/Category.php`)
Hierarchical category structure for organizing tests:

```
Properties:
- id: Unique identifier
- parent_id: Parent category (for subcategories)
- name: Category name
- description: Category description
- image_url: Category icon/image
- is_mobile: Visible in mobile app (boolean)
- is_bo: Visible in admin panel (boolean)
- is_active: Active status
- position: Display order
- code: Unique code identifier
- gradient_background: UI styling
- button_text: Button label
- count: Number of tests in category

Relationships:
- subcategories(): hasMany(Category) - Self-referential for nested categories
```

**Category Hierarchy:**
- Category ID 1: Test Models (Modèles de Tests)
- Category ID 2: Mental Health (Sante Mentale) - Parent
  - Subcategories (IDs 3-6): Different mental health test types
    - Cognitive tests (Mémoire, Attention)
    - Emotional tests (Stress, Anxiety)
    - Behavioral tests
    - Social tests

#### **Step Model** (`mindbridge-backend/app/Models/MindBridge/Step.php`)
Individual questions/steps within a test:

```
Properties:
- id: Unique identifier
- test_id: Foreign key to Test
- question_id: Foreign key to Question
- order: Question sequence number
- type: Question type ("one", "multiple", "text", etc.)
- condition: Conditional logic (optional)
- required: Whether answer is mandatory

Relationships:
- test(): belongsTo(Test)
- question(): belongsTo(Question)
```

#### **Question Model** (`mindbridge-backend/app/Models/MindBridge/Question.php`)
Question content and metadata:

```
Properties:
- id: Unique identifier
- type: Question type ("text", "multiple_choice", etc.)
- content: Question text
- description: Additional description
- is_required: Mandatory flag
- image_path: Associated image

Relationships:
- steps(): hasMany(Step)
- options(): hasMany(Option) - Answer choices
```

#### **Option Model**
Answer choices for questions:

```
Properties:
- id: Unique identifier
- question_id: Foreign key to Question
- option_text: Answer text
- is_correct: Correctness flag (for scoring)
```

### 1.2 Student Test Tracking Models

#### **EtudiantTestStatus** (`mindbridge-backend/app/Models/MindBridge/EtudiantTestStatus.php`)
Tracks student test completion status:

```
Properties:
- id: Unique identifier
- etudiant_id: Student ID
- test_id: Test ID
- status: "EN COURS", "TERMINE", "ABANDON"
- score: Total score achieved
- started_at: Test start timestamp
- completed_at: Test completion timestamp

Relationships:
- test(): belongsTo(Test)
- etudiant(): belongsTo(MindBridgeEtudiant)
```

#### **EtudiantTestAnswers** (`mindbridge-backend/app/Models/MindBridge/EtudiantTestAnswers.php`)
Stores individual question answers:

```
Properties:
- id: Unique identifier
- etudiant_id: Student ID
- test_id: Test ID
- question_id: Question ID
- selected_options: JSON array of selected option IDs
- score: Points for this question
- comment: Optional feedback

Relationships:
- test(): belongsTo(Test)
- etudiant(): belongsTo(MindBridgeEtudiant)
- question(): belongsTo(Question)
```

#### **EtudiantTestAssignment** (`mindbridge-backend/app/Models/MindBridge/EtudiantTestAssignment.php`)
Tracks test assignments to students:

```
Properties:
- id: Unique identifier
- etudiant_id: Student ID
- test_id: Test ID
- status: "ATTRIBUE" (assigned), "TERMINE" (completed)
- assigned_at: Assignment timestamp
- completed_at: Completion timestamp

Relationships:
- etudiant(): belongsTo(MindBridgeEtudiant)
- test(): belongsTo(Test)
```

---

## 2. Backend API Endpoints

### 2.1 Admin Panel Endpoints (Protected by Sanctum)

#### **Get Test Categories Board**
```
GET /api/mind_bridge/categories/board
Response:
{
  "test_models": { ... },
  "mental_health": {
    "id": 2,
    "name": "Sante Mentale",
    "enabled": true,
    "children": [
      {
        "id": 3,
        "name": "Cognitive Tests",
        "image_url": "...",
        "count": 5,
        ...
      },
      ...
    ]
  }
}
```

#### **Get Tests by Category**
```
GET /api/mind_bridge/categories/{category_id}/tests
Response:
{
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "Memory Test",
      "description": "...",
      "type": "sante_mentale",
      "category_id": 3,
      "steps": [
        {
          "id": 1,
          "order": 1,
          "question": {
            "id": 1,
            "type": "multiple_choice",
            "content": "Question text",
            "options": [...]
          }
        }
      ]
    }
  ]
}
```

#### **Get Mental Health Test Participants**
```
GET /api/mind_bridge/mental-health-tests/participants
Query Parameters:
- category_id: Filter by category
- niveau: Filter by student level
- etudiant: Search by student name
- page: Pagination

Response:
{
  "data": [
    {
      "id": 1,
      "etudiant": { ... },
      "test_title": "Memory Test",
      "category": { ... },
      "date_creation": "2024-01-15",
      "test_status": "TERMINE",
      "student_score": 85
    }
  ],
  "pagination": { ... }
}
```

#### **Run Mental Health Test**
```
POST /api/mind_bridge/run_test
Body:
{
  "subcategory_id": 3,
  "etudiant_id": 1
}
Response:
{
  "test": { ... },
  "next_difficulty_level": "difficile",
  "restarted": false
}
```

#### **Assign Test to Student**
```
POST /api/mind_bridge/assign-test/{etudiantId}/{testId}
Response:
{
  "message": "Test successfully assigned to the student"
}
```

### 2.2 Mobile App Endpoints

#### **Get Categories Board (Mobile)**
```
GET /api/mind_bridge/etudiant/categories/board?test_id={optional}
Response:
{
  "test_models": { ... },
  "welcome_message": "Welcome, Ahmed!",
  "test_to_do": { ... },
  "sondages_to_do": { ... },
  "mental_health_to_do": { ... }
}
```

#### **Submit Test Results**
```
POST /api/mind_bridge/etudiant/tests
Body:


---

## 14. Code Examples & Implementation Details

### 14.1 Backend - Test Submission Handler

**File:** `mindbridge-backend/app/Http/Controllers/MindBridge/MindBridgeEtudiantController.php`

```php
public function etudiantTestStore(Request $request)
{
    try {
        $user = auth()->user();
        $gainedPoints = 0;

        // Validate request
        $request->validate([
            'test_id' => 'required|integer|exists:mind_bridge.tests,id',
            'data' => 'required|array|min:1',
            'data.*.question_id' => 'required|integer',
            'data.*.selected_options' => 'required|array|min:1',
        ]);

        // Get student
        $etudiant = MindBridgeEtudiant::where('user_id', $user->id)->first();
        $test = Test::findOrFail($request->input('test_id'));

        $totalScore = 0;

        // Process each answer
        foreach ($request->input('data') as $answerData) {
            $questionId = $answerData['question_id'];
            $selectedOptions = $answerData['selected_options'];

            $step = Step::where('question_id', $questionId)->first();

            // Calculate score for this question
            $score = count($selectedOptions);
            $totalScore += $score;

            // Store individual answer
            EtudiantTestAnswers::updateOrCreate(
                [
                    'etudiant_id' => $etudiant->id,
                    'question_id' => $step->question_id,
                ],
                [
                    'test_id' => $test->id,
                    'selected_options' => json_encode($selectedOptions),
                    'score' => $score,
                ]
            );
        }

        // Update or create test status
        $testStatus = EtudiantTestStatus::updateOrCreate(
            [
                'etudiant_id' => $etudiant->id,
                'test_id' => $test->id,
            ],
            [
                'status' => 'TERMINE',
                'score' => $totalScore,
                'started_at' => $testStatus->started_at ?? now(),
                'completed_at' => now(),
            ]
        );

        // Award points and badges
        $badgePointsService = new BadgePointService();
        $gainedPoints += $badgePointsService->processAction('test_completed');

        return response()->json([
            'message' => 'Tests stored successfully.',
            'feedback_message' => "Great job! You earned $gainedPoints points.",
        ]);
    } catch (Exception $e) {
        Log::error('Error storing test: ' . $e->getMessage());
        return response()->json(['error' => 'Failed to store test'], 500);
    }
}
```

### 14.2 Backend - Get Categories Board

**File:** `mindbridge-backend/app/Http/Controllers/MindBridge/MindBridgeTestController.php`

```php
public function testBoardCategories(Request $request)
{
    try {
        // Get all categories with their subcategories
        $categories = Category::with(['subcategories' => function ($query) {
            $query->where('is_bo', true)->with('subcategories');
        }])->get();

        if (count($categories) === 0) {
            return response()->json(['error' => 'Categories is empty'], 404);
        }

        // Return test models and mental health categories
        return response()->json([
            'test_models' => CategoryResource::make($categories->firstWhere('id', 1)),
            'mental_health' => CategoryResource::make($categories->firstWhere('id', 2)),
        ], 200);
    } catch (Exception $exception) {
        Log::error('Error in testBoardCategories: ' . $exception);
        return response()->json(['error' => 'An unexpected error occurred.'], 500);
    }
}

public function testBoardCategoriesMo(Request $request)
{
    try {
        // Get categories for mobile (only is_mobile=true)
        $categories = Category::with(['subcategories' => function ($query) {
            $query->where('is_mobile', true)->orderBy('position', 'asc');
        }])
            ->where('id', 1)
            ->first();

        $testId = $request->input('test_id');
        $user_id = auth()->user()->id;
        $etudiant = MindBridgeEtudiant::where('user_id', $user_id)->first();

        // Get assigned test
        $assignment = EtudiantTestAssignment::where('etudiant_id', $etudiant->id)
            ->where('status', 'ATTRIBUE')
            ->with('test')
            ->first();

        $testToDo = null;
        if ($assignment) {
            $testToDo = Test::with(['content', 'steps', 'steps.question', 'steps.question.options'])
                ->where("id", $assignment->test_id)
                ->first();
        }

        // Get mental health test if test_id provided
        $mentalHealthTest = null;
        if ($testId) {
            $mentalHealthTest = Test::with(['content', 'steps', 'steps.question', 'steps.question.options'])
                ->where("id", $testId)
                ->first();
        }

        return response()->json([
            'test_models' => new CategoryResource($categories),
            'welcome_message' => $welcomeMessage,
            'test_to_do' => $testToDo,
            'mental_health_to_do' => $mentalHealthTest
        ], 200);
    } catch (Exception $exception) {
        Log::error('Error in testBoardCategoriesMo: ' . $exception);
        return response()->json(['error' => 'An unexpected error occurred.'], 500);
    }
}
```

### 14.3 Admin Panel - Service Layer

**File:** `mindbridge-admin/src/app/features/sante-mentale/sante-mentale.service.ts`

```typescript
import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({ providedIn: 'root' })
export class SanteMentaleService {
  private http = inject(HttpClient);

  // Get all tests for a specific category
  getCategoryTests(category_id: number): Observable<CategoryTestsResponse> {
    return this.http.get<CategoryTestsResponse>(
      environment.BASE_URL_API +
        'mind_bridge/categories/' +
        category_id +
        '/tests'
    );
  }

  // Get list of students who participated in mental health tests
  getListeParticipants(
    searchForm: IParticipantSearchForm,
    pagination: Pagination
  ): Observable<ApiResponse<ParticipantResponse>> {
    const params = getQuery(searchForm, pagination);

    return this.http.get<ApiResponse<ParticipantResponse>>(
      environment.BASE_URL_API + 'mind_bridge/mental-health-tests/participants',
      { params }
    );
  }
}
```

### 14.4 Admin Panel - Store (State Management)

**File:** `mindbridge-admin/src/app/features/sante-mentale/sante-mentale.store.ts`

```typescript
import { Injectable, inject } from '@angular/core';
import { signalState, patchState } from '@ngrx/signals';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { Store } from '@ngrx/store';
import { switchMap, map, tap, finalize } from 'rxjs/operators';
import { SanteMentaleService } from './sante-mentale.service';

interface SanteMentaleState {
  categoryTests: CategoryTest[];
  participants: ParticipantResponse[];
  participantsSearchForm: IParticipantSearchForm;
  current_page: number;
  total_pages: number;
}

const initialSanteMentaleState: SanteMentaleState = {
  categoryTests: [],
  participants: [],
  participantsSearchForm: new ParticipantSearchForm(),
  current_page: 1,
  total_pages: 1,
};

@Injectable()
export class SanteMentaleStore {
  store = inject<Store<AppState>>(Store);
  readonly state = signalState<SanteMentaleState>(initialSanteMentaleState);
  #santeMentaleService = inject(SanteMentaleService);

  // Fetch tests for a category
  getCategoryTests = rxMethod<number>(
    pipe(
      switchMap((category_id: number) =>
        this.#santeMentaleService.getCategoryTests(category_id).pipe(
          map((response: any) => response.data),
          tap((categoryTests: CategoryTest[]) => {
            patchState(this.state, { categoryTests });
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        )
      )
    )
  );

  // Fetch participants
  getListeParticipants = rxMethod<{ searchForm: IParticipantSearchForm; pagination: Pagination }>(
    pipe(
      switchMap(({ searchForm, pagination }) =>
        this.#santeMentaleService.getListeParticipants(searchForm, pagination).pipe(
          tap((response: ApiResponse<ParticipantResponse>) => {
            patchState(this.state, {
              participants: response.data,
              current_page: response.pagination.current_page,
              total_pages: response.pagination.total_pages,
            });
          })
        )
      )
    )
  );

  // Update search form
  setSearchForm = (participantsSearchForm: IParticipantSearchForm) =>
    patchState(this.state, { participantsSearchForm });
}
```

### 14.5 Mobile App - Repository

**File:** `mindbridge-mobile/lib/data/repositories/home_repository.dart`

```dart
import '../../../core/constants/api_routes.dart';
import '../../core/models/category.dart';
import '../providers/remote/dio_client.dart';

class HomeRepository {
  final DioClient _dioClient;

  HomeRepository({required DioClient dioClient}) : _dioClient = dioClient;

  // Fetch categories including mental health tests
  Future<CategoryResponse> fetchCategories(String? testID) async {
    try {
      String url = ApiRoutes.categoryInfo;

      // If test_id provided, fetch specific mental health test
      if (testID != null) {
        url += '?test_id=$testID';
      }

      final response = await _dioClient.dio.get(url);

      if (response.statusCode == 200 && response.data != null) {
        final cat = Category.fromJson(response.data);

        final children = cat.testModels?.children ?? [];
        final testToDo = cat.testToDo;
        final sondagesToDo = cat.sondagesToDo;
        final mentalHealthToDo = cat.mentalHealthToDo;

        // Check if any tests available
        final isCategoriesEmpty = children.isEmpty &&
            testToDo == null &&
            sondagesToDo == null &&
            mentalHealthToDo == null &&
            (cat.welcomeMessage == null || cat.welcomeMessage!.isEmpty);

        if (isCategoriesEmpty) {
          return CategoryResponse(isEmpty: true);
        } else {
          return CategoryResponse(data: cat);
        }
      }
    } catch (e) {
      print("Error fetching categories: $e");
      return CategoryResponse(isError: true);
    }
    return CategoryResponse(isError: true);
  }
}
```

### 14.6 Mobile App - Evaluation Repository

**File:** `mindbridge-mobile/lib/data/repositories/evaluation_repository.dart`

```dart
import '../../../core/constants/api_routes.dart';
import '../providers/remote/dio_client.dart';

class EvaluationRepository {
  final DioClient _dioClient;

  EvaluationRepository({required DioClient dioClient}) : _dioClient = dioClient;

  // Submit test answers
  Future<Map<String, dynamic>?> submitEvaluation(
      Map<String, dynamic> payload) async {
    try {
      // Payload structure:
      // {
      //   "test_id": 1,
      //   "data": [
      //     {
      //       "question_id": 1,
      //       "selected_options": [1, 2]
      //     },
      //     ...
      //   ],
      //   "got_easy_question_right": true,
      //   "got_difficult_question_right": false
      // }

      final response =
          await _dioClient.dio.post(ApiRoutes.evaluationSubmit, data: payload);

      if (response.statusCode == 200 && response.data != null) {
        return response.data;
      }
    } catch (e) {
      debugPrint("Error submitting evaluation: $e");
    }
    return null;
  }
}
```

### 14.7 Mobile App - Test Bottom Sheet Widget

**File:** `mindbridge-mobile/lib/presentation/widgets/test_btm_sheet.dart` (Excerpt)

```dart
class _TestBottomSheet extends StatefulWidget {
  final ToDo test;
  final String? type;

  const _TestBottomSheet({required this.test, this.type});

  @override
  State<_TestBottomSheet> createState() => _TestBottomSheetState();
}

class _TestBottomSheetState extends State<_TestBottomSheet> {
  late RxInt currentStepIndex;
  late RxMap<int, List<int>> selectedAnswers;
  late RxBool _showIntro;

  @override
  void initState() {
    super.initState();
    currentStepIndex = 0.obs;
    selectedAnswers = <int, List<int>>{}.obs;
    _showIntro = true.obs;
  }

  // Submit test answers
  Future<void> _submitTest() async {
    try {
      // Build payload
      final payload = {
        'test_id': widget.test.id,
        'data': selectedAnswers.entries.map((entry) {
          return {
            'question_id': entry.key,
            'selected_options': entry.value,
          };
        }).toList(),
        'got_easy_question_right': true,
        'got_difficult_question_right': false,
      };

      // Submit to backend
      final response =
          await Get.find<EvaluationRepository>().submitEvaluation(payload);

      Get.back();

      if (response != null && response.containsKey("feedback_message")) {
        final feedbackMessage =
            response["feedback_message"] ?? AppStrings.successMessage;

        // Show success dialog
        GlobalDialog.showTheDialog(
          imagePath: AppImages.bigOwl,
          title: feedbackMessage,
          buttonText: AppStrings.continuer,
          onButtonTap: () => Get.offAllNamed(Routes.btmNavigationBar),
        );

        // Clear mental health test from storage
        await Get.find<SecureStorageService>().write('has_mental_health_test', '');
      }
    } catch (e) {
      Get.back();
      debugPrint("Error submitting test: $e");
      LoadingUtils.showError(AppStrings.errorMessage);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (_showIntro.value) {
        // Show test intro
        return _buildIntroScreen();
      } else {
        // Show questions
        return _buildQuestionsScreen();
      }
    });
  }
}
```

### 14.8 Database Queries

**Get Mental Health Tests for Student:**

```sql
SELECT t.*
FROM tests t
WHERE t.category_id IN (5, 6, 7, 8)  -- Mental health subcategories
AND t.niveau_id = ?  -- Student level
AND NOT EXISTS (
  SELECT 1 FROM etudiant_test_status ets
  WHERE ets.test_id = t.id
  AND ets.etudiant_id = ?
  AND ets.status = 'TERMINE'
)
ORDER BY t.created_at DESC
```

**Get Student Test Results with Details:**

```sql
SELECT
  ets.id,
  ets.etudiant_id,
  ets.test_id,
  ets.status,
  ets.score,
  ets.started_at,
  ets.completed_at,
  t.title as test_title,
  c.name as category_name,
  e.first_name,
  e.last_name
FROM etudiant_test_status ets
JOIN tests t ON ets.test_id = t.id
JOIN categories c ON t.category_id = c.id
JOIN etudiants e ON ets.etudiant_id = e.id
WHERE t.category_id IN (5, 6, 7, 8)
AND ets.status = 'TERMINE'
ORDER BY ets.completed_at DESC
```

**Get Test with All Questions and Options:**

```sql
SELECT
  t.*,
  s.id as step_id,
  s.order as step_order,
  q.id as question_id,
  q.type as question_type,
  q.content as question_content,
  o.id as option_id,
  o.option_text,
  o.is_correct
FROM tests t
LEFT JOIN steps s ON t.id = s.test_id
LEFT JOIN questions q ON s.question_id = q.id
LEFT JOIN options o ON q.id = o.question_id
WHERE t.id = ?
ORDER BY s.order, o.id
```

---

## 15. API Response Examples

### 15.1 Get Categories Board Response

```json
{
  "test_models": {
    "id": 1,
    "name": "Test Models",
    "enabled": true,
    "children": [
      {
        "id": 3,
        "name": "Test Profiling",
        "enabled": true,
        "image_url": "http://api.example.com/storage/test-profiling.png",
        "icon": "http://api.example.com/storage/test-icon.svg",
        "code": "test_profiling",
        "count": 5,
        "gradient_background": "#FF6B6B",
        "button_text": "Start Test"
      }
    ]
  },
  "mental_health": {
    "id": 2,
    "name": "Sante Mentale",
    "enabled": true,
    "children": [
      {
        "id": 5,
        "name": "Cognitive Tests",
        "enabled": true,
        "image_url": "http://api.example.com/storage/cognitive.png",
        "icon": "http://api.example.com/storage/brain-icon.svg",
        "code": "cognitive",
        "count": 3,
        "gradient_background": "#4ECDC4",
        "button_text": "Take Test",
        "is_mobile": true,
        "is_bo": true,
        "position": 1
      },
      {
        "id": 6,
        "name": "Emotional Tests",
        "enabled": true,
        "image_url": "http://api.example.com/storage/emotional.png",
        "icon": "http://api.example.com/storage/heart-icon.svg",
        "code": "emotional",
        "count": 2,
        "gradient_background": "#FF6B9D",
        "button_text": "Take Test",
        "is_mobile": true,
        "is_bo": true,
        "position": 2
      }
    ]
  }
}
```

### 15.2 Get Tests by Category Response

```json
{
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "Memory & Attention Test",
      "description": "Evaluate your memory and concentration abilities",
      "type": "sante_mentale",
      "category_id": 5,
      "matiere_id": null,
      "niveau_id": 1,
      "created_by": 1,
      "timer": 15,
      "difficulty_level": "intermediaire",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "steps": [
        {
          "id": 1,
          "test_id": 1,
          "question_id": 1,
          "required": 1,
          "type": "one",
          "condition": null,
          "order": 1,
          "created_at": "2024-01-15T10:30:00Z",
          "updated_at": "2024-01-15T10:30:00Z",
          "question": {
            "id": 1,
            "type": "multiple_choice",
            "content": "Do you have difficulty remembering important information?",
            "description": "Choose the answer that best describes you",
            "is_required": 1,
            "image_path": null,
            "options": [
              {
                "id": 1,
                "question_id": 1,
                "option_text": "Yes, often",
                "is_correct": false
              },
              {
                "id": 2,
                "question_id": 1,
                "option_text": "Sometimes",
                "is_correct": false
              },
              {
                "id": 3,
                "question_id": 1,
                "option_text": "Rarely",
                "is_correct": true
              }
            ]
          }
        }
      ]
    }
  ]
}
```

### 15.3 Submit Test Response

```json
{
  "message": "Tests stored successfully.",
  "feedback_message": "Excellent work! You scored 85/100. You earned 50 points!"
}
```

### 15.4 Get Participants Response

```json
{
  "data": [
    {
      "id": 1,
      "etudiant": {
        "id": 1,
        "first_name": "Ahmed",
        "last_name": "Hassan",
        "email": "<EMAIL>",
        "niveau": {
          "id": 1,
          "name": "1ère Année"
        },
        "school": {
          "id": 1,
          "name": "Lycée Central"
        }
      },
      "test_title": "Memory & Attention Test",
      "category": {
        "id": 5,
        "name": "Cognitive Tests",
        "description": "Tests for cognitive abilities"
      },
      "date_creation": "2024-01-15",
      "comment": null,
      "test_status": "TERMINE",
      "student_score": 85
    },
    {
      "id": 2,
      "etudiant": {
        "id": 2,
        "first_name": "Fatima",
        "last_name": "Ali",
        "email": "<EMAIL>",
        "niveau": {
          "id": 1,
          "name": "1ère Année"
        },
        "school": {
          "id": 1,
          "name": "Lycée Central"
        }
      },
      "test_title": "Stress Level Assessment",
      "category": {
        "id": 6,
        "name": "Emotional Tests",
        "description": "Tests for emotional well-being"
      },
      "date_creation": "2024-01-16",
      "comment": null,
      "test_status": "TERMINE",
      "student_score": 72
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 5,
    "per_page": 10,
    "total": 45
  }
}
```

---

## 16. Troubleshooting & Common Issues

### Issue 1: Mental Health Test Not Appearing in Mobile App
**Cause:** Test category not marked as `is_mobile = true`
**Solution:** Update category in database:
```sql
UPDATE categories SET is_mobile = 1 WHERE id = 5;
```

### Issue 2: Student Can't Submit Test
**Cause:** Missing or invalid test_id in request
**Solution:** Verify test exists and is assigned to student:
```sql
SELECT * FROM tests WHERE id = ? AND category_id IN (5,6,7,8);
SELECT * FROM etudiant_test_assignments WHERE etudiant_id = ? AND test_id = ?;
```

### Issue 3: Score Not Calculated Correctly
**Cause:** Incorrect scoring logic in backend
**Solution:** Check `etudiantTestStore` method - ensure score calculation matches business logic

### Issue 4: Admin Can't See Mental Health Tests
**Cause:** Category not marked as `is_bo = true`
**Solution:** Update category:
```sql
UPDATE categories SET is_bo = 1 WHERE id = 2;
```

---

## 17. Performance Optimization Tips

1. **Cache Category Data:** Categories rarely change, cache for 1 hour
2. **Lazy Load Test Steps:** Load steps only when test is selected
3. **Paginate Participants:** Always paginate participant results
4. **Index Database:** Add indexes on `category_id`, `etudiant_id`, `test_id`
5. **Use Eager Loading:** Load relationships with tests to avoid N+1 queries

---

## 18. Security Considerations

1. **Authentication:** All endpoints require Sanctum token
2. **Authorization:** Students can only see their own tests
3. **Input Validation:** Validate all test submissions
4. **SQL Injection:** Use parameterized queries (Laravel ORM handles this)
5. **CORS:** Configure CORS for mobile and admin domains
6. **Rate Limiting:** Implement rate limiting on test submission endpoint

---

## 19. Testing Mental Health Tests

### Unit Test Example (Laravel)

```php
public function test_student_can_submit_mental_health_test()
{
    $student = MindBridgeEtudiant::factory()->create();
    $test = Test::factory()->create(['category_id' => 5]);
    $question = Question::factory()->create();
    $step = Step::factory()->create(['test_id' => $test->id, 'question_id' => $question->id]);
    $option = Option::factory()->create(['question_id' => $question->id]);

    $response = $this->actingAs($student->user)
        ->postJson('/api/mind_bridge/etudiant/tests', [
            'test_id' => $test->id,
            'data' => [
                [
                    'question_id' => $question->id,
                    'selected_options' => [$option->id],
                ]
            ]
        ]);

    $response->assertStatus(200);
    $this->assertDatabaseHas('etudiant_test_status', [
        'etudiant_id' => $student->id,
        'test_id' => $test->id,
        'status' => 'TERMINE',
    ]);
}
```

---

## 20. Future Roadmap

- [ ] AI-powered test recommendations based on student profile
- [ ] Adaptive difficulty that adjusts based on performance
- [ ] Detailed analytics dashboard with charts and insights
- [ ] Integration with counseling services for at-risk students
- [ ] Peer comparison (anonymized) for motivation
- [ ] Mobile offline support for test taking
- [ ] Multi-language support for tests
- [ ] Video explanations for test results
- [ ] Integration with learning management system (LMS)
- [ ] Automated alerts for concerning test results

{
  "test_id": 1,
  "data": [
    {
      "question_id": 1,
      "selected_options": [1, 2]
    },
    {
      "question_id": 2,
      "selected_options": [3]
    }
  ],
  "got_easy_question_right": true,
  "got_difficult_question_right": false
}
Response:
{
  "message": "Tests stored successfully.",
  "feedback_message": "Great job! You earned 50 points."
}
```

---

## 3. Admin Panel Implementation (Angular)

### 3.1 Service Layer

**SanteMentaleService** (`mindbridge-admin/src/app/features/sante-mentale/sante-mentale.service.ts`)

```typescript
@Injectable({ providedIn: 'root' })
export class SanteMentaleService {
  getCategoryTests(category_id: number): Observable<CategoryTestsResponse>
  getListeParticipants(searchForm, pagination): Observable<ApiResponse<ParticipantResponse>>
}
```

### 3.2 State Management

**SanteMentaleStore** (`mindbridge-admin/src/app/features/sante-mentale/sante-mentale.store.ts`)

Uses Angular Signals for reactive state:
```typescript
state = {
  categoryTests: Signal<CategoryTest[]>
  participants: Signal<ParticipantResponse[]>
  participantsSearchForm: Signal<IParticipantSearchForm>
  current_page: Signal<number>
  total_pages: Signal<number>
}

Methods:
- getCategoryTests(category_id): Fetches tests for a category
- getListeParticipants(): Fetches student test results
- setSearchForm(): Updates search filters
```

### 3.3 Components

#### **TestCategoriesComponent**
- Displays mental health test categories
- Shows test count per category
- Allows category selection

#### **CategoryTestsComponent**
- Lists all tests in selected category
- Shows test details
- Allows test creation/editing

#### **TestsSanteMentaleComponent**
- Displays student participation data
- Filters by category, level, student name
- Shows test results and scores

---

## 4. Mobile App Implementation (Flutter)

### 4.1 Models

**Category Model** (`mindbridge-mobile/lib/core/models/category.dart`)

```dart
class Category {
  TestModels? testModels;
  String? welcomeMessage;
  ToDo? testToDo;
  ToDo? mentalHealthToDo;
  ToDo? sondagesToDo;
}

class ToDo {
  int? id;
  String? title;
  String? description;
  int? timer;
  String? type;
  List<Step>? steps;
}

class Step {
  int? id;
  int? order;
  Question? question;
}

class Question {
  int? id;
  String? type;
  String? content;
  List<Option>? options;
}

class Option {
  int? id;
  String? optionText;
  bool? isCorrect;
}
```

### 4.2 Repositories

**HomeRepository** (`mindbridge-mobile/lib/data/repositories/home_repository.dart`)

```dart
Future<CategoryResponse> fetchCategories(String? testID)
// Fetches categories including mental health tests
// Returns: Category object with mentalHealthToDo
```

**EvaluationRepository** (`mindbridge-mobile/lib/data/repositories/evaluation_repository.dart`)

```dart
Future<Map<String, dynamic>?> submitEvaluation(Map<String, dynamic> payload)
// Submits test answers to backend
// Payload includes: test_id, question answers, points earned
```

### 4.3 UI Components

#### **TestBtmSheet Widget** (`mindbridge-mobile/lib/presentation/widgets/test_btm_sheet.dart`)

Displays mental health test in bottom sheet:
- Shows test title and description
- Displays test questions step-by-step
- Handles answer selection
- Submits results to backend
- Shows feedback message with points earned

**Key Features:**
- Intro screen with test description
- Question display with options
- Progress tracking
- Result submission with feedback
- Stores completion status in secure storage

---

## 5. Test Flow Diagram

### Admin Panel Flow
```
1. Admin logs in
2. Navigates to "Sante Mentale" section
3. Views mental health test categories
4. Selects a category (e.g., "Cognitive Tests")
5. Views all tests in that category
6. Can:
   - View test details
   - Edit test
   - Create new test
   - Assign test to students
7. Views student participation data
8. Filters by category, level, student name
9. Sees test results and scores
```

### Mobile App Flow
```
1. Student logs in
2. Home screen fetches categories (including mental health)
3. Mental health test appears in "mentalHealthToDo"
4. Student taps on mental health test
5. TestBtmSheet opens showing:
   - Test intro/description
   - "Start Test" button
6. Student answers questions
7. Submits answers
8. Backend processes:
   - Creates EtudiantTestStatus (TERMINE)
   - Creates EtudiantTestAnswers for each question
   - Calculates score
   - Awards points/badges
9. Shows feedback message with points
10. Clears mental health test from home screen
```

---

## 6. Key Features

### 6.1 Test Management
- Create tests with multiple questions
- Set difficulty levels
- Configure timers
- Organize by categories/subcategories
- Assign tests to specific students

### 6.2 Student Tracking
- Track test completion status
- Store individual answers
- Calculate scores
- Award points and badges
- View participation history

### 6.3 Admin Dashboard
- View all mental health tests
- Monitor student participation
- Filter by category, level, student
- Export participation data
- Manage test assignments

### 6.4 Mobile Experience
- Seamless test taking
- Progress tracking
- Immediate feedback
- Points/rewards system
- Secure storage of completion status

---

## 7. Test Types in MindBridge

The system supports multiple test types:

1. **Test Profiling** (category_id: 1)
   - Initial student assessment
   - Determines student level

2. **Mental Health Tests** (category_id: 2)
   - Cognitive tests (Memory, Attention)
   - Emotional tests (Stress, Anxiety)
   - Behavioral tests
   - Social tests

3. **Sondage** (category_id: 7)
   - Survey/feedback tests
   - One-time per student

4. **Challenge Hebdomadaire** (category_id: 8)
   - Weekly challenges
   - Time-limited
   - Difficulty progression

---

## 8. Database Queries

### Get Mental Health Tests for Student
```sql
SELECT t.* FROM tests t
WHERE t.category_id IN (3, 4, 5, 6)  -- Mental health subcategories
AND t.niveau_id = ?  -- Student level
AND NOT EXISTS (
  SELECT 1 FROM etudiant_test_status ets
  WHERE ets.test_id = t.id
  AND ets.etudiant_id = ?
  AND ets.status = 'TERMINE'
)
```

### Get Student Test Results
```sql
SELECT ets.*, t.title, c.name as category_name
FROM etudiant_test_status ets
JOIN tests t ON ets.test_id = t.id
JOIN categories c ON t.category_id = c.id
WHERE ets.etudiant_id = ?
AND t.category_id IN (3, 4, 5, 6)
ORDER BY ets.completed_at DESC
```

---

## 9. Scoring System

- Each question can have multiple correct options
- Score = number of correctly selected options
- Total test score = sum of all question scores
- Points awarded based on:
  - Test completion
  - Difficulty level
  - Correct answers
  - Badge achievements

---

## 10. Security & Validation

- All endpoints require Sanctum authentication
- Student can only see their own tests
- Admin can manage all tests
- Input validation on test submission
- Soft deletes for test answers
- Foreign key constraints for data integrity

---

## 11. Caching

- Welcome messages cached for 6 minutes per user
- Reduces database queries
- Improves performance

---

## 12. Notifications

- FCM notifications sent when test assigned
- Notification type: "mental_health_test"
- Includes test_id for deep linking

---

## 13. Future Enhancements

- AI-powered test recommendations
- Adaptive difficulty based on performance
- Detailed analytics and reporting
- Test result explanations
- Peer comparison (anonymized)
- Integration with counseling services

