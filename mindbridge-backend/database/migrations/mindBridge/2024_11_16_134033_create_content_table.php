<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contents', function (Blueprint $table) {
            $table->id();
            $table->string('type')->nullable()->default('pdf');
            $table->string('title');
            $table->text('content')->nullable();
            $table->text('description')->nullable();
            $table->foreignId('chapter_id')->nullable()->constrained('chapters')->onDelete('cascade');

            $table->unsignedBigInteger('niveau_id')->nullable();
            $table->foreign('niveau_id')
                  ->references('id')
                  ->on(config('database.connections.centrale.database') . '.niveaux')
                  ->onDelete('cascade');

            $table->unsignedBigInteger('matiere_id')->nullable();
            $table->foreign('matiere_id')
                  ->references('id')
                  ->on(config('database.connections.centrale.database') . '.matieres')
                  ->onDelete('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contents');
    }
};
