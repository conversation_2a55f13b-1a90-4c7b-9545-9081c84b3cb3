import 'package:flutter/material.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';

class LessonCard extends StatelessWidget {
  final String lessonTitle;
  final VoidCallback? onTap;

  const LessonCard({
    super.key,
    required this.lessonTitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 2.h),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [
              Color(0xFFF9E8F3),
              Color(0xFFF2EEF4),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomRight,
          ),
          border: const GradientBoxBorder(
            gradient: LinearGradient(
              colors: [
                Color(0xFFF2D1E7),
                Color(0xFFE5DEE8),
              ],
            ),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Text(
                lessonTitle,
                style: TextStyle(
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const Icon(
              Icons.chevron_right,
              color: AppColors.secondary,
            ),
          ],
        ),
      ),
    );
  }
}
