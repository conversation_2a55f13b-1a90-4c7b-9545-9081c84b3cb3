import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorageService {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  Future<void> write(String key, String value) async {
    try {
      debugPrint("Writing key: $key with value: $value");
      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      debugPrint("Error writing $key: $e");
    }
  }

  Future<String?> read(String key) async {
    try {
      debugPrint("Reading key: $key");
      final value = await _secureStorage.read(key: key);
      debugPrint("Value for $key: $value");
      return value;
    } catch (e) {
      debugPrint("Error reading $key: $e");
      return null;
    }
  }

  Future<void> delete(String key) async {
    try {
      debugPrint("Deleting key: $key");
      await _secureStorage.delete(key: key);
    } catch (e) {
      debugPrint("Error deleting $key: $e");
    }
  }

  Future<void> clear() async {
    try {
      debugPrint("Clearing all secure storage");
      await _secureStorage.deleteAll();
    } catch (e) {
      debugPrint("Error clearing storage: $e");
    }
  }
}
