<?php

namespace Database\Seeders\mindBridge;

use Illuminate\Database\Seeder;
use App\Models\MindBridge\Test;
use App\Models\MindBridge\Question;

class StepsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'test' => [
                    'title' => 'Profiling Test',
                    'description' => 'Profiling Test',
                    'type' => 'test_profiling',
                    'category_id' => 3,
                    'created_by' => 1,
                ],
                'steps' => [
                    [
                        'order' => 1,
                        'type'=> 'one',
                        'condition' => null,
                        'question' => [
                            'type' => 'text',
                            'content' => 'Comment apprends-tu le mieux ?',
                            'description' => 'Choisis une option qui te correspond le plus',
                            'options' => [
                                ['name' => "J'aime voir des images ou des vidéos. Les graphiques, les schémas, et les vidéos me parlent !", 'icon' => 'U+1F4DA'],
                                ['name' => "J'apprends mieux en écoutant. J'aime écouter des explications ou des podcasts pour mieux comprendre.", 'icon' => 'U+1F3A7'],
                                ['name' => "J'apprends en bougeant ou en faisant des activités. J'aime manipuler des objets ou participer à des expériences concrètes.", 'icon' => 'U+1F3AE'],
                            ],
                        ],
                    ],
                    [
                        'order' => 2,
                        'type'=> 'many',
                        'condition' => null,
                        'question' => [
                            'type' => 'text',
                            'content' => "Qu'est-ce qui te motive le plus à apprendre ?",
                            'description' => 'Clique sur ce qui te donne envie de réussir',
                            'options' => [
                                ['name' => "Je suis curieux et j'adore apprendre des nouvelles choses.", 'icon' => 'U+2B50'],
                                ['name' => "Je veux avoir de bonnes notes et être le meilleur !"],
                                ['name' => "J'aime relever des défis et gagner des récompenses"],
                                ['name' => "J'aime quand on me félicite pour mes efforts"],
                            ],
                        ],
                    ],
                    [
                        'order' => 3,
                        'type'=> 'many',
                        'condition' => null,
                        'question' => [
                            'type' => 'text',
                            'content' => "Comment préfères-tu travailler ?",
                            'description' => 'Sélectionne une ou plusieurs options',
                            'options' => [
                                ['name' => "Avec mes camarades. J’aime les projets en équipe et discuter avec les autres"],
                                ['name' => "Seul et tranquille. Je préfère travailler à mon rythme, sans distractions."],
                                ['name' => "Avec des outils interactifs. J’aime quand les leçons sont interactives et que je peux participer activement (quiz, jeux, vidéos, etc.)"],
                            ],
                        ],
                    ],
                    [
                        'order' => 4,
                        'type'=> 'one',
                        'condition' => null,
                        'question' => [
                            'type' => 'text',
                            'content' => 'Quand tu révises, comment te sens-tu ?',
                            'description' => 'Choisis ce qui te ressemble le plus',
                            'options' => [
                                ['name' => "Je gère bien, pas de stress. J’arrive à bien organiser mon temps et à rester concentré."],
                                ['name' => "Je suis un peu stressé. J’ai du mal à organiser mes révisions et je stresse pour les examens."],
                                ['name' => 'Je suis souvent stressé**. J’ai besoin d’aide pour gérer mes émotions et mes révisions.'],
                            ],
                        ],
                    ],
                    [
                        'order' => 5,
                        "type"=> "many",
                        'condition' => null,
                        'question' => [
                            'type' => 'text',
                            'content' => "Quand tu es en classe, tu te concentres...",
                            'description' => "Clique sur ta situation",
                            'options' => [
                                ['name' => "Je me concentre facilement et reste attentif"],
                                ['name' => "Je me concentre parfois, mais je me laisse vite distraire"],
                                ['name' => "J'ai du mal à rester concentré longtemps, je m'ennuie vite"],
                            ],
                        ],
                    ],
                    [
                        'order' => 6,
                        "type"=> "many",
                        'condition' => null,
                        'question' => [
                            'type' => 'text',
                            'content' => "Y a-t-il des matières où tu as des difficultés ?",
                            'description' => "Coche la ou les matières où tu as besoin de plus d'aide",
                            'options' => [
                                ['name' => "Mathématiques"],
                                ['name' => "SVT"],
                                ['name' => "Français"],
                                ['name' => "Histoire - Géographie"],
                                ['name' => "Langues étrangères (anglais, espagnol, etc.)"],
                                ['name' => "Autre"],
                            ],
                        ],
                    ],
                    [
                        'order' => 7,
                        "type"=> "one",
                        'condition' => null,
                        'question' => [
                            'type' => 'text',
                            'content' => "Si tu devais apprendre un nouveau sujet, tu préfèrerais...",
                            'description' => "Choisis ce qui te motive le plus",
                            'options' => [
                                ['name' => "Découvrir seul, à mon rythme"],
                                ['name' => "Faire des recherches avec mes camarades"],
                                ['name' => "Jouer à des jeux ou faire des activités interactives pour comprendre"],
                            ],
                        ],
                    ],
                    [
                        'order' => 8,
                        "type"=> "one",
                        'condition' => null,
                        'question' => [
                            'type' => 'text',
                            'content' => "À quel point te sens-tu en confiance pour cette année scolaire ?",
                            'description' => "Sélectionne la phrase qui te correspond",
                            'options' => [
                                ['name' => "Je suis super confiant"],
                                ['name' => "Je suis un peu inquiet"],
                                ['name' => "Je me sens un peu perdu"],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        foreach ($data as $testData) {
            $test = Test::create($testData['test']);
            foreach ($testData['steps'] as $stepData) {
                $questionData = $stepData['question'];
                $options = $questionData['options'];
                unset($questionData['options']);
                
                $question = Question::create($questionData);
                foreach ($options as $optionData) {
                    $question->options()->create($optionData);
                }

                $stepData['question_id'] = $question->id;
                unset($stepData['question']);
                $test->steps()->create($stepData);
            }
        }
    }
}
