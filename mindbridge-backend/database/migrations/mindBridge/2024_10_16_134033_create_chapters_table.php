<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chapters', function (Blueprint $table) {
            $table->id();
            $table->string('chapter');
            $table->text('description')->nullable();
            $table->unsignedBigInteger('matiere_id')->nullable();

            $table->foreign('matiere_id')
                  ->references('id')
                  ->on(config('database.connections.centrale.database') . '.matieres')
                  ->onDelete('cascade');
                  
            $table->unsignedBigInteger('niveau_id')->nullable();
            $table->foreign('niveau_id')
                ->references('id')
                ->on(config('database.connections.centrale.database') . '.niveaux')
                ->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chapters');
    }
};
