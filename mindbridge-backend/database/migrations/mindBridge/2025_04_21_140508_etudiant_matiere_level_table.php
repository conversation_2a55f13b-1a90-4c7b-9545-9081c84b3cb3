<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('etudiant_matiere_level', function (Blueprint $table) {
            $table->id();
            $table->foreignId('etudiant_id')
                ->nullable()
                ->constrained('etudiants')
                ->cascadeOnDelete();

            $table->foreignId('matiere_id')
                ->nullable()
                ->constrained(config('database.connections.centrale.database') . '.matieres')
                ->cascadeOnDelete();

            $table->enum('level', ['faible', 'moyen', 'fort'])->nullable()->default('faible');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('etudiant_matiere_level');
    }
};
