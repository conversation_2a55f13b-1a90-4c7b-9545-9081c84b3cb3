import {
  Component,
  computed,
  effect,
  inject,
  OnC<PERSON><PERSON>,
  OnInit,
  Signal,
  untracked,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { AppState } from '../../../../core/app.state';
import { Store } from '@ngrx/store';
import { SetLoading } from '../../../shared/store/shared.actions';
import * as questionsRepositoryActions from '../../store/questions-repository.actions';
import { tap } from 'rxjs';
import { Actions, ofType } from '@ngrx/effects';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { QuestionResponse } from '../../models/questions-response';
import { QuestionsStore } from '../../questions.store';
import {
  IQuestionSearchForm,
  IQuestionSearchFormGroup,
  QuestionSearchForm,
  QuestionSearchFormGroup,
} from '../../models/question-search-request';
import { OPTION_ALL } from '../../../shared/global.config';
import {
  MatiereResponse,
  NiveauMini,
} from '../../../matieres/models/matiere-response.model';
import { MatieresStore } from '../../../matieres/matieres.store';
import { TestManagementStore } from '../../../test-management/test-management.store';
import { IChildren } from '../../../test-management/models/test-categories-board';
import { QuestionDisplayReadOnlyComponent } from './question-display-read-only/question-display-read-only.component';

@Component({
  selector: 'app-list-questions-repository',
  templateUrl: './list-questions-repository.component.html',
  styleUrl: './list-questions-repository.component.scss',
  standalone: true,
  imports: [
    NgIf,
    ReactiveFormsModule,
    MatSlideToggle,
    NgFor,
    QuestionDisplayReadOnlyComponent,
  ],
  providers: [QuestionsStore, MatieresStore, TestManagementStore],
})
export class ListQuestionsRepositoryComponent implements OnInit, OnChanges {
  private fb = inject(FormBuilder);
  private store = inject<Store<AppState>>(Store);
  private actions = inject(Actions);

  form: FormGroup = new FormGroup({});
  checked = false;
  toggleAddQuestion = false;
  requiredInput = false;
  isUpdateQuestionId = null;
  // questionsRepository$: Observable<any> = this.store.select(
  //   (state) => state.questionsRepository.questionsRepository
  // );
  questionsRepository: any = [];

  #questionsStore = inject(QuestionsStore);

  searchForm: FormGroup<IQuestionSearchFormGroup> = this.fb.group(
    new QuestionSearchFormGroup()
  );

  #matieresStore = inject(MatieresStore);

  matieres: Signal<MatiereResponse[]> = this.#matieresStore.state.matieres;

  allNiveau: Signal<NiveauMini[]> = this.#matieresStore.state.niveaux;

  #testManagementStore = inject(TestManagementStore);
  testCategories: Signal<IChildren[]> = computed(() => {
    return [
      ...this.#testManagementStore.state.testCategoriesBoard().test_models
        .children,
      ...this.#testManagementStore.state.testCategoriesBoard().mental_health
        .children,
    ];
  });

  questionsSearchForm: Signal<IQuestionSearchForm> =
    this.#questionsStore.state.questionsSearchForm;

  questions: Signal<QuestionResponse[]> = this.#questionsStore.state.questions;
  current_page: Signal<number> = this.#questionsStore.state.current_page;
  total_pages: Signal<number> = computed(() =>
    Math.max(this.#questionsStore.state.total_pages(), 1)
  );

  filters = false;

  constructor() {
    effect(() => {
      const searchFormValue = this.questionsSearchForm();
      untracked(() => this.searchForm.patchValue(searchFormValue));
    });
    this.initForm();
  }

  ngOnInit(): void {
    /*this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.store.dispatch(questionsRepositoryActions.AllQuestionsRepository());

    this.questionsRepository$.subscribe((questionsRepository: any) => {
      this.questionsRepository = questionsRepository?.data;
    });*/

    this.store.dispatch(SetLoading({ isAppLoading: true }));

    this.searchQuestions();

    this.#matieresStore.getMatieres(null);
    this.#matieresStore.getAllNiveaux();

    this.#testManagementStore.getTestCategoriesBoard();

    this.actions
      .pipe(
        ofType(
          questionsRepositoryActions.AddQuestionRepositorySuccess,
          questionsRepositoryActions.UpdateQuestionRepositorySuccess,
          questionsRepositoryActions.DeleteQuestionRepositorySuccess
        ),
        tap(() => {
          this.toggleAddQuestion = false;
        })
      )
      .subscribe();
  }

  ngOnChanges() {
    this.searchQuestions();
  }

  searchQuestions() {
    this.#questionsStore.setSearchForm(this.searchForm.getRawValue());
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.#questionsStore.searchQuestions();
  }

  initForm(question?: any) {
    if (!question) {
      this.form = this.fb.group({
        type: new FormControl('text', Validators.required),
        description: new FormControl(null),
        content: new FormControl(null, Validators.required),
        image_path: new FormControl(null),
        options: this.fb.array(
          [
            this.fb.group({
              name: new FormControl(null, Validators.required),
              isCorrect: new FormControl(false),
            }),
          ],
          Validators.required
        ),
      });

      return;
    }
    this.form = this.fb.group({
      type: new FormControl(question?.type || 'text', Validators.required),
      description: new FormControl(question?.description || null),
      content: new FormControl(question?.content || null, Validators.required),
      image_path: new FormControl(question?.image_path || null),
      options: this.fb.array(
        question?.options?.map((option: any) =>
          this.fb.group({
            name: new FormControl(option?.name || null, Validators.required),
            isCorrect: new FormControl(option?.isCorrect || false),
          })
        ),
        Validators.required
      ),
    });
  }

  get options() {
    return this.form.get('options') as FormArray;
  }

  addOption(): void {
    const lastOption = this.options.at(this.options.length - 1) as FormGroup;

    // Ensure `name` is not null or empty before adding a new option
    if (lastOption && lastOption.get('name')?.value?.trim()) {
      this.options.push(
        this.fb.group({
          name: new FormControl(null), // Add a new FormGroup for the next option
          isCorrect: new FormControl(false),
        })
      );
    } else {
      console.warn('Complete the current option before adding a new one.');
    }
  }

  deleteOption(index: number): void {
    // Ensure the index is within bounds and remove the option from the FormArray
    if (this.options.length > 1) {
      this.options.removeAt(index);
    } else {
      console.warn('At least one option must remain.');
    }
  }

  validateForm() {
    if (this.form.valid) {
      this.save();
    } else {
      console.error('Form is invalid');
      this.requiredInput = true;
    }
  }

  save() {
    if (!this.form.valid) {
      this.form.markAllAsTouched();
      return;
    } else {
      if (this.form.valid && this.isUpdateQuestionId) {
        this.store.dispatch(SetLoading({ isAppLoading: true }));
        this.store.dispatch(
          questionsRepositoryActions.UpdateQuestionRepository({
            payload: this.form.value,
            id: this.isUpdateQuestionId,
          })
        );
        return;
      }
      this.store.dispatch(SetLoading({ isAppLoading: true }));
      this.store.dispatch(
        questionsRepositoryActions.AddQuestionRepository({
          payload: this.form.value,
        })
      );
    }
    this.toggleAddQuestion = false;
  }

  addQuestion() {
    this.toggleAddQuestion = !this.toggleAddQuestion;
    this.isUpdateQuestionId = null;

    this.initForm();
  }

  updateQuestion(question: any) {
    // Use find to locate the question by its id
    const matchedQuestion = this.questionsRepository.find(
      (q: any) => q.id === question.id
    );

    if (matchedQuestion) {
      this.toggleAddQuestion = true;
      this.initForm(question);

      // Enable update flag
      this.isUpdateQuestionId = question.id;
    } else {
      // Optional: Handle case where no question is found
      console.error('No question found with the given ID.');
      this.isUpdateQuestionId = null;
    }
  }

  deleteQuestion(id: any) {
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.store.dispatch(
      questionsRepositoryActions.DeleteQuestionRepository({ id })
    );
  }
  censel() {
    this.toggleAddQuestion = false;
    this.isUpdateQuestionId = null;
    this.initForm();
  }

  protected readonly OPTION_ALL = OPTION_ALL;

  goToPage(page: number) {
    this.#questionsStore.setCurrentPage(page);
    this.searchQuestions();
  }

  filterAppeared() {
    this.filters = !this.filters;
  }

  protected readonly Number = Number;

  initiateSearchQuestions() {
    const searchFormValue = new QuestionSearchForm();
    this.#questionsStore.setSearchForm(searchFormValue);
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.#questionsStore.searchQuestions();
  }

  onImageSelected(event: any): void {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      alert('File size must be less than 10MB');
      return;
    }

    // Create FormData for upload
    const formData = new FormData();
    formData.append('image', file);

    // Upload image via store/service
    this.#questionsStore.uploadImage(formData);
  }

  onImageUrlPasted(url: string): void {
    if (!url) return;

    // Validate URL ends with image extension
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif'];
    const lowerUrl = url.toLowerCase();
    const hasValidExtension = imageExtensions.some(ext => lowerUrl.endsWith(ext));

    if (!hasValidExtension) {
      alert('Please provide a valid image URL (must end with .jpg, .jpeg, .png, or .gif)');
      return;
    }

    // Set the image path in the form
    this.form.patchValue({ image_path: url });
  }

  removeImage(): void {
    this.form.patchValue({ image_path: null });
  }

  getImagePreviewUrl(): string | null {
    const imagePath = this.form.get('image_path')?.value;
    if (!imagePath) return null;

    // If it's a URL, return as-is
    if (imagePath.startsWith('http')) {
      return imagePath;
    }

    // If it's a storage path, construct the full URL
    if (imagePath.includes('storage/')) {
      return imagePath;
    }

    // Otherwise, assume it's a relative path and prepend storage
    return `/storage/${imagePath}`;
  }
}
