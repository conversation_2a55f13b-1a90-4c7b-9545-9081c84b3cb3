import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })

export class LocalStorageService {
  static put(key: string, object: {[key: string]: any}): void {
    localStorage.setItem(key, JSON.stringify(object));
  }

  // static get(key: string) {
  //   const data = localStorage.getItem(key);
  //   return JSON.parse(data || '{}');
  // }

  static get(key: string) {
  const data = localStorage.getItem(key);
  
  // Check if data exists and is not null
    if (data && data !== 'undefined') {
    try {
      return JSON.parse(data);
    } catch (error) {
      console.error("Error parsing JSON from localStorage", error);
      return null;
    }
  }
  
  return {};
}
}
