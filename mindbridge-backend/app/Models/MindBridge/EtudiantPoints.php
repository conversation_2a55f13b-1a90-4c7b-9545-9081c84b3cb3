<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EtudiantPoints extends Model
{
    use HasFactory;
    protected $connection = 'mind_bridge';

    protected $table = 'etudiant_points';

    protected $fillable = [
        'ep_etudiant_id',
        'ep_number_points',
    ];  

    public function etudiant()
    {
        return $this->belongsTo(MindBridgeEtudiant::class, 'ep_etudiant_id');
    }
}