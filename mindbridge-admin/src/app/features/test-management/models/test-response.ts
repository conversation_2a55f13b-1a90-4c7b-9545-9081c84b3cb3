import { TestCategoryEnum } from './test-category-enum';
import { TypeQuestionEnum } from './type-question.enum';
import { DifficultyLevelEnum } from './difficulty-level-enum';

export interface TestResponse {
  id: number;
  title: string;
  description: string;
  target?: string;
  type: TestCategoryEnum;
  difficulty_level: DifficultyLevelEnum;
  steps: Step[];
  matiere_id?: number;
  niveau_id?: number;
  timer?: number;
  challenge_date?: Date | string;
  created_by: number;
  category_id: number;
  created_at: Date;
  updated_at: Date;
  matiere?: Matiere;
  niveau?: Niveau;
  content?: Content;
  category: Category;
  creator: Creator;
}

export interface Matiere {
  id: number;
  name_fr: string;
  name_ar: string;
  description: any;
  created_at: any;
  updated_at: any;
  deleted_at: any;
}

export interface Niveau {
  id: number;
  name: string;
  description: any;
  created_at: any;
  updated_at: any;
  deleted_at: any;
}

export interface Content {
  id: number;
  type: string;
  title: string;
  content: string;
  description: string;
  chapter_id: number;
  niveau_id: number;
  matiere_id: number;
  created_at: string;
  updated_at: string;
  niveau: Niveau;
}

export interface Category {
  id: number;
  parent_id: number;
  name: string;
  image_url: string;
  description: string;
  is_active: number;
  position: number;
  created_at: Date;
  updated_at: Date;
  code: TestCategoryEnum;
}

export interface Creator {
  id: number;
  name: string;
  email: string;
  phone: string;
  avatar: string;
  active: number;
  type: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
  first_login_completed: number;
  fcm_token: string;
  status: string;
  is_mind_bridge_user: number;
}
export interface Step {
  id: number;
  test_id: number;
  question_id: number;
  required: boolean;
  type: TypeQuestionEnum;
  condition: any;
  order: number;
  created_at: Date;
  updated_at: Date;
  question: Question;
}

export interface Question {
  id: number;
  type: string;
  content: string;
  description: string;
  is_true: boolean;
  is_false: boolean;
  question_type: string;
  category_id: number;
  created_at: Date;
  updated_at: Date;
  is_required: boolean;
  image_path: string;
  options: Option[];
}

export interface Option {
  id: number;
  name: string;
  icon: string;
  isCorrect: boolean;
  question_id: number;
  created_at: Date;
  updated_at: Date;
}
