import { Component, inject } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { LocalStorageService } from '../../shared/localstorage.service';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrl: './navbar.component.scss',
  imports: [],
})
export class NavbarComponent {
  private router = inject(Router);

  currentUrl = '';
  currentUser: any = LocalStorageService.get('currentUser');
  constructor() {
    const router = this.router;

    router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.currentUrl = event.url;
        const nav = this.router.getCurrentNavigation();
        // this.data = nav!.extras.state;
      }
    });
  }

  setTitle(): any {
    if (this.currentUrl.startsWith('/list-contenues')) {
      return 'Matières';
    }
    if (this.currentUrl.startsWith('/etudiant')) {
      return 'Liste des étudiants';
    }
    switch (this.currentUrl) {
      case '/dashboard':
        return 'Tableau de bord';

      case '/tests':
        return 'Gestion de test';

      case '/add-test':
        return 'Gestion de test';

      case '/matieres':
        return 'Matières';

      case '/add-contenue':
        return 'Ajouter contenue';

      case '/sante-mentale':
        return 'SANTÉ MENTALE';

      case '/sante-mentale/test-categories':
        return 'SANTÉ MENTALE';

      case '/etudiants':
        return 'Liste des étudiants';

      case '/questions-repository':
        return 'Référentiel de questions';

      case '/observations':
        return 'Observations';

      default:
        return 'MINDBRIDGE';
    }
  }
}
