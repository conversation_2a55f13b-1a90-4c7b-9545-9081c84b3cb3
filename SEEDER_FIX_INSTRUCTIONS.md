# Seeder Fix Instructions - Mental Health Tests

## Problem Identified
The `MentalHealthTestsSeeder.php` had incorrect category IDs that didn't match the actual database categories.

## Fix Applied
All 16 test category IDs have been corrected in the seeder file:

### Changes Made
```
19 → 11  (Test de Mémoire et Attention)
20 → 12  (Test de Résolution de Problèmes)
21 → 13  (Test de Développement Moteur)
22 → 14  (Test de Développement du Langage)
23 → 15  (Test sur les Stratégies d'Apprentissage)
24 → 16  (Test de Motivation Scolaire)
25 → 17  (Test d'Évaluation de la Personnalité)
26 → 18  (Test de Développement Personnel)
27 → 19  (Test de Régulation Émotionnelle)
28 → 20  (Test de Comportements Adaptatifs)
29 → 21  (Test sur les Techniques de Motivation)
30 → 22  (Test de Renforcement de l'Estime de Soi)
31 → 23  (Test de Contrôle Attentionnel)
32 → 24  (Test des Fonctions Exécutives)
33 → 25  (Test de Communication Familiale)
34 → 26  (Test des Interactions Sociales)
```

## File Modified
- `mindbridge-backend/database/seeders/mindBridge/MentalHealthTestsSeeder.php`

## How to Apply the Fix

### Step 1: Clear Old Data (Optional but Recommended)
```bash
cd /Users/<USER>/Desktop/MindBridge/mindbridge-backend

# Delete old tests that were created with wrong category IDs
php artisan tinker
>>> DB::table('tests')->whereIn('category_id', [19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34])->delete();
>>> exit
```

### Step 2: Run the Seeder
```bash
cd /Users/<USER>/Desktop/MindBridge/mindbridge-backend

# Run the seeder
php artisan db:seed --class=Database\\Seeders\\mindBridge\\MentalHealthTestsSeeder
```

### Step 3: Verify the Fix
```bash
# Check if tests are now linked to correct categories
php artisan tinker
>>> DB::table('tests')->select('id', 'title', 'category_id')->get();
```

Expected output should show:
- Test 1: category_id = 11
- Test 2: category_id = 12
- Test 3: category_id = 13
- ... and so on

## Verification Query

Run this in your database to verify:
```sql
SELECT 
    t.id,
    t.title,
    t.category_id,
    c.name as category_name,
    c.parent_id
FROM tests t
LEFT JOIN categories c ON t.category_id = c.id
WHERE t.category_id BETWEEN 11 AND 26
ORDER BY t.category_id;
```

Expected result:
- All tests should have category_id between 11-26
- All categories should have parent_id between 7-15
- All categories should have is_bo = 1 (true)

## If Tests Already Exist

If you already ran the seeder with wrong IDs:

### Option 1: Delete and Re-seed
```bash
# Delete all tests with wrong category IDs
php artisan tinker
>>> DB::table('test_steps')->whereIn('test_id', DB::table('tests')->whereIn('category_id', [19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34])->pluck('id'))->delete();
>>> DB::table('tests')->whereIn('category_id', [19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34])->delete();
>>> exit

# Then run the seeder
php artisan db:seed --class=Database\\Seeders\\mindBridge\\MentalHealthTestsSeeder
```

### Option 2: Update Existing Tests
```bash
php artisan tinker
>>> DB::table('tests')->where('category_id', 19)->update(['category_id' => 11]);
>>> DB::table('tests')->where('category_id', 20)->update(['category_id' => 12]);
>>> DB::table('tests')->where('category_id', 21)->update(['category_id' => 13]);
>>> DB::table('tests')->where('category_id', 22)->update(['category_id' => 14]);
>>> DB::table('tests')->where('category_id', 23)->update(['category_id' => 15]);
>>> DB::table('tests')->where('category_id', 24)->update(['category_id' => 16]);
>>> DB::table('tests')->where('category_id', 25)->update(['category_id' => 17]);
>>> DB::table('tests')->where('category_id', 26)->update(['category_id' => 18]);
>>> DB::table('tests')->where('category_id', 27)->update(['category_id' => 19]);
>>> DB::table('tests')->where('category_id', 28)->update(['category_id' => 20]);
>>> DB::table('tests')->where('category_id', 29)->update(['category_id' => 21]);
>>> DB::table('tests')->where('category_id', 30)->update(['category_id' => 22]);
>>> DB::table('tests')->where('category_id', 31)->update(['category_id' => 23]);
>>> DB::table('tests')->where('category_id', 32)->update(['category_id' => 24]);
>>> DB::table('tests')->where('category_id', 33)->update(['category_id' => 25]);
>>> DB::table('tests')->where('category_id', 34)->update(['category_id' => 26]);
>>> exit
```

## Next Steps

1. Run the seeder using the commands above
2. Verify the tests are now linked to correct categories
3. Test the API endpoint: `GET /api/mind_bridge/categories/board`
4. Verify that tests now appear under the correct categories in the admin panel

## Support

If you encounter any issues:
1. Check the database to see current test category_id values
2. Verify categories exist with correct parent_id and is_bo values
3. Check Laravel logs for any errors: `storage/logs/laravel.log`

