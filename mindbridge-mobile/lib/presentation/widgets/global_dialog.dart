import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/core/constants/app_strings.dart';
import 'package:mindbridge/core/utilities/extensions.dart';
import 'package:sizer/sizer.dart';

import '../widgets/gradient_button.dart';
import 'global_spots.dart';
import 'return_back_button.dart';

class GlobalDialog {
  static void showTheDialog({
    required VoidCallback iconTap,
    required String imagePath,
    required String title,
    required String subtitle,
    required String buttonText,
    required VoidCallback onButtonTap,
    bool barrierDismissible = true,
  }) {
    Get.dialog(
      _AnimatedGlobalDialog(
        iconTap: iconTap,
        imagePath: imagePath,
        title: title,
        subtitle: subtitle,
        buttonText: buttonText,
        onButtonTap: onButtonTap,
      ),
      barrierDismissible: barrierDismissible,
    );
  }
}

class _AnimatedGlobalDialog extends StatefulWidget {
  final VoidCallback iconTap;
  final String imagePath;
  final String title;
  final String subtitle;
  final String buttonText;
  final VoidCallback onButtonTap;

  const _AnimatedGlobalDialog({
    required this.iconTap,
    required this.imagePath,
    required this.title,
    required this.subtitle,
    required this.buttonText,
    required this.onButtonTap,
  });

  @override
  State<_AnimatedGlobalDialog> createState() => _AnimatedGlobalDialogState();
}

class _AnimatedGlobalDialogState extends State<_AnimatedGlobalDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _scaleAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutBack,
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Dialog(
        backgroundColor: Colors.white,
        insetPadding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(2.h),
        ),
        child: _DialogBody(
          iconTap: widget.iconTap,
          imagePath: widget.imagePath,
          title: widget.title,
          subtitle: widget.subtitle,
          buttonText: widget.buttonText,
          onButtonTap: widget.onButtonTap,
        ),
      ),
    );
  }
}

class _DialogBody extends StatelessWidget {
  final VoidCallback iconTap;
  final String imagePath;
  final String title;
  final String subtitle;
  final String buttonText;
  final VoidCallback onButtonTap;

  const _DialogBody({
    required this.iconTap,
    required this.imagePath,
    required this.title,
    required this.subtitle,
    required this.buttonText,
    required this.onButtonTap,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (ctx, constraints) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            Positioned.fill(
              child: GlobalSpots(
                width: constraints.maxWidth,
                height: constraints.maxHeight,
              ),
            ),
            Padding(
              padding: EdgeInsets.all(4.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Align(
                    alignment: Alignment.topRight,
                    child: ReturnBackButton(
                      onTap: iconTap,
                      customIcon: Icons.close,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Image.asset(
                    imagePath,
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 15.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    subtitle,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.black.withOpacity(0.7),
                    ),
                  ),
                  SizedBox(height: 3.h),
                  GradientButton(
                    text: buttonText,
                    onPressed: onButtonTap,
                  ),
                  SizedBox(height: 2.h),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

class GlobalInfoDialog {
  static void showInfoDialog({
    required String imagePath,
    required String description,
    bool isNetworkImage = true,
    VoidCallback? onClose,
  }) {
    Get.dialog(
      _AnimatedDialog(
        imagePath: imagePath,
        description: description,
        isNetworkImage: isNetworkImage,
        onClose: onClose ?? Get.back,
      ),
      barrierDismissible: true,
    );
  }
}

class _AnimatedDialog extends StatefulWidget {
  final String imagePath;
  final String description;
  final bool isNetworkImage;

  final VoidCallback onClose;

  const _AnimatedDialog({
    required this.imagePath,
    required this.description,
    required this.isNetworkImage,
    required this.onClose,
  });

  @override
  State<_AnimatedDialog> createState() => _AnimatedDialogState();
}

class _AnimatedDialogState extends State<_AnimatedDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _scaleAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutBack,
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Dialog(
        backgroundColor: Colors.white,
        insetPadding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 10.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(2.h),
        ),
        child: _InfoDialogBody(
          imagePath: widget.imagePath,
          description: widget.description,
          isNetworkImage: widget.isNetworkImage,
          onClose: widget.onClose,
        ),
      ),
    );
  }
}

class _InfoDialogBody extends StatelessWidget {
  final String imagePath;
  final String description;
  final bool isNetworkImage;

  final VoidCallback onClose;

  const _InfoDialogBody({
    required this.imagePath,
    required this.description,
    required this.isNetworkImage,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        const Positioned.fill(
          child: GlobalSpots(),
        ),
        Positioned(
          top: -10.h,
          left: 0,
          right: 0,
          child: Align(
            alignment: Alignment.topCenter,
            child: isNetworkImage
                ? imagePath.toImage(
                    height: 20.h,
                  )
                : Image.asset(
                    imagePath,
                    height: 20.h,
                    fit: BoxFit.contain,
                  ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(
            top: 12.h,
            left: 4.w,
            right: 4.w,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                description,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w400,
                  color: Colors.black.withOpacity(0.8),
                  height: 1.5,
                ),
              ),
              SizedBox(height: 4.h),
              GradientButton(
                text: AppStrings.gotIt,
                onPressed: onClose,
              ),
              SizedBox(height: 2.h),
            ],
          ),
        ),
      ],
    );
  }
}
