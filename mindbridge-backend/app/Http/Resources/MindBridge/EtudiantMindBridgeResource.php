<?php

namespace App\Http\Resources\MindBridge;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\MindBridge\Badges;
use App\Models\MindBridge\EtudaintBadges;

class EtudiantMindBridgeResource extends JsonResource
{
    public static $wrap = null;

    public function toArray(Request $request): array
    {
        $points = $this->points?->ep_number_points ?? 0;

        $allBadges = Badges::all();

        $studentBadgeIds = EtudaintBadges::where('eb_etudiant_id', $this->id)
            ->pluck('eb_badge_id')
            ->toArray();

        $badgesList = $allBadges->map(function ($badge) use ($studentBadgeIds) {
            $unlocked = in_array($badge->id, $studentBadgeIds);
            return [
                'id' => $badge->id,
                'name' => $badge->bdg_name,
                'icon_url' => $unlocked 
                    ? asset('storage' . $badge->bdg_unlocked_icon_url) 
                    : asset('storage' . $badge->bdg_locked_icon_url),
                'unlocked' => $unlocked,
            ];
        });

        $latestBadge = $allBadges->whereIn('id', $studentBadgeIds)
            ->sortByDesc('bdg_required_points')
            ->first();

        return [
            'id'                   => $this->id,
            'name'                 => $this->first_name . ' ' . $this->last_name,
            'first_name'           => $this->first_name,
            'last_name'            => $this->last_name,
            'email'                => $this->email,
            'avatar'               => asset($this->avatar),
            'identifiant'          => $this->identifiant,
            'taux_engagement'      => 10,
            'heure_moyen'          => 2,
            'scores'               => 0,
            'name_gamification'    => $latestBadge ? $latestBadge->bdg_description : null,
            'nni'                  => $this->nni,
            'test_profiling_completed' => $this->test_profiling_completed,
            'niveau'               => $this->niveau,
            'school'               => $this->school,
            'classement'           => 0,
            'count_etudiant'       => $this->niveau->etudiants()->count(),
            'badges'               => $badgesList,
            'points'               => $points,
            'notes'                => $this->notes,
        ];
    }
}