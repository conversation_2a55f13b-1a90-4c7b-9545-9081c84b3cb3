import { createAction, props } from "@ngrx/store";
import { TestManagement } from "../models/test-management.model";

// All TestsManagement
export const AllTestsManagement = createAction(
  '[ Test ] - All Tests Management',
  props<{ payload: any }>()
);

export const AllTestsManagementSuccess = createAction(
  '[ Test ] - All Tests Management Success ',
  props<{ testsManagement: any }>()
);
  
export const AllTestsManagementError = createAction(
  '[ Test ] - All Tests Management Error',
);

// Add TestManagement
export const AddTestManagement = createAction(
  '[ Test ] - Add Test Management',
  props<{ payload: TestManagement }>()
);

export const AddTestManagementSuccess = createAction(
  '[ Test ] - Add Test Management Success ',
  props<{ testManagement: any }>()
);
  
export const AddTestManagementError = createAction(
  '[ Test ] - Add Test Management Error',
);