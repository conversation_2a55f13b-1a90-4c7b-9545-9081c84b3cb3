<?php

namespace App\Console\Commands;

use App\Models\School;
use App\Service\TenantService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;

class SeederCommande extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenants:seed {tenantName? : The name of the tenant} {seederClass? : The seeder class to run}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenantName = $this->argument('tenantName');
        $seederClass = $this->argument('seederClass');
    
            if ($tenantName) {
                $tenant = School::where('name', $tenantName)->first();
                if ($tenant) {
                    $this->seedTenant($tenant, $seederClass);
                } else {
                    $this->error('School not found.');
                }
            } else {
                $tenants = School::all();
                $tenants->each(function ($tenant) use ($seederClass) {
                    $this->seedTenant($tenant, $seederClass);
                });
            }
        
    
        return Command::SUCCESS;
    }
    
    protected function seedTenant($tenant, $seederClass = null)
    {
        TenantService::switchToTenant($tenant->database);
        $this->info('Starting seeding for: ' . $tenant->domain);
        $this->info('----------------------------------');
        Config::set('app.seeder_type', 'tenant');
    
        $params = [
            '--database' => 'tenant'];
        if ($seederClass) {
            $params['--class'] = $seederClass;
        }
    
        Artisan::call('db:seed', $params);
    
        $this->info(Artisan::output());
    }
    
    
    
}
