import {
  Component,
  effect,
  inject,
  input,
  InputSignal,
  OnInit,
  Signal,
} from '@angular/core';
import { NgStyle } from '@angular/common';
import { EtudiantResponse } from '../../models/etudiant-response';
import { EtudiantsStore } from '../../etudiants.store';
import { SetLoading } from '../../../shared/store/shared.actions';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../core/app.state';
import { StudentTestHistoryComponent } from '../../../sante-mentale/components/student-test-history/student-test-history.component';

@Component({
  selector: 'app-details-etudiant',
  imports: [NgStyle, StudentTestHistoryComponent],
  templateUrl: './details-etudiant.component.html',
  standalone: true,
  styleUrl: './details-etudiant.component.scss',
  providers: [EtudiantsStore],
})
export class DetailsEtudiantComponent implements OnInit {
  id: InputSignal<number> = input.required<number>();
  private store = inject<Store<AppState>>(Store);

  #etudiantsStore = inject(EtudiantsStore);

  etudiant: Signal<EtudiantResponse> = this.#etudiantsStore.state.etudiant;

  percentage = 0;

  constructor() {
    effect(() => {
      if (this.etudiant()) {
        this.percentage = this.etudiant()?.taux_engagement;
      }
    });
  }

  readonly radius = 16;
  readonly circumference = 2 * Math.PI * this.radius;

  // Calculate stroke offset for the progress
  get dashOffset(): number {
    return this.circumference - (this.percentage / 100) * this.circumference;
  }

  ngOnInit() {
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.#etudiantsStore.getDetailsEtudiant(this.id);
  }
}
