<?php

namespace App\Models;

use App\Models\MindBridge\MindBridgeEtudiant;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NiveauCentrale extends Model
{
    use SoftDeletes;

    protected $connection = 'centrale';
    protected $table = 'niveaux';
    protected $fillable = ['name', 'description','color','background'];

    public function matieres()
    {
        return $this->belongsToMany(MatiereCentrale::class, 'niveaux_matiere', 'niveau_id', 'matiere_id')
            ->withTimestamps();
    }

    public function etudiants()
    {
        return $this->hasMany(MindBridgeEtudiant::class, 'niveau_id');
    }
}
