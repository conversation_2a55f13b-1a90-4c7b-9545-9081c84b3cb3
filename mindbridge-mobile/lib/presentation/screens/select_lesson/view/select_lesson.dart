import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/presentation/widgets/btm_navigation_bar.dart';
import 'package:mindbridge/presentation/widgets/global_empty.dart';
import 'package:mindbridge/presentation/widgets/global_error.dart';
import 'package:sizer/sizer.dart';

import '../../../widgets/global_header.dart';
import '../../../widgets/global_loader.dart';
import '../../../widgets/global_white_card.dart';
import '../../lesson/view/lesson_view.dart';
import '../controller/content_controller.dart';
import '../widgets/lesson_card.dart';

class SelectLessonView extends StatelessWidget {
  final String? subjectTitle;
  final int matiereId;

  const SelectLessonView(
      {super.key, this.subjectTitle, required this.matiereId});

  @override
  Widget build(BuildContext context) {
    final ContentController controller = Get.find<ContentController>();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.fetchLessons(matiereId);
    });

    return BaseScreen(
      child: Scaffold(
        appBar: const GlobalHeader(),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
          child: Obx(() {
            return GlobalWhiteCard(
              title: subjectTitle ?? '',
              child: controller.isLoading.value
                  ? const Center(child: GlobalLoader())
                  : controller.isError.value
                      ? GlobalErrorHandler(
                          onRetry: () {
                            controller.fetchLessons(matiereId);
                          },
                        )
                      : controller.isEmptyData.value
                          ? GlobalEmptyHandler(
                              onBack: () {
                                Get.back();
                              },
                            )
                          : ListView.separated(
                              padding: EdgeInsets.zero,
                              itemCount: controller.lessons.length,
                              separatorBuilder: (context, index) =>
                                  SizedBox(height: 2.h),
                              itemBuilder: (context, index) {
                                final lesson = controller.lessons[index];
                                return LessonCard(
                                  lessonTitle: lesson.title ?? 'Untitled',
                                  onTap: () {
                                    Get.to(() => LessonView(
                                          lessonTitle: lesson.title,
                                          pdfPath: lesson.content,
                                          contentId: lesson.id,
                                        ));
                                  },
                                );
                              },
                            ),
            );
          }),
        ),
      ),
    );
  }
}
