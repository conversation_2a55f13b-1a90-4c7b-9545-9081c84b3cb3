<?php

namespace Database\Seeders\mindBridge;

use App\Models\MindBridge\Badges;
use Illuminate\Database\Seeder;

class   BadgetsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $badgets = [
            [
                'bdg_name' => "Explorateur du Savoi",
                'bdg_description' => "Explorateur du Savoir",
                'bdg_locked_icon_url' => "/mindBridge/badges/locked/1.png",
                'bdg_unlocked_icon_url' => "/mindBridge/badges/unlocked/1.png",
                'bdg_required_points' => 100
            ],
            [
                'bdg_name' => "Savant en Herbe",
                'bdg_description' => "Savant en Herbe",
                'bdg_locked_icon_url' => "/mindBridge/badges/locked/2.png",
                'bdg_unlocked_icon_url' => "/mindBridge/badges/unlocked/2.png",
                'bdg_required_points' => 500
            ],
            [
                'bdg_name' => "Conquérant des Connaissances",
                'bdg_description' => "Conquérant des Connaissances",
                'bdg_locked_icon_url' => "/mindBridge/badges/locked/3.png",
                'bdg_unlocked_icon_url' => "/mindBridge/badges/unlocked/3.png",
                'bdg_required_points' => 1000
            ],
            [
                'bdg_name' => "Maître Apprenant",
                'bdg_description' => "Maître Apprenant",
                'bdg_locked_icon_url' => "/mindBridge/badges/locked/4.png",
                'bdg_unlocked_icon_url' => "/mindBridge/badges/unlocked/4.png",
                'bdg_required_points' => 2000
            ],
            [
                'bdg_name' => "Innovateur de la Pensée",
                'bdg_description' => "Innovateur de la Pensée",
                'bdg_locked_icon_url' => "/mindBridge/badges/locked/5.png",
                'bdg_unlocked_icon_url' => "/mindBridge/badges/unlocked/5.png",
                'bdg_required_points' => 3000
            ],
            [
                'bdg_name' => "Guide Scolaire",
                'bdg_description' => "Guide Scolaire",
                'bdg_locked_icon_url' => "/mindBridge/badges/locked/6.png",
                'bdg_unlocked_icon_url' => "/mindBridge/badges/unlocked/6.png",
                'bdg_required_points' => 4000
            ],
            [
                'bdg_name' => "Leader Académique",
                'bdg_description' => "Leader Académique",
                'bdg_locked_icon_url' => "/mindBridge/badges/locked/7.png",
                'bdg_unlocked_icon_url' => "/mindBridge/badges/unlocked/7.png",
                'bdg_required_points' => 5000
            ],
            [
                'bdg_name' => "Sage de l’Éducation",
                'bdg_description' => "Sage de l’Éducation",
                'bdg_locked_icon_url' => "/mindBridge/badges/locked/8.png",
                'bdg_unlocked_icon_url' => "/mindBridge/badges/unlocked/8.png",
                'bdg_required_points' => 6500
            ],
            [
                'bdg_name' => "Ambassadeur du Savoir",
                'bdg_description' => "Ambassadeur du Savoir",
                'bdg_locked_icon_url' => "/mindBridge/badges/locked/9.png",
                'bdg_unlocked_icon_url' => "/mindBridge/badges/unlocked/9.png",
                'bdg_required_points' => 8000
            ],
            [
                'bdg_name' => "Maître de l’École",
                'bdg_description' => "Maître de l’École",
                'bdg_locked_icon_url' => "/mindBridge/badges/locked/10.png",
                'bdg_unlocked_icon_url' => "/mindBridge/badges/unlocked/10.png",
                'bdg_required_points' => 10000
            ],
        ];
      
        foreach ($badgets as $badge) {
            Badges::create($badge);
        }
    }
}
