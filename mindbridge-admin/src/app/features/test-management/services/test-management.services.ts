import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { TestManagement } from '../models/test-management.model';

@Injectable({
  providedIn: 'root',
})
export class TestManagementService {
  private http = inject(HttpClient);


  AllTestsManagement(payload: any): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/tests',
      payload
    );
  }

  AddTestMangement(payload: TestManagement): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/tests/storeAll',
      payload
    );
  }
}
