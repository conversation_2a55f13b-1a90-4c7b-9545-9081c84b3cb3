import 'package:flutter/material.dart';

class AppColors {
  static const Color primary = Color(0xFF568397);
  static const Color secondary = Color(0xFF365672);
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color fieldBorderColor = Color(0xffD0D5DD);
  static const Color lightPink = Color(0xffD8CDDD);
  static const Color lightPinkBorderColor = Color(0xffCBBCD2);

  static const LinearGradient gradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFC1EFF7), // Light Blue
      Color(0xFF6BA4BD), // Medium Blue
      Color(0xFF7E588E), // Purple
      Color(0xFFBE1A87), // Pink
    ],
    stops: [-0.0556, 0.4307, 1.1946, 1.4722],
  );

  static const LinearGradient flatButtongradient = LinearGradient(
    colors: [
      Color(0xFFF0F6F8),
      Color(0xFFE1EDF2),
      Color(0xFFF0F6F8),
    ],
  );
  static const LinearGradient customGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFF0F6F8), // Light Blue
      Color(0xFFF2EEF4), // Soft Lavender
      Color(0xFFF9E8F3), // Soft Pink
    ],
  );
  static LinearGradient borderAiGradient = const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFE5A3CF),
      Color(0xFFE5DEE8),
      Color(0xFFA6C8D7),
    ],
  );
  static LinearGradient backgroundAiGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      const Color(0xFFE5A3CF).withOpacity(0.6),
      const Color(0xFFE5DEE8).withOpacity(0.6),
      const Color(0xFFA6C8D7).withOpacity(0.6),
    ],
  );
  static LinearGradient audioWaveGradient = const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF4E2583),
      Color(0xFFBF318A),
      Color(0xFFEE739B),
      Color(0xFFBA318A),
      Color(0xFF4E2583),
    ],
  );
}
