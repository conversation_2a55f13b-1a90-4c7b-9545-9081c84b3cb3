<?php

namespace App\Services;

use App\Models\MindBridge\Option;
use App\Models\MindBridge\TestScoringRule;
use Illuminate\Support\Facades\Log;

class ScoringService
{
    /**
     * Calculate the overall test score
     */
    public function calculateTestScore($test, $answers)
    {
        Log::info('=== SCORING SERVICE DEBUG START ===');
        Log::info('Test ID: ' . $test->id . ', Test Title: ' . $test->title);
        Log::info('Answers received: ' . json_encode($answers));

        $score = 0;
        $maxScore = 0;

        // Get all steps for this test
        $steps = $test->steps()->with('question', 'question.options')->get();
        Log::info('Total steps found: ' . $steps->count());

        foreach ($steps as $step) {
            $pointsValue = $step->points_value ?? 1;
            Log::info('Processing step - ID: ' . $step->id . ', Question ID: ' . $step->question_id . ', Points Value: ' . $pointsValue);
            $maxScore += $pointsValue;

            if (isset($answers[$step->question_id])) {
                Log::info('Answer found for question ' . $step->question_id . ': ' . json_encode($answers[$step->question_id]));
                $isCorrect = $this->isAnswerCorrect($step, $answers[$step->question_id]);
                Log::info('Answer is correct: ' . ($isCorrect ? 'YES' : 'NO'));
                if ($isCorrect) {
                    $score += $pointsValue;
                    Log::info('Added ' . $pointsValue . ' points. Current score: ' . $score);
                }
            } else {
                Log::info('No answer found for question ' . $step->question_id);
            }
        }

        // Calculate overall percentage
        $percentage = ($maxScore > 0) ?
            round(($score / $maxScore) * 100, 2) : 0;

        Log::info('Final calculation - Score: ' . $score . ', Max Score: ' . $maxScore . ', Percentage: ' . $percentage);

        // Find applicable scoring rule
        $rule = $this->findApplicableRule($test, $percentage);
        Log::info('Scoring rule found: ' . ($rule ? json_encode($rule->toArray()) : 'None'));

        $result = [
            'total_score' => $score,
            'total_max_score' => $maxScore,
            'percentage' => $percentage,
            'feedback' => $rule ? $rule->feedback : null,
            'recommendation' => $rule ? $rule->recommendation : null,
            'interpretation' => $rule ? $rule->interpretation : 'À évaluer',
        ];

        Log::info('Final result: ' . json_encode($result));
        Log::info('=== SCORING SERVICE DEBUG END ===');

        return $result;
    }

    /**
     * Check if an answer is correct
     */
    private function isAnswerCorrect($step, $selectedOptions)
    {
        Log::info('  Checking answer for question ' . $step->question_id);

        // Get all correct options for this question
        $correctOptions = Option::where('question_id', $step->question_id)
            ->where('isCorrect', true)
            ->pluck('id')
            ->map(fn($id) => (int)$id)
            ->toArray();

        Log::info('  Correct options for question ' . $step->question_id . ': ' . json_encode($correctOptions));

        // Normalize selected options to array of integers
        $selected = is_array($selectedOptions) ?
            array_map('intval', $selectedOptions) : [(int)$selectedOptions];

        Log::info('  Selected options: ' . json_encode($selected));

        // If no correct options defined, answer is wrong
        if (empty($correctOptions)) {
            Log::info('  No correct options defined - answer is wrong');
            return false;
        }

        // Check if all correct options are selected and no wrong options
        $intersect = array_intersect($selected, $correctOptions);
        $isCorrect = count($intersect) === count($correctOptions) && count($intersect) === count($selected);

        Log::info('  Intersection: ' . json_encode($intersect));
        Log::info('  Is correct: ' . ($isCorrect ? 'YES' : 'NO'));
        Log::info('  Logic: intersect_count(' . count($intersect) . ') === correct_count(' . count($correctOptions) . ') && intersect_count(' . count($intersect) . ') === selected_count(' . count($selected) . ')');

        return $isCorrect;
    }

    /**
     * Find the applicable scoring rule for a test and percentage
     */
    private function findApplicableRule($test, $percentage)
    {
        return TestScoringRule::where('test_id', $test->id)
            ->where('min_percentage', '<=', $percentage)
            ->where('max_percentage', '>=', $percentage)
            ->first();
    }
}

