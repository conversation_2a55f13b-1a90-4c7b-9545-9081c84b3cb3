<?php

namespace App\Models;
 
use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;

class MatiereCentrale extends Model
{
    // use SoftDeletes;

    protected $connection = 'centrale';
    protected $table = 'matieres';
    protected $fillable = ['name_fr','name_ar', 'description','title_color','image_url','gradient_background','gradient_border'];


    public function niveaux()
    {
        return $this->belongsToMany(NiveauCentrale::class, 'niveaux_matiere', 'matiere_id', 'niveau_id');
    }

    public function chapters(){
        return $this->hasMany(MindBridge\Chapter::class, 'matiere_id');
    }
}
