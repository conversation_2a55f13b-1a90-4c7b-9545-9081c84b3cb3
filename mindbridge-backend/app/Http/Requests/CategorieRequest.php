<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class CategorieRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        Log::info("====== CategorieRequest => Payload Before Validation ======\n" . json_encode($this->all(), JSON_PRETTY_PRINT));
        $categorie_id = $this->route('category');
        return [
            "name" => [
                !$this->isUpdating() ? 'required' : 'nullable',
                'string',
                Rule::unique('categories', 'name')->ignore($categorie_id)
                ->whereNull('deleted_at')
            ],
            'position' => 'integer',
        ];
    }

    /**
     * Get the custom validation error messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => trans('categorieConst.const.required_name'),
            'name.unique' => trans('categorieConst.const.unique_name')
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param Validator $validator
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator): void
    {
        Log::error("====== CategorieRequest => Validation Errors ======\n" . json_encode($validator->errors()->toArray(), JSON_PRETTY_PRINT));
        throw new HttpResponseException(response()->json($validator->errors(), 400));
    }


    protected function isUpdating(): bool
    {
        return $this->isMethod('patch') || $this->isMethod('put') || ($this->isMethod('post') && $this->id);
    }
}