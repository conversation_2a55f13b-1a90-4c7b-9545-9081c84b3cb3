import { TestCategoryEnum } from './test-category-enum';

export interface ITestCategoriesBoardResponse {
  test_models: ITestModels;
  mental_health: IMentalHealth;
}

export interface ITestModels {
  id: number;
  name: string;
  enabled: boolean;
  children: IChildren[];
}

export interface IChildren {
  id: number;
  name: string;
  enabled: boolean;
  image_url: string;
  icon: string;
  code: TestCategoryEnum;
  count: number;
  gradient_background: string;
  button_text: string;
}

export interface IMentalHealth {
  id: number;
  name: string;
  enabled: boolean;
  children: IChildren[];
}
export class TestCategoriesBoardResponse
  implements ITestCategoriesBoardResponse
{
  test_models: ITestModels;
  mental_health: IMentalHealth;

  constructor() {
    this.test_models = new TestModels();
    this.mental_health = new MentalHealth();
  }
}

export class TestModels implements ITestModels {
  id: number;
  name: string;
  enabled: boolean;
  children: IChildren[];

  constructor() {
    this.id = null;
    this.name = '';
    this.enabled = false;
    this.children = [];
  }
}

export class MentalHealth implements IMentalHealth {
  id: number;
  name: string;
  enabled: boolean;
  children: IChildren[];

  constructor() {
    this.id = null;
    this.name = '';
    this.enabled = false;
    this.children = [];
  }
}
