<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EtudiantTestStatus extends Model
{

    protected $connection = 'mind_bridge';
    
    protected $table = 'etudiant_test_status';
    protected $fillable = [
        'etudiant_id',
        'test_id',
        'percentage',
        'status',
        'score',
        'started_at',
        'completed_at'

    ];
    

    public function test()
    {
        return $this->belongsTo(Test::class, 'test_id');
    }

    public function etudiant()
    {
        return $this->belongsTo(MindBridgeEtudiant::class, 'etudiant_id');
    }
}
