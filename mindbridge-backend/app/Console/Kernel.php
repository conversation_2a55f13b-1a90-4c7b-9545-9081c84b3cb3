<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('process:monthly-payments')->monthlyOn(28, '23:45'); // add to crontab
        // $schedule->command('process:credit-repayments')->monthlyOn(28, '23:59'); // add to crontab
        // $schedule->command('notifications:send-daily')->ddailyAt('16:00'); // add to crontab
        // $schedule->command('app:monthly-reminder')->monthlyOn(1, '10:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
