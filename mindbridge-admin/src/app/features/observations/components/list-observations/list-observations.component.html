<div class="observations-container p-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">Observations</h1>
    <button
      (click)="openFormDialog()"
      class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
    >
      <mat-icon>add</mat-icon>
      Ajouter une observation
    </button>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg p-4 mb-6 shadow">
    <form [formGroup]="filterForm" class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div>
        <label class="block text-sm font-medium mb-1">Rechercher</label>
        <input
          type="text"
          formControlName="search"
          placeholder="Rechercher par libellé ou description..."
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label class="block text-sm font-medium mb-1">Catégorie</label>
        <input
          type="text"
          formControlName="category"
          placeholder="Filtrer par catégorie..."
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label class="block text-sm font-medium mb-1">Visible pour</label>
        <select
          formControlName="visible_to"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Tous</option>
          <option value="teacher">Enseignant</option>
          <option value="parent">Parent</option>
          <option value="both">Les deux</option>
        </select>
      </div>

      <div>
        <label class="block text-sm font-medium mb-1">Statut</label>
        <select
          formControlName="active"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Tous</option>
          <option value="true">Actif</option>
          <option value="false">Inactif</option>
        </select>
      </div>
    </form>

    <div class="mt-4 flex gap-2">
      <button
        (click)="resetFilters()"
        class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
      >
        Réinitialiser les filtres
      </button>
    </div>
  </div>

  <!-- Loading State -->
  @if (isLoading()) {
    <div class="flex justify-center items-center py-8">
      <mat-icon class="animate-spin">refresh</mat-icon>
      <span class="ml-2">Chargement...</span>
    </div>
  } @else {
    <!-- Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <table class="w-full">
        <thead class="bg-gray-100 border-b">
          <tr>
            <th class="px-6 py-3 text-left text-sm font-semibold">Libellé</th>
            <th class="px-6 py-3 text-left text-sm font-semibold">Catégorie</th>
            <th class="px-6 py-3 text-left text-sm font-semibold">Visible pour</th>
            <th class="px-6 py-3 text-left text-sm font-semibold">Statut</th>
            <th class="px-6 py-3 text-left text-sm font-semibold">Actions</th>
          </tr>
        </thead>
        <tbody>
          @for (observation of observations(); track observation.id) {
            <tr class="border-b hover:bg-gray-50">
              <td class="px-6 py-4 text-sm">{{ observation.label }}</td>
              <td class="px-6 py-4 text-sm">{{ observation.category || '-' }}</td>
              <td class="px-6 py-4 text-sm">
                <span
                  class="px-2 py-1 rounded text-xs font-medium"
                  [ngClass]="{
                    'bg-blue-100 text-blue-800': observation.visible_to === 'both',
                    'bg-green-100 text-green-800': observation.visible_to === 'teacher',
                    'bg-purple-100 text-purple-800': observation.visible_to === 'parent',
                  }"
                >
                  {{ getVisibleToLabel(observation.visible_to) }}
                </span>
              </td>
              <td class="px-6 py-4 text-sm">
                <span
                  class="px-2 py-1 rounded text-xs font-medium"
                  [ngClass]="{
                    'bg-green-100 text-green-800': observation.active,
                    'bg-red-100 text-red-800': !observation.active,
                  }"
                >
                  {{ observation.active ? 'Actif' : 'Inactif' }}
                </span>
              </td>
              <td class="px-6 py-4 text-sm">
                <div class="flex gap-2">
                  <button
                    (click)="openFormDialog(observation)"
                    class="text-blue-600 hover:text-blue-800"
                    title="Modifier"
                  >
                    <mat-icon class="text-lg">edit</mat-icon>
                  </button>
                  <button
                    (click)="viewMappedTest(observation.id!)"
                    class="text-green-600 hover:text-green-800"
                    title="Voir le test lié"
                  >
                    <mat-icon class="text-lg">visibility</mat-icon>
                  </button>
                  <button
                    (click)="deleteObservation(observation.id!)"
                    class="text-red-600 hover:text-red-800"
                    title="Supprimer"
                  >
                    <mat-icon class="text-lg">delete</mat-icon>
                  </button>
                </div>
              </td>
            </tr>
          } @empty {
            <tr>
              <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                Aucune observation trouvée
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="flex items-center justify-between mt-6">
      <div class="text-sm text-gray-600">
        Affichage de {{ (currentPage() - 1) * perPage() + 1 }} à
        {{ Math.min(currentPage() * perPage(), totalItems()) }} sur
        {{ totalItems() }} observations
      </div>
      <div class="flex gap-2">
        <button
          (click)="goToPage(currentPage() - 1)"
          [disabled]="currentPage() === 1"
          class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Précédent
        </button>
        <span class="px-4 py-2">
          Page {{ currentPage() }} sur {{ totalPages() }}
        </span>
        <button
          (click)="goToPage(currentPage() + 1)"
          [disabled]="currentPage() === totalPages()"
          class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Suivant
        </button>
      </div>
    </div>
  }
</div>

