import 'package:get/get.dart';
import 'package:mindbridge/data/repositories/recommandation_repository.dart';
import 'package:mindbridge/presentation/screens/recommandation/controller/recommandation_controller.dart';

import '../../data/providers/local/secure_storage_service.dart';
import '../../data/providers/remote/dio_client.dart';
import '../../data/repositories/auth_repository.dart';
import '../../data/repositories/test_repository.dart';
import '../data/repositories/content_repository.dart';
import '../data/repositories/evaluation_repository.dart';
import '../data/repositories/home_repository.dart';
import '../data/repositories/subject_repository.dart';
import '../presentation/screens/complete_profile/controller/step_controller.dart';
import '../presentation/screens/evaluation/controller/evaluation_controller.dart';
import '../presentation/screens/home/<USER>/home_controller.dart';
import '../presentation/screens/login/controller/login_controller.dart';
import '../presentation/screens/select_lesson/controller/content_controller.dart';
import '../presentation/screens/select_subject/controller/subject_controller.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SecureStorageService());

    Get.lazyPut(
        () => DioClient(secureStorage: Get.find<SecureStorageService>()));

    Get.lazyPut(() => AuthRepository(
          dioClient: Get.find<DioClient>(),
          secureStorage: Get.find<SecureStorageService>(),
        ));

    Get.lazyPut(() => LoginController(authRepository: Get.find()));
    Get.lazyPut(() => HomeRepository(dioClient: Get.find<DioClient>()));
    Get.lazyPut(() => HomeController(
          homeRepository: Get.find<HomeRepository>(),
        ));
  }
}

class LoginBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SecureStorageService());
    Get.lazyPut(
        () => DioClient(secureStorage: Get.find<SecureStorageService>()));
    Get.lazyPut(() => AuthRepository(
          dioClient: Get.find<DioClient>(),
          secureStorage: Get.find<SecureStorageService>(),
        ));
    Get.lazyPut(() => LoginController(authRepository: Get.find()));
  }
}

class CompleteProfileBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SecureStorageService());
    Get.lazyPut(
        () => DioClient(secureStorage: Get.find<SecureStorageService>()));
    Get.lazyPut(() => TestRepository(dioClient: Get.find<DioClient>()));
    Get.lazyPut(() => StepController(testRepository: Get.find()));
  }
}

class BaseScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SecureStorageService());
    Get.lazyPut(
        () => DioClient(secureStorage: Get.find<SecureStorageService>()));
    Get.lazyPut(() => AuthRepository(
          dioClient: Get.find<DioClient>(),
          secureStorage: Get.find<SecureStorageService>(),
        ));
  }
}

class SubjectBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SubjectRepository(dioClient: Get.find<DioClient>()));
    Get.lazyPut(() =>
        SubjectController(subjectRepository: Get.find<SubjectRepository>()));
  }
}

class LessonBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ContentRepository(dioClient: Get.find<DioClient>()));
    Get.lazyPut(() =>
        ContentController(contentRepository: Get.find<ContentRepository>()));
  }
}

class EvaluationBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => EvaluationRepository(dioClient: Get.find<DioClient>()));
    Get.lazyPut(
      () => EvaluationController(
        evaluationRepository: Get.find<EvaluationRepository>(),
      ),
    );
  }
}


class RecommendationBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => RecommandationRepository(dioClient: Get.find<DioClient>()));
    Get.lazyPut(
      () => RecommandationController(
        recommandationRepository: Get.find<RecommandationRepository>(),
      ),
    );
  }
}
