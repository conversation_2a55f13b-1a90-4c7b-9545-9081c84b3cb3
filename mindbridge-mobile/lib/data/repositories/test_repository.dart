import 'package:flutter/material.dart';

import '../../core/constants/api_routes.dart';
import '../../core/models/test_profiling.dart';
import '../providers/remote/dio_client.dart';

class TestRepository {
  final DioClient _dioClient;

  TestRepository({required DioClient dioClient}) : _dioClient = dioClient;

  Future<TestProfiling?> fetchTestProfilingQuestions() async {
    try {
      final response = await _dioClient.dio.get(ApiRoutes.testProfiling);

      if (response.statusCode == 200 && response.data != null) {
        return TestProfiling.fromJson(response.data);
      }

      return null;
    } catch (e) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> submitTestResults(
      Map<String, dynamic> payload) async {
    try {
      final response =
          await _dioClient.dio.post(ApiRoutes.testProfiling, data: payload);

      if (response.statusCode == 200 && response.data != null) {
        return response.data;
      }
    } catch (e) {
      debugPrint("Error submitting test results: $e");
    }
    return null;
  }
}
