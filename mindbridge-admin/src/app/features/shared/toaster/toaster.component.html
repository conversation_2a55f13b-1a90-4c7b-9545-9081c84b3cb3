<div
  [class]="data.status !== 'SUCCESS' ? 'container_notification' : 'container_notification_success'"
>
  <div class="box_btn_and_notifie">
    <div class="icon_self" [class.ml13]="data.status === 'SUCCESS'">
      <img
        class="icon_true"
        [src]="
          (data.status !== 'SUCCESS'
            ? '/assets/icones/cancel.svg'
            : '/assets/icones/check_circle.svg')
        "
        alt=""
      />
    </div>
    <div [class]="data.status !== 'SUCCESS' ? 'place_texts' : 'place_texts_success'">
      <p [class]="data.status !== 'SUCCESS' ? 'text_notie1' : 'text_notie1_success'">
        <span class="span_toast">{{data.message}}</span>

      </p>
    </div>
  </div>
  <div [class]="data.status == 'SUCCESS' ? 'btn_close_notie_success' : 'btn_close_notie'" (click)="closeToast()">
  </div>
</div>
