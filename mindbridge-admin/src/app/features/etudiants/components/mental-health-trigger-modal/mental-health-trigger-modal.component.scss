.modal-container {
  display: flex;
  flex-direction: column;
  min-width: 500px;
  max-width: 600px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;

  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 28px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: #1f2937;
    }
  }
}

.modal-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  max-height: 500px;
}

.trigger-selection {
  margin-bottom: 24px;

  h3 {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }
}

.trigger-options {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.trigger-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #d1d5db;
    background: #f9fafb;
  }

  &.active {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  .trigger-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .trigger-label {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
  }

  .trigger-desc {
    font-size: 12px;
    color: #6b7280;
  }
}

.observation-selection {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;

  h3 {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  font-size: 14px;
}

.observation-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.observation-item {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #d1d5db;
    background: #f9fafb;
  }

  &.selected {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  .obs-label {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
  }

  .obs-description {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
  }

  .obs-category {
    font-size: 11px;
    color: #9ca3af;
    font-style: italic;
  }
}

.no-observations {
  text-align: center;
  padding: 20px;
  color: #9ca3af;
  font-size: 14px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.btn-cancel,
.btn-confirm {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.btn-cancel {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;

  &:hover:not(:disabled) {
    background: #f3f4f6;
  }
}

.btn-confirm {
  background: #3b82f6;
  color: white;

  &:hover:not(:disabled) {
    background: #2563eb;
  }
}

