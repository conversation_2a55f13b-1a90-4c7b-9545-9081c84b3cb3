import { FormControl } from '@angular/forms';
import { OPTION_ALL } from '../../shared/global.config';

export interface IContentSearchForm {
  niveau: number;
  chapter: number;
}

export class ContentSearchForm implements IContentSearchForm {
  niveau: number;
  chapter: number;
  constructor() {
    this.niveau = OPTION_ALL;
    this.chapter = OPTION_ALL;
  }
}

export interface IContentSearchFormGroup {
  niveau: FormControl<number>;
  chapter: FormControl<number>;
}

export class ContentSearchFormGroup implements IContentSearchFormGroup {
  niveau: FormControl<number>;
  chapter: FormControl<number>;

  constructor(contentSearchForm: IContentSearchForm = new ContentSearchForm()) {
    this.niveau = new FormControl<number>(contentSearchForm.niveau);
    this.chapter = new FormControl<number>(contentSearchForm.chapter);
  }
}
