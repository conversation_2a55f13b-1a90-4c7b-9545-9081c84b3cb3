.sidebar {
    height: 100%;
    width: 310px;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    padding-top: 16px;
    min-height: 100%;
    border-right: 1px solid #EAECF0;

    .logo_ {
        position: relative;
        width: 100%;
        height: 70px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        padding-left: 30px;

        .logo {
            width: 32px;
            height: 32px;
            border-radius: 7px;
        }

        .title_logo {
            font-size: 15px;
            font-weight: 600;
            color: #2C323F;
            padding-left: 7px;
        }

        .head {
            margin: 0 auto;
            color: #6BA4BD;
            font-weight: 600;
            width: 88%;
            font-size: 40px;

            span {
                color: #6BA4BD;
            }
        }

        .logo_responsive {
            display: none;
        }
    }

    .side_list {
        height: calc(100vh - 113px);
        max-height: 100vh;
        overflow-y: auto;

        .link_a {
            text-decoration: none;
            font-size: 17px;
            margin: 0 0 0 19px;
            width: 90%;
            cursor: pointer;
            color: #464D5C;
            padding: 8px 0px 8px 12px;

            .icn {
                width: 21px;
                height: 25px;
                border-radius: 0 !important;
                display: flex;
                justify-content: center;
                align-items: center;

                img {
                    width: 100%;
                    height: 80%;
                }

                .mat-icon {
                    fill: #464D5C;
                    width: fit-content;
                    height: fit-content;
                    // width: 31px;
                    // height: 24px;
                    font-size: 23px;
                }

                .logout {
                    width: 35px;
                    height: 26px;
                    font-size: 30px;
                    margin-left: auto;
                }
            }

            .logout_btn {
                margin-left: auto;
                margin-right: 0;

                .logout {
                    padding-left: 0;
                }
            }
        }

        .link_a:last-child {
            border-top: 1px solid #EAECF0;
            margin-top: auto !important;

            .icn {
                align-items: center;
            }
        }
    }
}

.main {
    margin-left: 217px;
    padding: 0px 13px;
}

.end_btn {
    width: 100%;
}

.name_email {
    width: 80%;
    max-width: 165px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    flex-direction: column;
}

.name_email div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
}

.vente_is_open {
    margin-top: 110px !important;
}

.active-route {
    color: #6BA4BD !important;
}

.font-small {
    font-size: small;
    color: #6BA4BDd1;
}

.title {
    text-transform: uppercase;
}

.log_out {
    width: fit-content;
    margin-inline: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px ;

    span {
        color: #365672;
        font-size: 14px;
        font-weight: 600;
    }
}

.flex-inline {
    display: flex;
    gap: 10px;

    .title {
        line-height: 25px;
    }
}

.not_alowed {
    cursor: not-allowed !important;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #e6e0e5fe !important;
    border-radius: 25px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #dad2d9 !important;
}

@media (max-width: 1000px) {
    .logo_ {
        position: relative;
        width: 20px;
        height: 20px;
        padding-left: 6px !important;

        .title_logo {
            display: none;
        }

        margin: 0 {
            left: 0 !important;
            bottom: 20px !important;
        }

        .logo_responsive {
            display: block !important;
            position: absolute;
            width: 72px;
            height: 72px;
        }
    }

    .logo_::after {
        content: '';
        width: 138% !important;
    }

    .link_a {
        position: relative;
        margin: 0 auto !important;
        width: fit-content !important;
        padding: 0 !important;
    }

    .icn {
        margin-right: 0;
        padding: 0;
        align-items: center;

        img {
            width: 23px;
            height: 27px;
        }
    }

    .title {
        position: absolute;
        top: 18px;
        left: 200%;
        background-color: #6BA4BD;
        color: #fff;
        padding: 16px 10px;
        border-radius: 6px;
        transform-origin: left;
        --scale: 0;
        opacity: 0;
        transform: translateY(-50%) scale(var(--scale));
        transition: opacity 0.15s ease, transform 0.15s ease;
        white-space: nowrap;
        width: auto;
    }

    .link_a:last-child {
        .title {
            top: auto !important;
            bottom: 10px !important;
            --scale: auto !important;
        }
    }

    .link_a:hover .title {
        --scale: 1;
        opacity: 1;
    }

    .sidebar {
        padding-top: 15px;
        width: 47px !important;
        overflow: initial !important;
    }

    .mat-icon:not(.logo_responsive) {
        width: fit-content !important;
    }

    .div_Pseudo_elements,
    .head {
        text-wrap: nowrap;
        overflow: hidden;
        font-size: 30px !important;
        padding-left: 4.3px !important;
        letter-spacing: 2px;
    }

    .side_list {
        margin-top: 40px !important;
        overflow: initial !important;

        .content_links {

            .menu_links,
            .stock_menu {
                width: 86% !important;
                margin-left: 0 !important;

                .link_a {
                    position: relative;
                    margin: 0 auto !important;
                    width: 100% !important;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                }
            }

            .icn {
                margin-right: 0 !important;
            }
        }
    }

    .active::before {
        border-left: 0 !important;
    }

    .log_out {
        padding: 15px 0px !important;
        
        span {
            display: none;
        }

        img {
            width: 30px;
            height: 30px;
        }
    }
}