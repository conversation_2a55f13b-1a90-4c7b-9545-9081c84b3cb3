@if (false) {
  <div class="navbar pb-0">
    <div class="df">
      <div class="icon">
        <img class="svg_left_sidebar" src="assets/icons/user.svg" alt="">
      </div>
      <h1>
        Utilisateurs
      </h1>
    </div>
    <span class="df mt-5 ml-5">
      Vue détaillée d'un Utilisateur.
    </span>
  </div>
}
<div class="db dimensions_container">
  <form class="filters df" [formGroup]="form" (ngSubmit)="add()">
    <div class="search df">
      <mat-icon>search</mat-icon>
      <input type="text" placeholder="Recherche Utilisateur">
    </div>
    <button type="button" class="bg-color-btn df bg-active-btn">
      <mat-icon>filter_list</mat-icon>
      Filters
    </button>
    <button type="reset" class="bg-color-btn df bg-active-btn">
      Reset
    </button>
    <button type="submit" class="bg-color-btn bg-active-btn df left-btn">
      <mat-icon>add</mat-icon>
      Utilisateur
    </button>
  </form>
  <div class="table">
    <table>
      <tr>
        <th>Name</th>
        <th>Email</th>
        <th>Phone</th>
        <th>Role</th>
        <th>Actions</th>
      </tr>
      <tr>
        <td class="truncate">{{ '---' }}</td>
        <td class="truncate">{{ '---' }}</td>
        <td class="truncate">{{ '---' }}</td>
        <td class="truncate">{{ '---' }}</td>
        <td class="df truncate">
          <button>
            @if (false) {
              <mat-icon svgIcon="show-icon"></mat-icon>
            }
          </button>
          <button type="button">
            <mat-icon>edit</mat-icon>
          </button>
          <button>
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </tr>
    </table>
  </div>
  <!-- <app-pagination [pagination]="pagination" (toPageEvent)="toPage($event)"></app-pagination> -->
</div>