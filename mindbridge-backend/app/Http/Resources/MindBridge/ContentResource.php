<?php

namespace App\Http\Resources\MindBridge;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContentResource extends JsonResource
{
    public static $wrap = null;

    public function toArray(Request $request): array
    {
        return 
            [
                'id' => $this->id,
                "type" => $this->type,
                "title" => $this->title,
                "description" => $this->description,
                "content" => asset('storage/' . $this->content),
                'niveau' => $this->niveau,
                "matiere" => $this->matiere,
                "chapter" => $this->chapter
            ];
    }
}
