<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('hooty_requests', function (Blueprint $table) {
            $table->id();
            $table->string('file_path');
            $table->string('action')->nullable(); 
            $table->text('result')->nullable();
            $table->timestamps(); 
        });
    }

 
    public function down()
    {
        Schema::dropIfExists('hooty_requests');
    }
};
