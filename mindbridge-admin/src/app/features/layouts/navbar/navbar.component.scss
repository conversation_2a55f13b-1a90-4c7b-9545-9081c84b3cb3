.navbar {
    padding: 17px 28px;
    gap: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon {
        width: 40px;
        height: 45px;
        background-color: rgba(205, 205, 205, 0.15);
        border-radius: 7px;

        .svg_left_sidebar {
            width: 100%;
            height: 100%;
        }
    }

    h2 {
        font-weight: 600;
        margin: 0;
        font-size: 24px;
    }
}

.arrow {
    width: 15px;
    height: 15px;
    transform: rotate(-90deg);
}

.person {
    display: flex;
    align-items: center;
    margin-right: 15px;
    gap: 10px;
    min-width: fit-content;

    .pofile_img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
    }

    .arrow {
        width: 20px;
        height: 20px;
        margin-left: auto;
        margin-bottom: auto;
        cursor: pointer;
    }
}

.name_user {
    color: #344054;
    font-size: 14px;
    margin: 0;
    font-weight: 600;
    line-height: 20px;
}

.under_descr {
    color: #475467;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    display: block;

}
.under_descr::first-letter {
    text-transform: uppercase !important;
}