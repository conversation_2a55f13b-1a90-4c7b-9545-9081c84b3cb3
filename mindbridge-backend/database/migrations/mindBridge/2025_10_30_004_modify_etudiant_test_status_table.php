<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('etudiant_test_status', function (Blueprint $table) {
            $table->decimal('percentage', 5, 2)->nullable()->after('score');
            $table->text('feedback')->nullable()->after('percentage');
            $table->text('recommendation')->nullable()->after('feedback');
            $table->string('interpretation')->nullable()->after('recommendation');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('etudiant_test_status', function (Blueprint $table) {
            $table->dropColumn(['percentage', 'feedback', 'recommendation', 'interpretation']);
        });
    }
};

