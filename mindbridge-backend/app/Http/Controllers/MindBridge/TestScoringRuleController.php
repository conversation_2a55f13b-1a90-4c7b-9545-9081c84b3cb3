<?php

namespace App\Http\Controllers\MindBridge;

use App\Http\Controllers\Controller;
use App\Models\MindBridge\Test;
use App\Models\MindBridge\TestScoringRule;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;
use Exception;

class TestScoringRuleController extends Controller
{
    /**
     * Get all scoring rules for a test
     */
    public function index($testId)
    {
        try {
            $test = Test::findOrFail($testId);
            $rules = $test->scoringRules()->orderBy('min_percentage', 'asc')->get();

            return response()->json([
                'data' => $rules,
                'test_id' => $testId,
            ], 200);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Test not found'], 404);
        } catch (Exception $e) {
            Log::error('Error fetching scoring rules: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }

    /**
     * Create a new scoring rule for a test
     */
    public function store(Request $request, $testId)
    {
        try {
            $test = Test::findOrFail($testId);

            $validated = $request->validate([
                'min_percentage' => 'required|numeric|min:0|max:100',
                'max_percentage' => 'required|numeric|min:0|max:100|gte:min_percentage',
                'interpretation' => 'required|string|max:255',
                'feedback' => 'nullable|string',
                'recommendation' => 'nullable|string',
            ]);

            // Check for overlapping ranges
            $overlapping = TestScoringRule::where('test_id', $testId)
                ->where(function ($query) use ($validated) {
                    $query->whereBetween('min_percentage', [$validated['min_percentage'], $validated['max_percentage']])
                        ->orWhereBetween('max_percentage', [$validated['min_percentage'], $validated['max_percentage']])
                        ->orWhere(function ($q) use ($validated) {
                            $q->where('min_percentage', '<=', $validated['min_percentage'])
                                ->where('max_percentage', '>=', $validated['max_percentage']);
                        });
                })
                ->exists();

            if ($overlapping) {
                return response()->json([
                    'error' => 'This percentage range overlaps with an existing rule'
                ], 422);
            }

            $validated['test_id'] = $testId;
            $rule = TestScoringRule::create($validated);

            return response()->json([
                'message' => 'Scoring rule created successfully',
                'data' => $rule,
            ], 201);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Test not found'], 404);
        } catch (ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        } catch (Exception $e) {
            Log::error('Error creating scoring rule: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }

    /**
     * Update a scoring rule
     */
    public function update(Request $request, $testId, $ruleId)
    {
        try {
            $test = Test::findOrFail($testId);
            $rule = TestScoringRule::where('test_id', $testId)->findOrFail($ruleId);

            $validated = $request->validate([
                'min_percentage' => 'required|numeric|min:0|max:100',
                'max_percentage' => 'required|numeric|min:0|max:100|gte:min_percentage',
                'interpretation' => 'required|string|max:255',
                'feedback' => 'nullable|string',
                'recommendation' => 'nullable|string',
            ]);

            // Check for overlapping ranges (excluding current rule)
            $overlapping = TestScoringRule::where('test_id', $testId)
                ->where('id', '!=', $ruleId)
                ->where(function ($query) use ($validated) {
                    $query->whereBetween('min_percentage', [$validated['min_percentage'], $validated['max_percentage']])
                        ->orWhereBetween('max_percentage', [$validated['min_percentage'], $validated['max_percentage']])
                        ->orWhere(function ($q) use ($validated) {
                            $q->where('min_percentage', '<=', $validated['min_percentage'])
                                ->where('max_percentage', '>=', $validated['max_percentage']);
                        });
                })
                ->exists();

            if ($overlapping) {
                return response()->json([
                    'error' => 'This percentage range overlaps with an existing rule'
                ], 422);
            }

            $rule->update($validated);

            return response()->json([
                'message' => 'Scoring rule updated successfully',
                'data' => $rule,
            ], 200);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Test or rule not found'], 404);
        } catch (ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        } catch (Exception $e) {
            Log::error('Error updating scoring rule: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }

    /**
     * Delete a scoring rule
     */
    public function destroy($testId, $ruleId)
    {
        try {
            $test = Test::findOrFail($testId);
            $rule = TestScoringRule::where('test_id', $testId)->findOrFail($ruleId);

            $rule->delete();

            return response()->json([
                'message' => 'Scoring rule deleted successfully'
            ], 200);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Test or rule not found'], 404);
        } catch (Exception $e) {
            Log::error('Error deleting scoring rule: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }
}

