<div class="sidebar bg-block flex-column">
  <!-- <div class="div_Pseudo_elements"></div> -->
  <div class="logo_">
    <img class="logo" src="/assets/icones/logo-mind-bridge.jpg" alt="" />
    <span class="title_logo">MINDBRIDGE</span>
  </div>
  <div class="side_list flex-column">
    <a
      class="link_a flex-row"
      routerLink="/dashboard"
      routerLinkActive="active-route"
    >
      <div class="icn">
        <img
          class="svg_left_sidebar"
          [src]="
            isRouteActive('/dashboard')
              ? '/assets/icones/dashboard-active.png'
              : '/assets/icones/dashboard-mindbridge.png'
          "
          alt=""
        />
      </div>
      <div class="title font-btn">Tableau de bord</div>
    </a>
    <a
      class="link_a flex-row"
      routerLink="/tests"
      routerLinkActive="active-route"
    >
      <div class="icn">
        <img
          class="svg_left_sidebar"
          [src]="
            isRouteActive('/tests')
              ? '/assets/icones/test-active.png'
              : '/assets/icones/test.png'
          "
          alt=""
        />
      </div>
      <div class="title font-btn">GESTION DE TEST</div>
    </a>
    <a
      class="link_a flex-row"
      routerLink="/scoring-rules"
      routerLinkActive="active-route"
    >
      <div class="icn">
        <mat-icon
          [ngClass]="
            isRouteActive('/scoring-rules')
              ? 'text-[#6BA4BD]'
              : 'text-[#464D5C]'
          "
        >
          assessment
        </mat-icon>
      </div>
      <div class="title font-btn">RÈGLES DE NOTATION</div>
    </a>
    <a
      class="link_a flex-row"
      routerLink="/matieres"
      routerLinkActive="active-route"
    >
      <div class="icn">
        <img
          class="svg_left_sidebar"
          [src]="
            isRouteActive('/matieres')
              ? '/assets/icones/matier-active.png'
              : '/assets/icones/matier-mindbridge.png'
          "
          alt=""
        />
      </div>
      <div class="title font-btn">MATIERES</div>
    </a>
    <a
      class="link_a flex-row max-lg:flex-col"
      routerLink="/sante-mentale"
      [ngClass]="{ 'active-route': isActiveBaseRoute('/sante-mentale') }"
    >
      <div class="flex-row">
        <div class="icn">
          <img
            class="svg_left_sidebar"
            [src]="
              isActiveBaseRoute('/sante-mentale')
                ? '/assets/icones/sante_mentale.png'
                : '/assets/icones/sante_mentale_inactive.png'
            "
            alt=""
          />
        </div>
        <div class="title font-btn">SANTÉ MENTALE</div>
      </div>

      <mat-icon
        class="flex-1 pr-2 text-right text-gray-400 max-lg:pr-0.5 max-lg:text-center"
        (click)="toggleSMMenu()"
      >
        {{ isSMMenuOpen ? 'keyboard_arrow_up' : 'expand_more' }}
      </mat-icon>
    </a>
    @if (isSMMenuOpen) {
      <a
        class="link_a flex-row"
        routerLink="/sante-mentale/dashboard"
        routerLinkActive="active-route"
      >
        <div class="hidden max-lg:block">
          <div class="icn">
            <mat-icon
              [ngClass]="
                isRouteActive('/sante-mentale/dashboard')
                  ? 'text-[#6BA4BD]'
                  : 'text-[#464D5C]'
              "
            >
              dashboard
            </mat-icon>
          </div>
        </div>
        <div class="title font-btn pl-10 text-xs max-md:pl-1 max-md:text-xs">
          DASHBOARD RISQUES
        </div>
      </a>
      <a
        class="link_a flex-row"
        routerLink="/sante-mentale/test-categories"
        routerLinkActive="active-route"
      >
        <div class="hidden max-lg:block">
          <div class="icn">
            <mat-icon
              [ngClass]="
                isRouteActive('/sante-mentale/test-categories')
                  ? 'text-[#6BA4BD]'
                  : 'text-[#464D5C]'
              "
            >
              drag_indicator
            </mat-icon>
          </div>
        </div>
        <div class="title font-btn pl-10 text-xs max-md:pl-1 max-md:text-xs">
          CATÉGORIES ET TESTS
        </div>
      </a>
    }
    <a
      class="link_a flex-inline"
      routerLink="/questions-repository"
      routerLinkActive="active-route"
    >
      <div class="icn">
        <img
          class="svg_left_sidebar"
          [src]="
            isRouteActive('/questions-repository')
              ? '/assets/icones/questions-repository-active.png'
              : '/assets/icones/questions-repository.png'
          "
          alt=""
        />
      </div>
      <div class="title font-btn">
        Référentiel de
        <br />
        questions
      </div>
    </a>
    <a
      class="link_a flex-row"
      routerLink="/observations"
      routerLinkActive="active-route"
    >
      <div class="icn">
        <mat-icon
          [ngClass]="
            isRouteActive('/observations')
              ? 'text-[#6BA4BD]'
              : 'text-[#464D5C]'
          "
        >
          visibility
        </mat-icon>
      </div>
      <div class="title font-btn">OBSERVATIONS</div>
    </a>
    <a
      class="link_a flex-row"
      routerLink="/etudiants"
      routerLinkActive="active-route"
    >
      <div class="icn">
        <img
          class="svg_left_sidebar"
          [src]="
            isRouteActive('/etudiants')
              ? '/assets/icones/etudiant-active.png'
              : '/assets/icones/etudiant.png'
          "
          alt=""
        />
      </div>
      <div class="title font-btn">LISTE DES ÉTUDIANTS</div>
    </a>
    @if (false) {
      <a
        class="link_a flex-row"
        routerLink="/users"
        routerLinkActive="active-route"
      >
        <div class="icn">
          <mat-icon class="svg_left_sidebar">person</mat-icon>
        </div>
        <div class="title font-btn">Utilisateur</div>
      </a>
    }
    @if (false) {
      <a
        class="link_a flex-row"
        routerLink="/role-permission"
        routerLinkActive="active-route"
      >
        <div class="icn">
          <mat-icon class="svg_left_sidebar">settings</mat-icon>
        </div>
        <div class="title font-btn">Permissions de rôle</div>
      </a>
    }

    <a class="link_a flex-row" (click)="logout()">
      <div class="log_out">
        <span>Log out</span>
        <img
          class="svg_left_sidebar"
          src="../../assets/icones/log-out-mindbridge.png"
          alt=""
        />
      </div>
      @if (false) {
        <div class="icn">
          <img
            class="svg_left_sidebar"
            src="../../assets/icones/person-placeholder.png"
            alt=""
          />
        </div>
      }
      @if (false) {
        <div class="title end_btn flex-row">
          <div class="name_email">
            <div class="">--- ---</div>
            <div class="font-small">---</div>
          </div>
          <div class="icn logout_btn">
            <mat-icon class="logout">logout</mat-icon>
          </div>
        </div>
      }
    </a>
  </div>
</div>
