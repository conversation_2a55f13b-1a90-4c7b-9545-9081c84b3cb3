import {
  Component,
  computed,
  effect,
  inject,
  input,
  InputSignal,
  OnInit,
  Signal,
  untracked,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { MatieresStore } from '../../matieres.store';
import {
  MatiereResponse,
  NiveauMini,
} from '../../models/matiere-response.model';
import { ContenueResponse } from '../../models/contenue-response.model';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../core/app.state';
import { SetLoading } from '../../../shared/store/shared.actions';
import { NgStyle } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import {
  EtudiantSearchFormGroup,
  IEtudiantSearchForm,
  IEtudiantSearchFormGroup,
} from '../../../etudiants/models/etudiant-search-request';
import {
  ContentSearchFormGroup,
  IContentSearchForm,
  IContentSearchFormGroup,
} from '../../models/content-search-request';
import { OPTION_ALL } from '../../../shared/global.config';
import { ChapterResponse } from '../../models/chapter-response.model';
import { Observable } from 'rxjs';
import { ChapterRequest } from '../../models/chapter-request.model';
import { TestCategoryEnum } from '../../../test-management/models/test-category-enum';

@Component({
  selector: 'app-list-contenues',
  templateUrl: './list-contenues.component.html',
  styleUrl: './list-contenues.component.scss',
  providers: [MatieresStore],
  imports: [RouterLink, MatIcon, NgStyle, FormsModule, ReactiveFormsModule],
})
export class ListContenuesComponent implements OnInit {
  private store = inject<Store<AppState>>(Store);
  private router = inject(Router);

  private fb = inject(FormBuilder);

  form: FormGroup<IContentSearchFormGroup> = this.fb.group(
    new ContentSearchFormGroup()
  );

  #matieresStore = inject(MatieresStore);
  matiereContents: Signal<ContenueResponse[]> =
    this.#matieresStore.state.contenues;
  matieres: Signal<MatiereResponse[]> = this.#matieresStore.state.matieres;
  matiereId: InputSignal<number> = input.required<number>();

  contentsSearchForm: Signal<IContentSearchForm> =
    this.#matieresStore.state.contentsSearchForm;

  chaptersByMatiere: Signal<ChapterResponse[]> =
    this.#matieresStore.state.chapters;
  allNiveaux: Signal<NiveauMini[]> = this.#matieresStore.state.niveaux;
  current_page: Signal<number> = this.#matieresStore.state.current_page;
  total_pages: Signal<number> = computed(() =>
    Math.max(this.#matieresStore.state.total_pages(), 1)
  );

  matiereSelected: MatiereResponse | undefined;
  matiereName: string | undefined;

  chapterRequest: ChapterRequest;

  constructor() {
    effect(() => {
      const searchFormValue = this.contentsSearchForm();
      untracked(() => this.form.patchValue(searchFormValue));
    });
    effect(() => {
      if (this.matiereContents()) {
        this.#matieresStore.getMatieres(null);
        this.matiereSelected = this.matiereById(Number(this.matiereId()));
        this.matiereName = this.matiereSelected?.name_fr;
      }
    });
  }

  ngOnInit() {
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.searchContents();
    this.#matieresStore.getAllNiveaux();
    this.chapterRequest = {
      matiereId: Number(this.matiereId()),
    };
    this.#matieresStore.chapterByMatiereAndNiveau(this.chapterRequest);
  }

  searchContents() {
    this.#matieresStore.setSearchForm(this.form.getRawValue());
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.#matieresStore.matiereContents(this.matiereId());
  }

  goToPage(page: number) {
    this.#matieresStore.setCurrentPage(page);
    this.searchContents();
  }

  matiereById(id: number): MatiereResponse | undefined {
    return this.matieres().find((m) => m.id === id);
  }

  getContenus(matiereId: any) {
    this.router.navigate(['/add-contenue', matiereId]);
  }

  protected readonly OPTION_ALL = OPTION_ALL;
  protected readonly Number = Number;

  create_test(contenue: ContenueResponse) {
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['/add-test'], {
        queryParams: {
          type: TestCategoryEnum.TestContenu,
          niveau_id: contenue.niveau.id,
          matiere_id: this.matiereId(),
          content_id: contenue.id,
        },
      })
    );
    window.open(url, '_blank');
  }
}
