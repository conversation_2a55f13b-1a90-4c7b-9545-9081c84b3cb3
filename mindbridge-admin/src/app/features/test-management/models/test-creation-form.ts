import { TypeQuestionEnum } from './type-question.enum';
import { DifficultyLevelEnum } from './difficulty-level-enum';

export class TestCreationRequestWrapper {
  test: TestCreationRequest;
  steps: TestCreationRequestStep[];

  constructor() {
    this.test = new TestCreationRequest();
    this.steps = [];
  }
}

export class TestCreationRequest {
  id: number;
  title: string;
  description: string;
  category_id: number;
  matiere_id: number;
  niveau_id: number;
  content_id: number;
  timer: number;
  challenge_date: Date | string;
  difficulty_level: DifficultyLevelEnum;

  constructor() {
    this.id = null;
    this.title = '';
    this.description = '';
    this.category_id = null;
    this.matiere_id = null;
    this.niveau_id = null;
    this.content_id = null;
    this.timer = null;
    this.challenge_date = null;
    this.difficulty_level = null;
  }
}

export class TestCreationRequestStep {
  id: number;
  type: TypeQuestionEnum;
  order: number;
  question?: TestCreationRequestQuestion;

  constructor() {
    this.id = null;
    this.type = null;
    this.order = null;
    this.question = undefined;
  }
}

export class TestCreationRequestQuestion {
  id: number;
  type: string;
  content: string;
  description: string;
  image_path: Blob | string;
  required: boolean;
  answer?: boolean;
  is_true?: boolean;
  is_false?: boolean;
  options?: TestCreationRequestQuestionOption[];

  constructor() {
    this.id = null;
    this.type = 'text';
    this.content = '';
    this.description = '';
    this.image_path = '';
    this.required = true;
    this.options = [new TestCreationRequestQuestionOption()];
  }
}

export class TestCreationRequestQuestionOption {
  id: number;
  name: string;
  icon?: string;
  isCorrect?: boolean;

  constructor() {
    this.id = null;
    this.name = '';
    this.icon = null;
    this.isCorrect = false;
  }
}
