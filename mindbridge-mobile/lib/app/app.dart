import 'package:get/get.dart';
import 'package:mindbridge/presentation/screens/home/<USER>/evaluation_param.dart';
import 'package:mindbridge/presentation/screens/recommandation/view/recommandation_view.dart';

import '../presentation/screens/complete_profile/view/complete_profile_view.dart';
import '../presentation/screens/congratulations/view/congratulations_view.dart';
import '../presentation/screens/espace_perso/view/espace_perso.dart';
import '../presentation/screens/evaluation/view/evaluation_view.dart';
import '../presentation/screens/home/<USER>/home_view.dart';
import '../presentation/screens/lesson/view/lesson_view.dart';
import '../presentation/screens/login/view/login_view.dart';
import '../presentation/screens/select_lesson/view/select_lesson.dart';
import '../presentation/screens/select_subject/view/select_subject_view.dart';
import '../presentation/screens/splash/view/init_view.dart';
import '../presentation/screens/splash/view/splash_view.dart';
import '../presentation/widgets/btm_navigation_bar.dart';
import 'bindings.dart';
import 'routes.dart';

class AppPages {
  static final routes = [
    GetPage(
      name: Routes.init,
      page: () => const InitScreen(),
      binding: InitialBinding(),
    ),
    GetPage(
      name: Routes.splash,
      page: () => SplashView(),
    ),
    GetPage(
      name: Routes.login,
      page: () => LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: Routes.completeProfile,
      page: () => CompleteProfileView(),
      binding: CompleteProfileBinding(),
    ),
    GetPage(
      name: Routes.congratulations,
      page: () => const CongratulationsView(),
    ),
    GetPage(name: Routes.btmNavigationBar, page: () => BaseScreen(), bindings: [
      BaseScreenBinding(),
      InitialBinding(),
    ]),
    GetPage(
      name: Routes.home,
      page: () => const HomeView(),
    ),
    GetPage(
        name: Routes.selectSubject,
        page: () => SelectSubjectView(categoryId: Get.arguments as int),
        binding: SubjectBinding()),
    GetPage(
      name: Routes.selectLesson,
      page: () => SelectLessonView(
        subjectTitle: Get.arguments['subjectTitle'] as String?,
        matiereId: Get.arguments['matiereId'] as int,
      ),
      binding: LessonBinding(),
    ),
    GetPage(
      name: Routes.lesson,
      page: () => const LessonView(),
    ),
    GetPage(
        name: Routes.evaluation,
        page: () => EvaluationView(param: Get.arguments as EvaluationParam),
        binding: EvaluationBinding()),
    GetPage(
      name: Routes.espacePersonnel,
      page: () => const EspacePersonnel(),
    ),
    GetPage(
      name: Routes.recommandation,
      page: () => const RecommandationView(),
      binding: RecommendationBinding(),
    ),
  ];
}
