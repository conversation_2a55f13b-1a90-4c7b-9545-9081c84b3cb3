import { Ni<PERSON>au, School } from '../../etudiants/models/etudiant-response';

export interface ParticipantResponse {
  id: number;
  etudiant: Etudiant;
  test_title: string;
  category: MentalHealthTestsCategory;
  date_creation: string;
  comment: string;
  test_status: string;
  student_score: number;
}

export interface MentalHealthTestsCategory {
  id: number;
  parent_id: number;
  name: string;
  description: string;
  button_text: string;
  gradient_background: string;
  gradient_border: string;
  icon: string;
  image_url: string;
  action_type: string;
  is_mobile: number;
  is_bo: number;
  is_active: number;
  code: string;
  position: number;
  count: number;
  created_at: Date;
  updated_at: Date;
}

export interface Etudiant {
  id: number;
  name: string;
  first_name: string;
  last_name: string;
  email: string;
  avatar: string;
  identifiant: string;
  heure_moyen: number;
  taux_engagement: number;
  nni: number;
  test_profiling_completed: string;
  scores: number;
  name_gamification: string;

  niveau: Niveau;
  school: School;
  classement: number;
  count_etudiant: number;
}
