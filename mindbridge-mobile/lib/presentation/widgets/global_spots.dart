// lib/presentation/widgets/global_spots.dart
import 'package:flutter/material.dart';

import '../../../core/utilities/app_images.dart';

class GlobalSpots extends StatelessWidget {
  final double? width;
  final double? height;

  const GlobalSpots({
    super.key,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final w = width ?? MediaQuery.of(context).size.width;
    final h = height ?? MediaQuery.of(context).size.height;

    return SizedBox(
      width: w,
      height: h,
      child: Stack(
        children: [
          Positioned(
            top: h * 0.05,
            right: 0,
            child: Image.asset(
              AppImages.circleSpot,
              width: w * 0.4,
              height: h * 0.4,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Image.asset(
              AppImages.mainSpot,
              width: w,
              height: h * 0.5,
              fit: BoxFit.cover,
            ),
          ),
        ],
      ),
    );
  }
}
