<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class UserMessageState extends Model
{
    use HasFactory;

    protected $connection = 'mind_bridge';

    protected $table = 'user_message_states';

    protected $fillable = [
        'user_id',
        'category',
        'sub_category',
        'last_index'
  
    ];
    
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
