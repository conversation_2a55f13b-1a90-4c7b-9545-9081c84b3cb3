import { Component, inject } from '@angular/core';
import { Router, RouterLinkActive, RouterLink } from '@angular/router';
import { MatIcon } from '@angular/material/icon';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
  imports: [RouterLinkActive, RouterLink, MatIcon, NgClass],
})
export class SidebarComponent {
  private router = inject(Router);

  isRouteActive(route: string): boolean {
    return this.router.url === route;
  }

  logout() {
    localStorage.clear();
    this.router.navigate(['/login']);
  }

  isSMMenuOpen = false;

  toggleSMMenu() {
    this.isSMMenuOpen = !this.isSMMenuOpen;
  }

  isActiveBaseRoute(baseRoute: string): boolean {
    return this.router.url.startsWith(baseRoute);
  }
}
