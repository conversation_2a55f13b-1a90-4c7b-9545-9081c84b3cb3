import { createAction, props } from "@ngrx/store";
import { AuthRequest } from "../models/auth-request";
import { AuthResponse } from "../models/auth-response";

export const LoginAction = createAction(
    '[ Auth ] - Login',
    props<{ payload: AuthRequest }>()
  );
  export const LoginActionSuccess = createAction(
    '[ Auth ] - Login Success ',
    props<{ payload: AuthResponse }>()
  );
  
  export const LoginActionError = createAction(
    '[ Auth ] - Login Error',
);