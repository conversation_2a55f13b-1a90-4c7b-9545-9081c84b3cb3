import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AuthRequest } from '../models/auth-request';
import { Observable } from 'rxjs';
import { AuthResponse } from '../models/auth-response';
import { LocalStorageService } from '../../shared/localstorage.service';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private http = inject(HttpClient);


  login(payload: AuthRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(
      environment.BASE_URL_API + 'mind_bridge/auth/login',
      payload
    );
  }

  getAuthStatus() {
    const currentUser = LocalStorageService.get('currentUser');
    return currentUser.token ? true : false;
  }
}
