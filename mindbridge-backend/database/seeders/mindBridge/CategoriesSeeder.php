<?php

namespace Database\Seeders\mindBridge;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //les categories
        DB::connection('mind_bridge')->table('categories')->insert([
            [
                'name' => 'MODÈLES DE TESTS',// id = 1
            ],
            [
                'name' => 'SANTÉ MENTALE',// id = 2
            ],
        ]);


        //les subcategories de la categorie 1 [MODÈLES DE TESTS] BO seulement
        DB::connection('mind_bridge')->table('categories')->insert([
            // BO seulement
            [
                'name' => 'Test profiling', // id = 3
                'parent_id' => 1,
                'icon' => '/mindBridge/test_profiling_icon.png',
                'code' => 'test_profiling',
                'action_type' => 'test',
                'image_url' => null,
                'button_text' => null,
                'gradient_background' => null,
                'gradient_border' => null,
                'description' => 'Un test de profilage conçu pour évaluer les compétences et les aptitudes des utilisateurs dans divers domaines.',
                'is_mobile' => false,
                'is_bo' => true,
                'is_active' => true,
                'position' => 0,
                'count' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Test de contenu', // id = 4
                'parent_id' => 1,
                'icon' => '/mindBridge/test_contenu_icon.png',
                'code' => 'test_contenu',
                'action_type' => 'test',
                'image_url' => null,
                'button_text' => null,
                'gradient_background' => null,
                'gradient_border' => null,
                'description' => 'Évaluez votre compréhension et vos connaissances grâce à notre test de contenu détaillé.',
                'is_mobile' => false,
                'is_bo' => true,
                'is_active' => true,
                'position' => 0,
                'count' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // BO & Étudiant
            [
                'name' => 'Examen simulé', // id = 5
                'parent_id' => 1,
                'icon' => '/mindBridge/exament_simuler_icon.png',
                'image_url' => '/mindBridge/exament_simuler_image.png',
                'code' => 'examen_simule',
                'action_type' => 'test',
                'button_text' => 'Commencer le test',
                'gradient_background' => json_encode(["0xffD3E4EB", "0xff568397"]),
                'gradient_border' => json_encode(["0xffD3E4EB", "0xff568397"]),
                'description' => 'Préparez-vous efficacement avec nos examens simulés qui reproduisent les conditions réelles d\'évaluation.',
                'is_mobile' => true,
                'is_bo' => true,
                'is_active' => true,
                'position' => 5,
                'count' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Quiz culture générale', // id = 6
                'parent_id' => 1,
                'icon' => '/mindBridge/quiz_culture_generale_icon.png',
                'image_url' => '/mindBridge/quiz_culture_generale_image.png',
                'code' => 'quiz_culture_generale',
                'action_type' => 'test',
                'button_text' => 'Passer le quiz',
                'gradient_background' => json_encode(["0xffF3FCFD", "0xffECFAFD"]),
                'gradient_border' => json_encode([]),
                'description' => 'Testez vos connaissances générales avec notre quiz interactif et amusant.',
                'is_mobile' => true,
                'is_bo' => true,
                'is_active' => true,
                'position' => 2,
                'count' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // BO seulement
            [
                'name' => 'Sondage test', // id = 7
                'parent_id' => 1,
                'icon' => '/mindBridge/sondage_test_icon.png',
                'code' => 'sondage_test',
                'action_type' => 'test',
                'image_url' => null,
                'button_text' => null,
                'gradient_background' => null,
                'gradient_border' => null,
                'description' => 'Participez à notre sondage pour nous aider à améliorer nos services et produits.',
                'is_mobile' => false,
                'is_bo' => true,
                'is_active' => true,
                'position' => 0,
                'count' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
    
            // BO & Étudiant
            
            [
                'name' => 'Challenge Hebdomadaire', // id = 8
                'parent_id' => 1,
                'icon' => '/mindBridge/challenge_hebdomadaire_icon.png',
                'image_url' => '/mindBridge/challenge_hebdomadaire_image.png',
                'code' => 'challenge_hebdomadaire',
                'action_type' => 'test',
                'button_text' => 'Commencer le test',
                'gradient_background' => json_encode(["0xffD3E4EB", "0xff406271"]),
                'gradient_border' => json_encode(["0xffD3E4EB", "0xff406271"]),
                'description' => 'Relevez nos défis hebdomadaires pour améliorer vos compétences et gagner des récompenses.',
                'is_mobile' => true,
                'is_bo' => true,
                'is_active' => true,
                'position' => 1,
                'count' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
    
            // Étudiant seulement
            [
                'name' => 'Choisir ta leçon', // id = 9
                'parent_id' => 1,
                'icon' => null,
                'image_url' => '/mindBridge/choisir_ta_lecon_image.png',
                'code' => 'choisir_ta_lecon',
                'action_type' => 'lecon',
                'button_text' => 'Commencer le test',
                'gradient_background' => json_encode(["0xffCBBCD2", "0xff7E588E"]),
                'gradient_border' => json_encode(["0xffCBBCD2", "0xff7E588E"]),
                'description' => 'Sélectionnez et commencez vos leçons personnalisées pour un apprentissage optimal.',
                'is_mobile' => true,
                'is_bo' => false,
                'is_active' => true,
                'position' => 4,
                'count' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Recommandation MindBridge', // id = 10
                'parent_id' => 1,
                'icon' => null,
                'image_url' => '/mindBridge/recommandation_mindbridge_image.png',
                'code' => 'recommandation_mindbridge',
                'action_type' => 'lecon',
                'button_text' => 'Commencer le test',
                'gradient_background' => json_encode(["0xffD3E4EB", "0xff406271"]),
                'gradient_border' => json_encode(["0xffD3E4EB", "0xff406271"]),
                'description' => 'Recevez des recommandations personnalisées pour améliorer votre parcours d\'apprentissage.',
                'is_mobile' => true,
                'is_bo' => false,
                'is_active' => true,
                'position' => 3,
                'count' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);



        //les subcategories de la categorie 2 [SANTÉ MENTALE]
        DB::connection('mind_bridge')->table('categories')->insert([
            [
                'name' => 'Cognitifs',// id = 11
                'parent_id' => 2,
                'icon' => '/mindBridge/cognitifs_icon.png',
                'image_url' => '/mindBridge/cognitifs_icon.png',
                'code' => 'cognitifs',
            ],
            [
                'name' => 'Neuro Développement',// id = 12
                'parent_id' => 2,
                'icon' => '/mindBridge/neuro_developpement_icon.png',
                'image_url' => '/mindBridge/neuro_developpement_icon.png',
                'code' => 'neuro_developpement',
            ],
            [
                'name' => 'Apprentissage',// id = 13
                'parent_id' => 2,
                'icon' => '/mindBridge/apprentissage_icon.png',
                'image_url' => '/mindBridge/apprentissage_icon.png',
                'code' => 'apprentissage',
            ],
            [
                'name' => 'Personnalité',// id = 14
                'parent_id' => 2,
                'icon' => '/mindBridge/personnalite_icon.png',
                'image_url' => '/mindBridge/personnalite_icon.png',
                'code' => 'personnalite',
            ],
            [
                'name' => 'Émotionnel et comportemental',// id = 15
                'parent_id' => 2,
                'icon' => '/mindBridge/emotionnel_et_comportemental_icon.png',
                'image_url' => '/mindBridge/emotionnel_et_comportemental_icon.png',
                'code' => 'emotionnel_et_comportemental',
            ],
            [
                'name' => 'Motivation et estime de soi',// id = 16
                'parent_id' => 2,
                'icon' => '/mindBridge/attention_executif_icon.png',
                'image_url' => '/mindBridge/attention_executif_icon.png',
                'code' => 'motivation_et_estime_de_soi',
            ],
            [
                'name' => 'Attention/exécutif',// id = 17
                'parent_id' => 2,
                'icon' => '/mindBridge/attention_executif_icon.png',
                'image_url' => '/mindBridge/attention_executif_icon.png',
                'code' => 'attention_executif',
            ],
            [
                'name' => 'Relations familiales/sociales',// id = 18
                'parent_id' => 2,
                'icon' => '/mindBridge/relations_familiales_sociales_icon.png',
                'image_url' => '/mindBridge/relations_familiales_sociales_icon.png',
                'code' => 'relations_familiales_sociales',
            ]
        ]);

        DB::connection('mind_bridge')->table('categories')->insert([
            [
                'name'               => 'Mémoire et attention', // id = 19
                'parent_id'          => 11,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'memoire_et_attention',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'description'        => null,
                'is_mobile'          => false,
                'is_bo'              => true,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
            [
                'name'               => 'Résolution de problèmes', // id = 20
                'parent_id'          => 11,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'resolution_de_problemes',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'description'        => null,
                'is_mobile'          => false,
                'is_bo'              => true,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
        ]);

        // Under parent id 12 (Neuro Développement)
        DB::connection('mind_bridge')->table('categories')->insert([
            [
                'name'               => 'Développement moteur', // id = 21
                'description'        => 'Évaluation et amélioration des capacités motrices chez l\'enfant.',
                'parent_id'          => 12,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'developpement_moteur',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
            [
                'name'               => 'Développement du langage', // id = 22
                'description'        => 'Stratégies pour stimuler l\'évolution du langage et de la communication.',
                'parent_id'          => 12,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'developpement_du_langage',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
        ]);

        // Under parent id 13 (Apprentissage)
        DB::connection('mind_bridge')->table('categories')->insert([
            [
                'name'               => 'Stratégies d\'apprentissage', // id = 23
                'description'        => 'Méthodes pédagogiques pour optimiser l\'acquisition de connaissances.',
                'parent_id'          => 13,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'strategies_apprentissage',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
            [
                'name'               => 'Motivation scolaire', // id = 24
                'description'        => 'Techniques pour encourager l\'engagement et la réussite scolaire.',
                'parent_id'          => 13,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'motivation_scolaire',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
        ]);

        // Under parent id 14 (Personnalité)
        DB::connection('mind_bridge')->table('categories')->insert([
            [
                'name'               => 'Évaluation de la personnalité', // id = 25
                'description'        => 'Outils et tests pour analyser et comprendre la personnalité.',
                'parent_id'          => 14,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'evaluation_personnalite',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
            [
                'name'               => 'Développement personnel', // id = 26
                'description'        => 'Approches visant à renforcer l\'estime de soi et la confiance individuelle.',
                'parent_id'          => 14,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'developpement_personnel',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
        ]);

        // Under parent id 15 (Émotionnel et comportemental)
        DB::connection('mind_bridge')->table('categories')->insert([
            [
                'name'               => 'Régulation émotionnelle', // id = 27
                'description'        => 'Techniques pour gérer efficacement les émotions et le stress.',
                'parent_id'          => 15,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'regulation_emotionnelle',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
            [
                'name'               => 'Comportements adaptatifs', // id = 28
                'description'        => 'Stratégies pour favoriser des comportements sociaux positifs.',
                'parent_id'          => 15,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'comportements_adaptatifs',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
        ]);

        // Under parent id 16 (Motivation et estime de soi)
        DB::connection('mind_bridge')->table('categories')->insert([
            [
                'name'               => 'Techniques de motivation', // id = 29
                'description'        => 'Méthodes pour stimuler la motivation personnelle et académique.',
                'parent_id'          => 16,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'techniques_de_motivation',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
            [
                'name'               => 'Renforcement de l\'estime de soi', // id = 30
                'description'        => 'Approches pour développer une image de soi positive et résiliente.',
                'parent_id'          => 16,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'renforcement_estime_de_soi',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
        ]);

        // Under parent id 17 (Attention/exécutif)
        DB::connection('mind_bridge')->table('categories')->insert([
            [
                'name'               => 'Contrôle attentionnel', // id = 31
                'description'        => 'Exercices et techniques pour améliorer le focus et la concentration.',
                'parent_id'          => 17,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'controle_attentionnel',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
            [
                'name'               => 'Fonctions exécutives', // id = 32
                'description'        => 'Stratégies pour optimiser la planification, l’organisation et la prise de décision.',
                'parent_id'          => 17,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'fonctions_executives',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
        ]);

        // Under parent id 18 (Relations familiales/sociales)
        DB::connection('mind_bridge')->table('categories')->insert([
            [
                'name'               => 'Communication familiale', // id = 33
                'description'        => 'Outils pour renforcer la communication et les liens au sein de la famille.',
                'parent_id'          => 18,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'communication_familiale',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
            [
                'name'               => 'Interactions sociales', // id = 34
                'description'        => 'Techniques pour développer et maintenir des relations sociales saines.',
                'parent_id'          => 18,
                'icon'               => null,
                'image_url'          => null,
                'code'               => 'interactions_sociales',
                'button_text'        => null,
                'gradient_background'=> null,
                'gradient_border'    => null,
                'is_mobile'          => false,
                'is_bo'              => false,
                'is_active'          => true,
                'position'           => 0,
                'count'              => 0,
                'created_at'         => now(),
                'updated_at'         => now(),
            ],
        ]);
    }
}