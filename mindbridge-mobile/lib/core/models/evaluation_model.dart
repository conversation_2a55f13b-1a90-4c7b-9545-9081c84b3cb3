class EvaluationQuestion {
  final int id;
  final String question;
  final List<EvaluationChoice> choices;

  EvaluationQuestion({
    required this.id,
    required this.question,
    required this.choices,
  });
}

class EvaluationChoice {
  final int id;
  final String text;
  final bool isCorrect;

  EvaluationChoice({
    required this.id,
    required this.text,
    required this.isCorrect,
  });
}
