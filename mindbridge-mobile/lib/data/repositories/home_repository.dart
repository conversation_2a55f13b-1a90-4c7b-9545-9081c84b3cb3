import '../../../core/constants/api_routes.dart';
import '../../../core/models/profile.dart';
import '../../core/models/category.dart';
import '../providers/remote/dio_client.dart';

class CategoryResponse {
  final Category? data;
  final bool isEmpty;
  final bool isError;

  CategoryResponse({
    this.data,
    this.isEmpty = false,
    this.isError = false,
  });
}

class HomeRepository {
  final DioClient _dioClient;

  HomeRepository({required DioClient dioClient}) : _dioClient = dioClient;

  Future<Profile?> fetchStudentInfo(int userId) async {
    try {
      final response = await _dioClient.dio.get(
        '${ApiRoutes.etudiantInfo}/$userId',
      );

      if (response.statusCode == 200 && response.data != null) {
        return Profile.fromJson(response.data);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<CategoryResponse> fetchCategories(String? testID) async {
    try {
      String url = ApiRoutes.categoryInfo;

      if (testID != null) {
        url += '?test_id=$testID';
      }

      final response = await _dioClient.dio.get(url);

      if (response.statusCode == 200 && response.data != null) {
        final cat = Category.fromJson(response.data);

        final children = cat.testModels?.children ?? [];
        final testToDo = cat.testToDo;
        final sondagesToDo = cat.sondagesToDo;

        final isCategoriesEmpty = children.isEmpty &&
            testToDo == null &&
            sondagesToDo == null &&
            (cat.welcomeMessage == null || cat.welcomeMessage!.isEmpty);

        if (isCategoriesEmpty) {
          return CategoryResponse(isEmpty: true);
        } else {
          return CategoryResponse(data: cat);
        }
      }
    } catch (e) {
      print("Error fetching categories: $e");
      return CategoryResponse(isError: true);
    }
    return CategoryResponse(isError: true);
  }
}
