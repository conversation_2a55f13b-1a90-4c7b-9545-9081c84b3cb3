import 'package:flutter/material.dart';

class AvatarClipper extends CustomClipper<Path> {
  final bool fromTop;

  AvatarClipper({required this.fromTop});

  @override
  Path getClip(Size size) {
    final double avatarRadius = size.width * 0.13;
    final double halfWidth = size.width / 2;
    const double notchRadius = 16.0;

    Path path = Path();

    if (fromTop) {
      path
        ..moveTo(0, 0)
        ..lineTo(halfWidth - avatarRadius - notchRadius, 0)
        ..arcToPoint(
          Offset(halfWidth - avatarRadius, notchRadius),
          radius: const Radius.circular(notchRadius),
          clockwise: true,
        )
        ..arcToPoint(
          Offset(halfWidth + avatarRadius, notchRadius),
          radius: Radius.circular(avatarRadius),
          clockwise: false,
        )
        ..arcToPoint(
          Offset(halfWidth + avatarRadius + notchRadius, 0),
          radius: const Radius.circular(notchRadius),
          clockwise: true,
        )
        ..lineTo(size.width, 0)
        ..lineTo(size.width, size.height)
        ..lineTo(0, size.height)
        ..close();
    } else {
      path
        ..moveTo(0, size.height)
        ..lineTo(halfWidth - avatarRadius - notchRadius, size.height)
        ..arcToPoint(
          Offset(halfWidth - avatarRadius, size.height - notchRadius),
          radius: const Radius.circular(notchRadius),
          clockwise: false,
        )
        ..arcToPoint(
          Offset(halfWidth + avatarRadius, size.height - notchRadius),
          radius: Radius.circular(avatarRadius),
          clockwise: true,
        )
        ..arcToPoint(
          Offset(halfWidth + avatarRadius + notchRadius, size.height),
          radius: const Radius.circular(notchRadius),
          clockwise: false,
        )
        ..lineTo(size.width, size.height)
        ..lineTo(size.width, 0)
        ..lineTo(0, 0)
        ..close();
    }

    return path;
  }

  @override
  bool shouldReclip(AvatarClipper oldClipper) => false;
}
