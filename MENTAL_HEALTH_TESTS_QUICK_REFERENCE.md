# Mental Health Tests - Quick Reference Guide

## 🎯 Quick Navigation

### Key Files
- **Backend Model:** `mindbridge-backend/app/Models/MindBridge/Test.php`
- **Backend Controller:** `mindbridge-backend/app/Http/Controllers/MindBridge/MindBridgeTestController.php`
- **Admin Service:** `mindbridge-admin/src/app/features/sante-mentale/sante-mentale.service.ts`
- **Admin Store:** `mindbridge-admin/src/app/features/sante-mentale/sante-mentale.store.ts`
- **Mobile Repository:** `mindbridge-mobile/lib/data/repositories/home_repository.dart`
- **Mobile Widget:** `mindbridge-mobile/lib/presentation/widgets/test_btm_sheet.dart`

---

## 📊 Database Tables

| Table | Purpose | Key Fields |
|-------|---------|-----------|
| `tests` | Test metadata | id, title, category_id, type, timer, difficulty_level |
| `categories` | Test categories (hierarchical) | id, parent_id, name, is_mobile, is_bo |
| `steps` | Test questions | id, test_id, question_id, order |
| `questions` | Question content | id, type, content, is_required |
| `options` | Answer choices | id, question_id, option_text, is_correct |
| `etudiant_test_status` | Student test completion | id, etudiant_id, test_id, status, score |
| `etudiant_test_answers` | Student answers | id, etudiant_id, test_id, question_id, selected_options |
| `etudiant_test_assignments` | Test assignments | id, etudiant_id, test_id, status |

---

## 🔌 API Endpoints

### Admin Panel
```
GET  /api/mind_bridge/categories/board
GET  /api/mind_bridge/categories/{id}/tests
GET  /api/mind_bridge/mental-health-tests/participants
POST /api/mind_bridge/run_test
POST /api/mind_bridge/assign-test/{etudiantId}/{testId}
```

### Mobile App
```
GET  /api/mind_bridge/etudiant/categories/board
POST /api/mind_bridge/etudiant/tests
```

---

## 🏗️ Category Hierarchy

```
Root
├── Test Models (ID: 1)
│   ├── Test Profiling (ID: 3)
│   └── Diagnostic (ID: 4)
└── Sante Mentale (ID: 2)
    ├── Cognitive Tests (ID: 5)
    ├── Emotional Tests (ID: 6)
    ├── Behavioral Tests (ID: 7)
    └── Social Tests (ID: 8)
```

---

## 🔄 Data Flow

### Student Takes Test
1. **Home Screen:** `HomeRepository.fetchCategories()` → Returns `mentalHealthToDo`
2. **Test Display:** `TestBtmSheet` widget shows test intro
3. **Answer Questions:** Student selects options for each question
4. **Submit:** `EvaluationRepository.submitEvaluation()` → POST to `/etudiant/tests`
5. **Backend Processing:**
   - Validate test and student
   - Calculate score
   - Create `EtudiantTestStatus` (TERMINE)
   - Create `EtudiantTestAnswers` for each question
   - Award points/badges
6. **Response:** Return feedback message with points
7. **UI Update:** Show success dialog, clear test from home

### Admin Views Results
1. **Dashboard:** Fetch categories with `GET /categories/board`
2. **Select Category:** Fetch tests with `GET /categories/{id}/tests`
3. **View Participants:** Fetch results with `GET /mental-health-tests/participants`
4. **Filter:** By category, level, student name
5. **Assign Test:** POST to `/assign-test/{etudiantId}/{testId}`

---

## 📱 Mobile App Models

```dart
Category
├── testModels: TestModels
├── welcomeMessage: String
├── testToDo: ToDo
├── sondagesToDo: ToDo
└── mentalHealthToDo: ToDo

ToDo (Test)
├── id: int
├── title: String
├── description: String
├── timer: int
├── type: String
└── steps: List<Step>

Step
├── id: int
├── order: int
└── question: Question

Question
├── id: int
├── type: String
├── content: String
└── options: List<Option>

Option
├── id: int
├── optionText: String
└── isCorrect: bool
```

---

## 🛠️ Common Tasks

### Add New Mental Health Test
1. Create test in admin panel
2. Set `category_id` to mental health subcategory (5-8)
3. Add steps with questions
4. Set `is_mobile = true` for mobile visibility
5. Assign to students

### Fetch Mental Health Tests (Backend)
```php
$tests = Test::where('category_id', 5)  // Cognitive tests
    ->where('niveau_id', $studentLevel)
    ->whereDoesntHave('etudiantTestStatuses', function($q) use ($studentId) {
        $q->where('etudiant_id', $studentId)->where('status', 'TERMINE');
    })
    ->get();
```

### Submit Test Answers (Mobile)
```dart
final payload = {
  'test_id': 1,
  'data': [
    {'question_id': 1, 'selected_options': [1, 2]},
    {'question_id': 2, 'selected_options': [3]},
  ]
};
await evaluationRepository.submitEvaluation(payload);
```

### Get Student Results (Admin)
```typescript
this.santeMentaleService.getListeParticipants(
  { category_id: 5, niveau: 1, etudiant: '' },
  { page: 1, per_page: 10 }
).subscribe(response => {
  console.log(response.data); // Participant results
});
```

---

## 🔐 Status Values

| Status | Meaning | Used In |
|--------|---------|---------|
| `EN COURS` | Test in progress | etudiant_test_status |
| `TERMINE` | Test completed | etudiant_test_status |
| `ABANDON` | Test abandoned | etudiant_test_status |
| `ATTRIBUE` | Test assigned | etudiant_test_assignments |

---

## 📈 Scoring System

- **Per Question:** Score = number of selected options
- **Total Score:** Sum of all question scores
- **Points Awarded:** Based on test completion + difficulty + correct answers
- **Badges:** Awarded for achievements (easy/hard questions)

---

## 🚨 Important Notes

1. **Mental Health Category ID = 2** (parent category)
2. **Subcategories = 5, 6, 7, 8** (actual test categories)
3. **Mobile visibility:** Check `is_mobile = true`
4. **Admin visibility:** Check `is_bo = true`
5. **Student can only see:** Tests not yet completed
6. **Soft deletes:** Used for `etudiant_test_answers`

---

## 🔍 Debugging Tips

### Check if test appears in mobile
```sql
SELECT * FROM categories WHERE id = 5 AND is_mobile = 1;
SELECT * FROM tests WHERE category_id = 5;
```

### Check student test status
```sql
SELECT * FROM etudiant_test_status 
WHERE etudiant_id = ? AND test_id = ?;
```

### Check student answers
```sql
SELECT * FROM etudiant_test_answers 
WHERE etudiant_id = ? AND test_id = ?;
```

### Check test assignments
```sql
SELECT * FROM etudiant_test_assignments 
WHERE etudiant_id = ? AND test_id = ?;
```

---

## 📚 Related Documentation

- Full Documentation: `MENTAL_HEALTH_TESTS_DOCUMENTATION.md`
- Architecture Diagram: See Mermaid diagrams in documentation
- Backend API: `MindBridge_Backend_Documentation.md`

---

## 🎓 Test Types in MindBridge

| Type | Category ID | Purpose |
|------|------------|---------|
| Test Profiling | 1 | Initial student assessment |
| Mental Health | 2 | Psychological assessments |
| Sondage | 7 | Surveys/feedback |
| Challenge | 8 | Weekly challenges |

---

## 🔗 Key Relationships

```
Test
├── belongs to Category
├── has many Steps
├── has many EtudiantTestStatus
├── has many EtudiantTestAnswers
└── has many EtudiantTestAssignment

Step
├── belongs to Test
└── belongs to Question

Question
├── has many Steps
└── has many Options

EtudiantTestStatus
├── belongs to Test
└── belongs to MindBridgeEtudiant

EtudiantTestAnswers
├── belongs to Test
├── belongs to Question
└── belongs to MindBridgeEtudiant

EtudiantTestAssignment
├── belongs to Test
└── belongs to MindBridgeEtudiant
```

---

## 💡 Pro Tips

1. **Cache categories** - They rarely change
2. **Paginate results** - Always use pagination for participants
3. **Eager load relationships** - Avoid N+1 queries
4. **Validate inputs** - Always validate test_id and question_id
5. **Log errors** - Use Laravel logging for debugging
6. **Test thoroughly** - Write unit tests for scoring logic

---

## 📞 Support

For detailed information, refer to:
- `MENTAL_HEALTH_TESTS_DOCUMENTATION.md` - Complete guide
- Backend code: `mindbridge-backend/app/Http/Controllers/MindBridge/`
- Admin code: `mindbridge-admin/src/app/features/sante-mentale/`
- Mobile code: `mindbridge-mobile/lib/data/repositories/`

