import 'package:flutter/material.dart';
import 'package:mindbridge/core/models/profile.dart' as pro;
import 'package:mindbridge/core/utilities/app_images.dart';
import 'package:mindbridge/core/utilities/extensions.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import '../../../../core/constants/app_strings.dart';

class BadgesSection extends StatefulWidget {
  final List<pro.Badge> badges;

  const BadgesSection({super.key, required this.badges});

  @override
  State<BadgesSection> createState() => _BadgesSectionState();
}

class _BadgesSectionState extends State<BadgesSection> {
  bool _expanded = false;

  @override
  Widget build(BuildContext context) {
    final displayBadges =
        _expanded ? widget.badges : widget.badges.take(3).toList();

    return Card(
      color: Colors.white,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Colors.grey.withOpacity(0.2))),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppStrings.badgesCollected,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.black,
                  ),
                ),
                IconButton(
                  icon: Icon(
                    _expanded ? Icons.expand_less : Icons.expand_more,
                    size: 20.sp,
                    color: AppColors.primary,
                  ),
                  onPressed: () => setState(() => _expanded = !_expanded),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 2.w,
                  mainAxisSpacing: 2.h,
                  childAspectRatio: 1,
                ),
                itemCount: displayBadges.length,
                itemBuilder: (context, index) {
                  final badge = displayBadges[index];
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      (badge.iconUrl ?? "").toImage(
                        height: 7.h,
                        fit: BoxFit.cover,
                        errorWidget: Image.asset(
                          AppImages.pruple,
                          height: 7.h,
                          fit: BoxFit.cover,
                        ),
                      ),
                      SizedBox(height: 1.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 2.w),
                        child: Tooltip(
                          message:
                              badge.name ?? '', // Show full name on long press
                          child: Text(
                            badge.name ?? '',
                            maxLines: 2, // Allow text to wrap to 2 lines
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 9.sp, // Slightly smaller font
                              fontWeight: FontWeight.w600,
                              color: AppColors.black.withOpacity(0.7),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
