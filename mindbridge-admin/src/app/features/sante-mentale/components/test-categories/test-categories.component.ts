import {
  Component,
  computed,
  effect,
  inject,
  OnInit,
  Signal,
} from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../core/app.state';
import { SetLoading } from '../../../shared/store/shared.actions';
import { CategoryTestsComponent } from '../category-tests/category-tests.component';
import { NgClass, NgStyle } from '@angular/common';
import { TestManagementStore } from '../../../test-management/test-management.store';
import {
  IChildren,
  IMentalHealth,
} from '../../../test-management/models/test-categories-board';
import { SanteMentaleStore } from '../../sante-mentale.store';
import { CategoryTest } from '../../models/category-tests-response.model';

@Component({
  selector: 'app-test-categories',
  templateUrl: './test-categories.component.html',
  styleUrl: './test-categories.component.scss',
  imports: [CategoryTestsComponent, NgClass, NgStyle],
  providers: [TestManagementStore, SanteMentaleStore],
})
export class TestCategoriesComponent implements OnInit {
  private store = inject<Store<AppState>>(Store);
  #testManagementStore = inject(TestManagementStore);

  #santeMentaleStore = inject(SanteMentaleStore);

  categoryTests: Signal<CategoryTest[]> =
    this.#santeMentaleStore.state.categoryTests;

  testCategoriesHealthMental: Signal<IMentalHealth> = computed(
    () => this.#testManagementStore.state.testCategoriesBoard().mental_health
  );

  activeCategory!: IChildren;

  constructor() {
    effect(() => {
      const mentalHealth = this.testCategoriesHealthMental();
      console.log('[TestCategoriesComponent] Mental health object:', mentalHealth);
      console.log('[TestCategoriesComponent] Mental health children:', mentalHealth?.children);
      console.log('[TestCategoriesComponent] Children length:', mentalHealth?.children?.length);

      if (mentalHealth && mentalHealth.children && mentalHealth.children.length > 0) {
        console.log('[TestCategoriesComponent] Mental health categories loaded:', mentalHealth);
        console.log('[TestCategoriesComponent] First child: ', mentalHealth.children[0]);
        console.log('[TestCategoriesComponent] Category ID to fetch:', mentalHealth.children[0].id);
        this.store.dispatch(SetLoading({ isAppLoading: true }));
        this.activeCategory = mentalHealth.children[0];
        this.#santeMentaleStore.getCategoryTests(mentalHealth.children[0].id);
      } else {
        console.warn('[TestCategoriesComponent] No mental health categories found or children array is empty');
      }
    });
  }

  ngOnInit() {
    console.log('[TestCategoriesComponent] ngOnInit called');
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    console.log('[TestCategoriesComponent] Calling getTestCategoriesBoard()');
    this.#testManagementStore.getTestCategoriesBoard();
    console.log('[TestCategoriesComponent] getTestCategoriesBoard() called');
  }

  setActiveCategory(category: IChildren) {
    console.log('[TestCategoriesComponent] setActiveCategory called with:', category);
    console.log('[TestCategoriesComponent] Category ID:', category.id);
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.activeCategory = category;
    this.#santeMentaleStore.getCategoryTests(category.id);
  }
}
