import '../../../core/constants/api_routes.dart';
import '../../core/models/lesson.dart';
import '../providers/remote/dio_client.dart';

class ContentResponse {
  final Lesson? data;
  final bool isEmpty;
  final bool isError;

  ContentResponse({
    this.data,
    this.isEmpty = false,
    this.isError = false,
  });
}

class ContentRepository {
  final DioClient _dioClient;

  ContentRepository({required DioClient dioClient}) : _dioClient = dioClient;

  Future<ContentResponse> fetchLessons(int matiereId) async {
    try {
      final response = await _dioClient.dio
          .get('${ApiRoutes.lessons}?matiere_id=$matiereId');

      if (response.statusCode == 200) {
        if (response.data['contents'] == null ||
            response.data['contents'].isEmpty) {
          return ContentResponse(isEmpty: true);
        }
        return ContentResponse(data: Lesson.fromJson(response.data));
      }
    } catch (e) {
      print("Error fetching lessons: $e");
      return ContentResponse(isError: true);
    }
    return ContentResponse(isError: true);
  }
}
