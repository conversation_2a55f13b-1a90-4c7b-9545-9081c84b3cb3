<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('steps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('test_id')
            ->constrained('tests')
            ->onDelete('cascade');
            $table->foreignId('question_id')
                ->nullable()
                ->constrained('questions')
                ->onDelete('set null');
            $table->boolean('required')->default(false);
            $table->enum('type', ['one', 'many','true_false']);
            $table->text('condition')->nullable();
            $table->integer('order')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('steps');
    }
};
