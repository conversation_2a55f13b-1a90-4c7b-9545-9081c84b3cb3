import { Component, Inject, inject, OnInit, WritableSignal, signal } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../../environments/environment';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface Observation {
  id: number;
  label: string;
  category?: string;
  visible_to: 'teacher' | 'parent' | 'both';
  description?: string;
  active: boolean;
  test_id?: number;
  trigger_type: 'parent' | 'teacher' | 'significant_grade_drop';
}

@Component({
  selector: 'app-mental-health-trigger-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './mental-health-trigger-modal.component.html',
  styleUrl: './mental-health-trigger-modal.component.scss',
})
export class MentalHealthTriggerModalComponent implements OnInit {
  private http = inject(HttpClient);
  private dialogRef = inject(MatDialogRef<MentalHealthTriggerModalComponent>);
  private data = inject(MAT_DIALOG_DATA);

  etudiantId: number = this.data?.etudiantId;
  triggerType: WritableSignal<string> = signal('');
  selectedObservation: WritableSignal<Observation | null> = signal(null);
  observations: WritableSignal<Observation[]> = signal([]);
  isLoading: WritableSignal<boolean> = signal(false);
  showObservationList: WritableSignal<boolean> = signal(false);

  ngOnInit(): void {
    // Initialize if needed
  }

  selectTriggerType(type: 'parent' | 'teacher' | 'significant_grade_drop'): void {
    this.triggerType.set(type);

    if (type === 'parent') {
      this.loadObservations('parent');
      this.showObservationList.set(true);
    } else if (type === 'teacher') {
      this.loadObservations('teacher');
      this.showObservationList.set(true);
    } else if (type === 'significant_grade_drop') {
      this.loadObservations('significant_grade_drop');
      this.showObservationList.set(true);
    }
  }

  loadObservations(triggerType: string): void {
    this.isLoading.set(true);
    const url = `${environment.BASE_URL_API}mind_bridge/by-trigger-type?trigger_type=${triggerType}&visible_to=${triggerType}`;
    
    this.http.get<{ success: boolean; data: Observation[] }>(url).subscribe({
      next: (response) => {
        if (response.success) {
          this.observations.set(response.data);
        }
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Error loading observations:', error);
        this.isLoading.set(false);
      },
    });
  }

  selectObservation(observation: Observation): void {
    this.selectedObservation.set(observation);
  }

  confirm(): void {
    const triggerType = this.triggerType();

    if ((triggerType === 'parent' || triggerType === 'significant_grade_drop') && !this.selectedObservation()) {
      alert('Please select an observation');
      return;
    }

    const result = {
      triggerType,
      observation: this.selectedObservation(),
      etudiantId: this.etudiantId,
    };

    this.dialogRef.close(result);
  }

  cancel(): void {
    this.dialogRef.close(null);
  }
}

