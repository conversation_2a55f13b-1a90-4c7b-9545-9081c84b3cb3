import { Injectable, inject } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { ToasterComponent } from './toaster/toaster.component';

@Injectable({ providedIn: 'root' })
export class CommonService {
  private _snackBar = inject(MatSnackBar);
  private router = inject(Router);


  // #TODO STATUS should be an ENUM
  showToast(message: string, status: string) {
    this._snackBar.openFromComponent(ToasterComponent, {
      data: {
        message,
        status,
      },
      duration: 5000,
      verticalPosition: 'top', // Allowed values are  'top' | 'bottom'
      horizontalPosition: 'end', // Allowed values are 'start' | 'center' | 'end' | 'left' | 'right',
    });
  }
}
