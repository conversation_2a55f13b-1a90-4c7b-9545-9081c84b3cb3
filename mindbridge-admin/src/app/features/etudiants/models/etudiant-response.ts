export interface EtudiantResponse {
  id: number;
  name: string;
  first_name: string;
  last_name: string;
  email: string;
  avatar: string;
  identifiant: string;
  heure_moyen: number;
  taux_engagement: number;
  nni: number;
  test_profiling_completed: string;
  niveau: Niveau;
  school: School;
}

export interface Niveau {
  id: number;
  name: string;
  color: string;
  background: string;
  description: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
}

export interface School {
  id: number;
  organization_id: number;
  name: string;
  domain: string;
  database: string;
  created_at: Date;
  updated_at: Date;
  migrations_path: string;
}
