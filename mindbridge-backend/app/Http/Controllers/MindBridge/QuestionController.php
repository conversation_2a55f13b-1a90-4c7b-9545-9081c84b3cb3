<?php


namespace App\Http\Controllers\MindBridge;

use DB;
use App\Http\Controllers\Controller;
use App\Http\Resources\MindBridge\QuestionResource;
use App\Models\MatiereCentrale;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Storage;
use App\Models\MindBridge\Question;
use App\Models\NiveauCentrale;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Str;
use Exception;



class QuestionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            
            $categoryFilter = $request->query('category');
            $matiereFilter = $request->query('matiere');
            $niveauFilter = $request->query('niveau');
            $titleFilter = $request->query('title');
            $limit = $request->query('limit', 10);
            $page = $request->query('page', 1);
    
            
            $query = Question::query();
    
            
            if ($categoryFilter) {
                $query->whereHas('steps.test', function ($q) use ($categoryFilter) {
                    $q->where('category_id', $categoryFilter);
                });
            }

            $matiereIds = null;
            if ($matiereFilter) {
                $matiereIds = MatiereCentrale::where('id', $matiereFilter)
                    ->pluck('id')
                    ->toArray();
            }
    
            
            if (!empty($matiereIds)) {
                $query->whereHas('steps.test', function ($q) use ($matiereIds) {
                    $q->whereIn('matiere_id', $matiereIds);
                });
            }

            $niveauIds = null;
            if ($niveauFilter) {
                $niveauIds = NiveauCentrale::where('id', $niveauFilter)
                    ->pluck('id')
                    ->toArray();
            }
    
            
            if (!empty($niveauIds)) {
                $query->whereHas('steps.test', function ($q) use ($niveauIds) {
                    $q->whereIn('niveau_id', $niveauIds);
                });
            }
    
            
            if ($titleFilter) {
                $query->where('content', 'like', '%' . $titleFilter . '%');
            }
    
            
            $query->with([
                'options',
                'steps.test.matiere',
                'steps.test.niveau',
                'steps.test.category',
            ]);
    
            
            $total = $query->count(); 
            $questions = $query
                ->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->get();
    
            $totalPages = ceil($total / $limit); 
    
            
            return response()->json([
                'data' => QuestionResource::collection($questions),
                'current_page' => (int) $page,
                'total_pages' => (int) $totalPages,
                'per_page' => (int) $limit,
                'total_items' => (int) $total,
            ], 200);
        } catch (\Exception $e) {
            \Log::error("Error in QuestionController::index", ['exception' => $e]);
            return response()->json(['error' => 'An error occurred while fetching questions.'], 500);
        }
    }
    




    public function getQuestionsByCategory(Request $request, $id)
    {
        try {
            $questions = Question::where('category_id', $id)->get();

            if ($questions->isEmpty()) {
                return response()->json(['error' => 'No questions for this category'], 404);
            }
            return response()->json(['data'=> QuestionResource::collection($questions)], 200);
        } catch (\Exception $e) {
            \Log::error("Error GET questions by category : ===>", ['exception' => $e]);
            return response()->json(['error' => 'An error occurred while fetching questions for this category.'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->validate($request, [
                'type' => 'required|in:video,image,text,audio',
                'description' => 'nullable|string',
                'options' => 'nullable|array',
                'options.*.name' => 'required|string',
                'options.*.icon' => 'nullable',
                "options.*.isCorrect" => "nullable|boolean",
                'content' => [
                    'required',
                    function ($attribute, $value, $fail) {
                        if (!is_string($value) && !is_file($value)) {
                            $fail("The $attribute must be either a string or a valid file.");
                        }
                    }
                ],
            ]);

            $data = $request->only(['type', 'description']);

            if ($request->hasFile('content') && $request->type !== 'text') {
                $file = $request->file('content');
                $directory = "mindbridge/uploads/{$request->type}";

                if (!Storage::disk('public')->exists($directory)) {
                    Storage::disk('public')->makeDirectory($directory);
                }

                $uniqueFilename = time() . '-' . Str::random(5) . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs($directory, $uniqueFilename, 'public');
                $data['content'] = $path;
            } else if ($request->type === 'text') {
                $data['content'] = $request->content;
            }

            $question = Question::create($data);
            if ($request->has('options')) {
                $question->options()->createMany($request->options);
            }
            DB::commit();
            return new QuestionResource($question);
        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json($e->validator->errors(), 422);
        } catch (Exception $e) {
            DB::rollBack();
            \Log::error("Error in QuestionController@store: ", ['exception' => $e]);
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $question = Question::findOrFail($id);
            return new QuestionResource($question);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'message' => 'Question not found'
            ], 404);
        } catch (Exception $e) {
            \Log::error('error in QuestionController@show', ['exception' => $e]);
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update($id, Request $request)
    {
        try {
            DB::beginTransaction();

            $question = Question::with('options')->findOrFail($id);

            \Log::info('QuestionController@update', ['question' => $question]);

            $this->validate($request, [
                'type' => 'required|in:video,image,text,audio',
                'description' => 'nullable|string',
                'options' => 'nullable|array',
                'options.*.id' => 'nullable',
                'options.*.name' => 'required|string',
                'options.*.icon' => 'nullable',
                "options.*.isCorrect" => "nullable|boolean",
                'content' => [
                    'required',
                    function ($attribute, $value, $fail) {
                        if (!is_string($value) && !is_file($value)) {
                            $fail("The $attribute must be either a string or a valid file.");
                        }
                    }
                ]
            ]);

            $data = $request->only(['type', 'description']);
            if ($request->hasFile('content') && $request->type !== 'text') {
                $file = $request->file('content');
                $directory = "mindbridge/uploads/{$request->type}";

                if (!Storage::disk('public')->exists($directory)) {
                    Storage::disk('public')->makeDirectory($directory);
                }

                $uniqueFilename = time() . '-' . Str::random(5) . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs($directory, $uniqueFilename, 'public');

                if ($question->content && Storage::disk('public')->exists($question->content)) {
                    Storage::disk('public')->delete($question->content);
                }

                $data['content'] = $path;
            } elseif ($request->type === 'text') {
                $data['content'] = $request->content;
            }
            $question->update($data);
            if ($request->has('options')) {
                $options = $request->options;

                foreach ($options as $optionData) {
                    if (isset($optionData['id'])) {
                        $option = $question->options()->find($optionData['id']);
                        if ($option) {
                            unset($optionData['id'], $optionData['question_id']);
                            $option->update($optionData);
                        }
                    } else {
                        $question->options()->create($optionData);
                    }
                }

                $existingOptionIds = $question->options->pluck('id')->toArray();
                $requestOptionIds = collect($options)->pluck('id')->filter()->toArray();
                $idsToDelete = array_diff($existingOptionIds, $requestOptionIds);

                if (!empty($idsToDelete)) {
                    $question->options()->whereIn('id', $idsToDelete)->delete();
                }
            }

            DB::commit();

            \Log::info('QuestionController@update', ['updated question' => $question]);
            $question->refresh();
            return new QuestionResource($question);
        } catch (ValidationException $e) {
            \Log::info($e->getMessage());
            DB::rollBack();
            return response()->json($e->validator->errors(), 422);
        } catch (Exception $e) {
            DB::rollBack();
            \Log::error("Error in QuestionController@update: ", ['exception' => $e]);
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $question = Question::findOrFail($id);

            // Delete associated file if it exists
            if ($question->content && Storage::disk('public')->exists($question->content)) {
                Storage::disk('public')->delete($question->content);
            }

            $question->delete();

            return response()->json(['message' => 'Question deleted successfully.'], 200);
        } catch (Exception $e) {
            \Log::error("Error in QuestionController@destroy: ", ['exception' => $e]);
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    /**
     * Upload an image for a question
     */
    public function uploadImage(Request $request)
    {
        try {
            $this->validate($request, [
                'image' => 'required|image|max:10240', // 10MB max, all image types
            ]);

            $file = $request->file('image');
            $directory = 'mindbridge/uploads/questions';

            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }

            $uniqueFilename = 'question_' . time() . '_' . Str::random(8) . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs($directory, $uniqueFilename, 'public');

            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'image_path' => $path,
                'image_url' => 'storage/' . $path
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->validator->errors()
            ], 422);
        } catch (Exception $e) {
            \Log::error("Error in QuestionController@uploadImage: ", ['exception' => $e]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while uploading the image'
            ], 500);
        }
    }

    /**
     * Delete an image from a question
     */
    public function deleteImage(Request $request)
    {
        try {
            $this->validate($request, [
                'image_path' => 'required|string',
            ]);

            $imagePath = $request->input('image_path');

            if (Storage::disk('public')->exists($imagePath)) {
                Storage::disk('public')->delete($imagePath);
            }

            return response()->json([
                'success' => true,
                'message' => 'Image deleted successfully'
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->validator->errors()
            ], 422);
        } catch (Exception $e) {
            \Log::error("Error in QuestionController@deleteImage: ", ['exception' => $e]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the image'
            ], 500);
        }
    }
}
