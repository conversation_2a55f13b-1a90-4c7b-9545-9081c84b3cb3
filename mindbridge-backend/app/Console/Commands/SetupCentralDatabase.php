<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class SetupCentralDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mindbridge:setup-central-db 
                            {--force : Force setup without confirmation}
                            {--fresh : Drop all tables and recreate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup the central database with all required tables and initial data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🏗️  MindBridge Central Database Setup');
        $this->info('===================================');

        // Check database connection
        if (!$this->checkDatabaseConnection()) {
            return 1;
        }

        if ($this->option('fresh')) {
            $this->warn('⚠️  FRESH SETUP: This will drop all existing tables in the central database!');
            if (!$this->option('force') && !$this->confirm('Are you sure you want to continue?')) {
                $this->info('❌ Setup cancelled.');
                return 0;
            }
            $this->freshSetup();
        } else {
            if (!$this->option('force')) {
                $this->info('This will create the central database tables and initial data.');
                if (!$this->confirm('Do you want to continue?')) {
                    $this->info('❌ Setup cancelled.');
                    return 0;
                }
            }
            $this->normalSetup();
        }

        return 0;
    }

    /**
     * Check if database connection is working
     */
    private function checkDatabaseConnection(): bool
    {
        $this->info('🔍 Checking database connection...');

        try {
            DB::connection('centrale')->getPdo();
            $this->info('✅ Central database connection successful');
            return true;
        } catch (\Exception $e) {
            $this->error('❌ Cannot connect to central database: ' . $e->getMessage());
            $this->error('Please check your database configuration in .env file');
            return false;
        }
    }

    /**
     * Fresh setup - drop and recreate all tables
     */
    private function freshSetup()
    {
        $this->info('🗑️  Dropping existing tables...');
        
        try {
            // Set foreign key checks to 0 to avoid constraint issues
            DB::connection('centrale')->statement('SET FOREIGN_KEY_CHECKS=0');
            
            // Get all tables in the central database
            $tables = DB::connection('centrale')->select('SHOW TABLES');
            $databaseName = DB::connection('centrale')->getDatabaseName();
            
            foreach ($tables as $table) {
                $tableName = $table->{"Tables_in_{$databaseName}"};
                DB::connection('centrale')->statement("DROP TABLE IF EXISTS `{$tableName}`");
                $this->line("   Dropped table: {$tableName}");
            }
            
            // Re-enable foreign key checks
            DB::connection('centrale')->statement('SET FOREIGN_KEY_CHECKS=1');
            
            $this->info('✅ All tables dropped successfully');
            
        } catch (\Exception $e) {
            $this->error('❌ Error dropping tables: ' . $e->getMessage());
            return;
        }

        $this->normalSetup();
    }

    /**
     * Normal setup - create tables and seed data
     */
    private function normalSetup()
    {
        $this->info('🚀 Setting up central database...');

        // Step 1: Run migrations
        $this->runCentralMigrations();

        // Step 2: Seed initial data
        $this->seedInitialData();

        $this->info('✅ Central database setup completed successfully!');
        $this->displaySummary();
    }

    /**
     * Run central database migrations
     */
    private function runCentralMigrations()
    {
        $this->info('📋 Running central database migrations...');

        try {
            // Run migrations for central database
            Artisan::call('migrate', [
                '--database' => 'centrale',
                '--path' => 'database/migrations/centrale',
                '--force' => true
            ]);

            $output = Artisan::output();
            if (trim($output)) {
                $this->line($output);
            }

            $this->info('✅ Central migrations completed');

        } catch (\Exception $e) {
            $this->error('❌ Error running migrations: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Seed initial data
     */
    private function seedInitialData()
    {
        $this->info('🌱 Seeding initial data...');

        try {
            // Run central database seeders
            $this->info('   Creating schools...');
            Artisan::call('db:seed', [
                '--class' => 'Database\\Seeders\\centrale\\TenantsSeeder',
                '--force' => true
            ]);

            $this->info('   Creating educational levels...');
            Artisan::call('db:seed', [
                '--class' => 'Database\\Seeders\\centrale\\NiveauxSeederCentrale',
                '--force' => true
            ]);

            $this->info('   Creating subjects...');
            Artisan::call('db:seed', [
                '--class' => 'Database\\Seeders\\centrale\\MatiersSeederCentrale',
                '--force' => true
            ]);

            $this->info('   Creating subject-level relationships...');
            Artisan::call('db:seed', [
                '--class' => 'Database\\Seeders\\centrale\\NiveauxMatiereSeederCentrale',
                '--force' => true
            ]);

            $this->info('✅ Initial data seeded successfully');

        } catch (\Exception $e) {
            $this->error('❌ Error seeding data: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Display setup summary
     */
    private function displaySummary()
    {
        $this->info('');
        $this->info('📊 Setup Summary:');
        $this->info('================');

        try {
            // Count records in each table
            $schools = DB::connection('centrale')->table('schools')->count();
            $users = DB::connection('centrale')->table('users')->count();
            $niveaux = DB::connection('centrale')->table('niveaux')->count();
            $matieres = DB::connection('centrale')->table('matieres')->count();
            $relationships = DB::connection('centrale')->table('niveaux_matiere')->count();

            $this->info("📚 Schools: {$schools}");
            $this->info("👥 Users: {$users}");
            $this->info("🎓 Educational Levels: {$niveaux}");
            $this->info("📖 Subjects: {$matieres}");
            $this->info("🔗 Level-Subject Relationships: {$relationships}");

            $this->info('');
            $this->info('🎯 Next Steps:');
            $this->info('1. Run: php artisan mindbridge:sync-data --dry-run');
            $this->info('2. If everything looks good, run: php artisan mindbridge:sync-data');
            $this->info('3. Test your MindBridge application');

        } catch (\Exception $e) {
            $this->warn('⚠️  Could not generate summary: ' . $e->getMessage());
        }
    }
}
