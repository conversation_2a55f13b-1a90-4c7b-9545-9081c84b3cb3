.timer {
    position: relative;

    .toggle_timer {
        position: absolute;
        top: 70%;
        right: 10px;
        transform: translateY(-50%);
        cursor: pointer;
    }


    .set_timer {
        position: absolute;
        top: 100%;
        right: 0;
        width: 350px;
        height: 134px;
        background-color: #fff;
        box-shadow: 0px 0px 20px 4px #BFBFBF40;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30px;

        .content_timer {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 15px;
            height: 100%;

            img {
                width: 30px;
                height: 30px;
                cursor: pointer;
            }
        }
    }
}

.scrolling_container {
    overflow-y: auto;
    height: 100%;
    max-height: calc(100vh - 200px);
}

.drag {
    position: relative;

    .img_drag {
        position: absolute;
        top: 5px;
        left: 50%;
        right: 50%;
        cursor: move;
    }
}

.border_b {
    border-bottom: 2px solid #e5e7eb;
    border-radius: 0;
    background-color: #bcc3d30d;
}

.not_allowed {
    cursor: not-allowed !important;
}

.list_test {
    background-color: #fff;
    position: relative;

    .delete_test {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 20px;
        height: 20px;
        cursor: pointer;
    }

    h2 {
        font-weight: 400;
    }
}

.input_answer {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon_answer {
        width: 20px;
        height: 20px;
        // position: absolute;
        // right: 15px;
        // top: 20%;
        cursor: pointer;
    }
}

.upload_icon {
    min-width: 45px;
    height: 40px;
    position: relative;

    .emoji_display {
        width: 40px;
        height: 36px;
        font-size: x-large;
        text-align: center;
        line-height: 45px;
    }

    img {
        position: absolute;
        inset: 0;
        z-index: 0;
        width: 45px;
        height: 40px;
    }

    button {
        width: 45px;
        height: 40px;
        position: absolute;
        inset: 0;
        z-index: 1;
    }
}

.step-container .action-buttons {
    display: none;
  }

  .step-container:hover .action-buttons {
    display: flex;
  }

  .step-container.current .action-buttons {
    display: flex;
  }
.step-container[formGroupName].isImported {
    background-color: #f9f9f9;
    border: 2px dashed #ccc;
  }

.disabled_element {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed !important;
}


