.scoring-management-container {
  padding: 30px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 30px;

    h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 700;
      color: #1a1a1a;
    }

    .subtitle {
      margin: 0;
      font-size: 14px;
      color: #666;
    }
  }

  .test-selection-card,
  .scoring-rules-card {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    h2 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .test-selection-card {
    .filter-form {
      margin-bottom: 16px;

      mat-form-field {
        width: 100%;
      }
    }

    .loading {
      padding: 16px;
      text-align: center;
      color: #666;
      font-size: 14px;
    }

    ::ng-deep .mat-mdc-paginator {
      background-color: #f9f9f9;
      border-top: 1px solid #eee;
    }
  }

  .scoring-rules-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #eee;

      .test-info {
        margin: 8px 0 0 0;
        font-size: 13px;
        color: #666;

        strong {
          color: #333;
        }
      }

      .btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;

        &.btn-primary {
          background-color: #007bff;
          color: white;

          &:hover:not(:disabled) {
            background-color: #0056b3;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }

    .scoring-rule-form {
      background-color: #f9f9f9;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 24px;
      border: 1px solid #eee;

      .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 20px;

        .full-width {
          grid-column: 1 / -1;
        }

        mat-form-field {
          width: 100%;
        }
      }

      .form-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;

        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.3s ease;

          &.btn-primary {
            background-color: #007bff;
            color: white;

            &:hover:not(:disabled) {
              background-color: #0056b3;
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }

          &.btn-secondary {
            background-color: #6c757d;
            color: white;

            &:hover {
              background-color: #545b62;
            }
          }
        }
      }
    }

    .scoring-rules-table {
      .loading,
      .no-data {
        padding: 20px;
        text-align: center;
        color: #666;
        font-size: 14px;
      }

      .rules-table {
        width: 100%;
        border-collapse: collapse;

        th {
          background-color: #f8f9fa;
          padding: 12px;
          text-align: left;
          font-weight: 600;
          color: #333;
          border-bottom: 2px solid #ddd;
          font-size: 13px;
        }

        td {
          padding: 12px;
          border-bottom: 1px solid #eee;
          font-size: 13px;
          color: #555;
        }

        tr:hover {
          background-color: #f9f9f9;
        }

        .btn-icon {
          background: none;
          border: none;
          cursor: pointer;
          padding: 4px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          margin-right: 8px;
          transition: all 0.3s ease;

          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }

          &.btn-edit {
            color: #007bff;

            &:hover {
              color: #0056b3;
              background-color: rgba(0, 123, 255, 0.1);
              border-radius: 4px;
            }
          }

          &.btn-delete {
            color: #dc3545;

            &:hover {
              color: #c82333;
              background-color: rgba(220, 53, 69, 0.1);
              border-radius: 4px;
            }
          }
        }
      }
    }
  }

  .empty-state {
    background-color: white;
    border-radius: 8px;
    padding: 60px 30px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: #ccc;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 16px;
      color: #666;
    }
  }
}

