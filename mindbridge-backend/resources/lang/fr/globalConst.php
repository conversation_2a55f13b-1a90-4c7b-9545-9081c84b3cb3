<?php
return [
    "auth" => [
        'login_incorrect' => "L'adresse e-mail ou le mot de passe est incorrect.",
        'password_update' => "Mot de passe mis à jour avec succès.",
        'user_not_found' => "Aucun utilisateur trouvé avec cette adresse e-mail.",
        'user_create_success' => 'Utilisateur créé avec succès.',
        'required_email' => "L'adresse e-mail est requise pour la connexion.",
        'required_password' => "Veuillez saisir votre mot de passe.",
        'required_token' => "Token est requi.",
        'email_already_taken' => "Cette adresse e-mail est déjà enregistrée.",
        'phone_already_taken' => "Ce numéro de téléphone est déjà enregistré.",
        'required_phone' => "Le numereau portable est requise pour la connexion.",
        'required_name' => "Le nom est requise pour la connexion.",
        'required_role' => "Le role est requise.",
        'image_avatar' => "L'avatar doit être une image.",
        'mimes_avatar' => "L'avatar doit être au format jpeg, png ou jpg.",
        'max_avatar' => "L'avatar ne doit pas dépasser 2 Mo.",
        'mail_title' => "Bonjour",
        'mail_header_contenu' => "Vous recevez cet e-mail suite à une demande de réinitialisation de mot de passe de votre compte.",
        'mail_action' => "Réinitialisez le mot de passe",
        'mail_action_message_trouble' => "Si vous rencontrez des difficultés pour cliquer sur le bouton « Réinitialisez le mot de passe », copiez et collez l'URL ci-dessous dans votre navigateur Web :",
        'mail_footer_contenu' => "Ce lien de réinitialisation expirera dans 60 minutes. Si vous n'avez pas demandé cette réinitialisation, aucune action n'est requise de votre part.",
    ],
    "const" => [
        "invalide_token" => "Jeton invalide !",
        "token_already_used" => "Le lien de réinitialisation a déjà été utilisé. Veuillez demander un nouveau lien.",
        "message" => "message",
        'success_reset_password' => "La demande de réinitialisation a été envoyée.",
        'error_reset_password' => "Erreur lors de la Réinitialisation de la mot de passe.",
        'users_not_found' => "Aucun utilisateur trouvé.",
        'UserUpdated' => 'Utilisateur mis à jour avec succès.',
        'error_update_user' => "Erreur lors de la mise à jour de l'utilisateur.",
        'delete_user' => "Utilisateur supprimé avec succès.",
        'error_delete_user' => "Erreur lors de la suppression de l'utilisateur.",
        'error_create_user_centre_not_found' => "Erreur. Le centre n'est pas trouver.",
        'error_create_user' => "Erreur lors de la création de l'utilisateur.",
        'required_name' => "Le Nom est requis.",
        'unique_name_error' => "Le nom doit être unique.",
        'type_not_found'=>"type n'existe pas",
    ],
];