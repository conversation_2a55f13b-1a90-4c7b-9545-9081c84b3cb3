import { Form<PERSON>rray, FormC<PERSON>rol, FormGroup, Validators } from '@angular/forms';
import {
  TestCreationRequest,
  TestCreationRequestQuestion,
  TestCreationRequestQuestionOption,
  TestCreationRequestStep,
  TestCreationRequestWrapper,
} from './test-creation-form';
import { TypeQuestionEnum } from './type-question.enum';
import { DifficultyLevelEnum } from './difficulty-level-enum';

export class TestCreationRequestWrapperFormGroup {
  test: FormGroup<TestCreationRequestFormGroup>;
  steps: FormArray<FormGroup<TestCreationRequestStepFormGroup>>;
  constructor(testCreationRequestWrapper = new TestCreationRequestWrapper()) {
    this.test = new FormGroup(
      new TestCreationRequestFormGroup(testCreationRequestWrapper.test)
    );
    this.steps = new FormArray(
      testCreationRequestWrapper.steps.map((step) => {
        return new FormGroup<TestCreationRequestStepFormGroup>(
          new TestCreationRequestStepFormGroup(step)
        );
      })
    );
  }
}

export class TestCreationRequestFormGroup {
  id: FormControl<number>;
  title: FormControl<string>;
  description: FormControl<string>;
  category_id: FormControl<number>;
  matiere_id: FormControl<number>;
  niveau_id: FormControl<number>;
  content_id: FormControl<number>;
  timer: FormControl<number>;
  challenge_date: FormControl<Date | string>;
  difficulty_level: FormControl<DifficultyLevelEnum>;
  constructor(testCreationRequest = new TestCreationRequest()) {
    this.id = new FormControl(testCreationRequest.id);
    this.title = new FormControl(testCreationRequest.title, [
      Validators.required,
    ]);
    this.description = new FormControl(testCreationRequest.description, [
      Validators.required,
    ]);
    this.category_id = new FormControl(testCreationRequest.category_id, [
      Validators.required,
    ]);
    this.matiere_id = new FormControl(testCreationRequest.matiere_id);
    this.niveau_id = new FormControl(testCreationRequest.niveau_id);
    this.content_id = new FormControl(testCreationRequest.content_id);
    this.timer = new FormControl(testCreationRequest.timer);
    this.challenge_date = new FormControl(testCreationRequest.challenge_date);
    this.difficulty_level = new FormControl(
      testCreationRequest.difficulty_level
    );
  }
}

export class TestCreationRequestStepFormGroup {
  id: FormControl<number>;
  type: FormControl<TypeQuestionEnum>;
  order: FormControl<number>;
  question: FormGroup<TestCreationRequestQuestionFormGroup>;
  question_id?: FormControl<number>;
  constructor(testCreationRequestStep = new TestCreationRequestStep()) {
    this.id = new FormControl(testCreationRequestStep.id);
    this.type = new FormControl(testCreationRequestStep.type, [
      Validators.required,
    ]);
    this.order = new FormControl(testCreationRequestStep.order, [
      Validators.required,
    ]);
    this.question = new FormGroup(
      new TestCreationRequestQuestionFormGroup(testCreationRequestStep.question)
    );
    this.question_id = new FormControl(testCreationRequestStep?.question?.id);
  }
}

export class TestCreationRequestQuestionFormGroup {
  id: FormControl<number>;
  type: FormControl<string>;
  content: FormControl<string>;
  answer: FormControl<boolean>;
  description: FormControl<string>;
  image_path: FormControl<Blob | string>;
  required: FormControl<boolean>;
  options: FormArray<FormGroup<TestCreationRequestQuestionOptionFormGroup>>;
  constructor(testCreationRequestQuestion = new TestCreationRequestQuestion()) {
    this.id = new FormControl(testCreationRequestQuestion.id);
    this.type = new FormControl(testCreationRequestQuestion.type);
    this.content = new FormControl(testCreationRequestQuestion.content, [
      Validators.required,
    ]);
    this.description = new FormControl(testCreationRequestQuestion.description);
    this.image_path = new FormControl(testCreationRequestQuestion.image_path);
    this.required = new FormControl(testCreationRequestQuestion.required);
    this.answer = new FormControl(testCreationRequestQuestion.answer);
    this.options = new FormArray(
      testCreationRequestQuestion.options.map((option) => {
        return new FormGroup<TestCreationRequestQuestionOptionFormGroup>(
          new TestCreationRequestQuestionOptionFormGroup(option)
        );
      })
    );
  }
}

export class TestCreationRequestQuestionOptionFormGroup {
  id: FormControl<number>;
  name: FormControl<string>;
  icon?: FormControl<string>;
  isCorrect?: FormControl<boolean>;
  constructor(
    testCreationRequestQuestionOption = new TestCreationRequestQuestionOption()
  ) {
    this.id = new FormControl(testCreationRequestQuestionOption.id);
    this.name = new FormControl(testCreationRequestQuestionOption.name, [
      Validators.required,
    ]);
    this.icon = new FormControl(testCreationRequestQuestionOption?.icon);
    this.isCorrect = new FormControl(
      testCreationRequestQuestionOption?.isCorrect
    );
  }
}
