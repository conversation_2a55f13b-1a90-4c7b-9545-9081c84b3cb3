import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/app/theme/colors.dart';
import 'package:shimmer/shimmer.dart';
import 'package:sizer/sizer.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../widgets/global_empty.dart';
import '../../../widgets/global_error.dart';
import '../../../widgets/global_header.dart';
import '../controller/home_controller.dart';
import '../widgets/ai_card.dart';
import '../widgets/profile_card.dart';
import '../widgets/swipeable_card.dart';

class HomeView extends StatelessWidget {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find<HomeController>();

    return InkWell(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: const GlobalHeader(),
        body: RefreshIndicator(
          backgroundColor: AppColors.primary,
          color: Colors.white,
          onRefresh: controller.callAPIs,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              children: [
                SizedBox(height: 6.h),
                Obx(() => Skeletonizer(
                      enabled: controller.profileLoading.value,
                      effect: const ShimmerEffect(),
                      enableSwitchAnimation: true,
                      child: ProfileCard(
                        avatarSize: 12.h,
                        avatarId: controller.selectedAvatarId.value,
                        onAvatarChange: controller.updateAvatarId,
                      ),
                    )),
                AiCard(),
                Obx(() {
                  if (controller.isError.value) {
                    return Center(child: GlobalErrorHandler(
                      onRetry: () {
                        controller.fetchCategories();
                      },
                    ));
                  }

                  if (controller.isEmptyData.value) {
                    return Center(child: GlobalEmptyHandler(
                      onBack: () {
                        Get.back();
                      },
                    ));
                  }

                  if (controller.isLoading.value &&
                      controller.categories.isEmpty) {
                    return buildSkeletonLoader();
                  }
                  return SizedBox(
                    height: 40.h,
                    child: Skeletonizer(
                      enabled: controller.isLoading.value &&
                          controller.categories.isNotEmpty,
                      effect: const ShimmerEffect(),
                      enableSwitchAnimation: true,
                      child: CarouselSlider.builder(
                        key: ValueKey(controller.currentPage.value),
                        itemCount: controller.categories.length,
                        itemBuilder: (context, index, realIndex) {
                          return SwipeableCard(
                            cardData: controller.categories[index],
                            isHighlighted:
                                index == controller.currentPage.value.round(),
                          );
                        },
                        options: CarouselOptions(
                          height: 40.h,
                          aspectRatio: 16 / 9,
                          viewportFraction: 0.65,
                          initialPage: controller.currentPage.value,
                          enableInfiniteScroll: true,
                          enlargeCenterPage: true,
                          disableCenter: true,
                          onPageChanged: (index, reason) {
                            controller.updateCurrentPage(index);
                          },
                          scrollPhysics: const BouncingScrollPhysics(),
                        ),
                      ),
                    ),
                  );
                }),
                SizedBox(height: 3.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildSkeletonLoader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w),
      height: 40.h,
      child: PageView.builder(
        controller: PageController(
          viewportFraction: 0.65,
          initialPage: 1,
        ),
        itemCount: 3,
        itemBuilder: (context, index) {
          double scale = index == 1 ? 1.0 : 0.8;
          return Transform.scale(
            scale: scale,
            alignment: Alignment.center,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 0.w),
              child: Shimmer.fromColors(
                baseColor: const Color(0xFFE0E0E0),
                highlightColor: Colors.white,
                period: const Duration(milliseconds: 1500),
                child: Container(
                  margin: EdgeInsets.symmetric(vertical: 1.h),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0E0E0),
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
