# Quick Start: Scoring Rules in Admin Panel

## 🎯 What Was Implemented

You can now manage scoring rules directly in the admin panel when creating/editing tests in the "Santé Mentale" section.

## 📋 Files Summary

### Backend (<PERSON>vel)
```
✅ TestScoringRuleController.php - API endpoints for CRUD operations
✅ Routes added to api.php - 4 new endpoints for scoring rules
```

### Frontend (Angular)
```
✅ test-scoring-rule.ts - Models and interfaces
✅ test-scoring-rule-form-group.ts - Form validation
✅ test-scoring-rule.service.ts - API service
✅ scoring-rules.component.ts - Main component logic
✅ scoring-rules.component.html - UI template
✅ scoring-rules.component.scss - Styling
✅ add-test.component.ts - Integration (import added)
✅ add-test.component.html - Integration (component added)
```

## 🚀 How to Use

### Step 1: Create a Test
1. Go to Admin Panel → Santé Mentale
2. Create a new test with questions
3. Save the test

### Step 2: Add Scoring Rules
1. Click "Edit" on the test
2. Scroll down to "Règles de Notation" section
3. Click "Ajouter une Règle"
4. Fill in the form:
   - **Min %**: Minimum percentage (0-100)
   - **Max %**: Maximum percentage (0-100)
   - **Interprétation**: Label (e.g., "Excellent", "Good")
   - **Feedback**: Message for students (optional)
   - **Recommandation**: Professional advice (optional)
5. Click "Créer"

### Step 3: Manage Rules
- **Edit**: Click the edit icon to modify a rule
- **Delete**: Click the delete icon to remove a rule
- **View**: All rules are displayed in a table

## 📊 Example Configuration

For a WISC-V style test:

| Min % | Max % | Interprétation | Feedback | Recommandation |
|-------|-------|-----------------|----------|-----------------|
| 85 | 100 | Excellent | Outstanding performance! | Continue with advanced materials |
| 60 | 84 | Good | Good understanding | Review weak areas |
| 40 | 59 | Fair | Needs improvement | Consultation recommended |
| 0 | 39 | Poor | Significant difficulties | Urgent specialist consultation |

## 🔧 API Endpoints

```
GET    /api/mind_bridge/tests/{testId}/scoring-rules
POST   /api/mind_bridge/tests/{testId}/scoring-rules
PUT    /api/mind_bridge/tests/{testId}/scoring-rules/{ruleId}
DELETE /api/mind_bridge/tests/{testId}/scoring-rules/{ruleId}
```

## ✅ Validation Rules

- Min % must be 0-100
- Max % must be 0-100
- Max % must be >= Min %
- Ranges cannot overlap
- Interpretation is required
- Feedback and Recommendation are optional

## 🎨 UI Features

- ✅ Responsive table design
- ✅ Add/Edit/Delete buttons
- ✅ Form validation with error messages
- ✅ Toast notifications for success/error
- ✅ Loading states
- ✅ Empty state message

## 🔄 Integration with Scoring

The scoring rules are automatically applied when:
1. Student completes a test
2. System calculates percentage score
3. Finds matching rule based on percentage
4. Returns feedback, recommendation, and interpretation

## 📝 Notes

- Scoring rules are only visible when **editing** an existing test
- Rules are hidden in **read-only mode**
- Rules are hidden during **test creation** (add after creation)
- System prevents overlapping percentage ranges
- All fields are validated before saving

## 🐛 Troubleshooting

**Rules not showing?**
- Make sure you're editing an existing test (not creating new)
- Check that test ID is properly set

**Can't add a rule?**
- Verify all required fields are filled
- Check for overlapping percentage ranges
- Look for validation error messages

**API errors?**
- Check backend logs: `docker logs mindbridge_api`
- Verify test exists in database
- Check percentage range validation

## 📞 Support

For issues or questions, check:
1. Browser console for JavaScript errors
2. Network tab for API responses
3. Backend logs for server errors

