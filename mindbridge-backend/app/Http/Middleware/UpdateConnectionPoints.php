<?php

namespace App\Http\Middleware;

use App\Models\MindBridge\MindBridgeEtudiant;
use Closure;
use Carbon\Carbon;
use App\Service\BadgePointService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class UpdateConnectionPoints
{
    /**
     * Handle an incoming request: award XP for login streaks
     */
    public function handle($request, Closure $next)
    {
        if ($this->shouldSkipProcessing($request)) {
            return $next($request);
        }

        try {
            if (auth()->check()) {
                $user = auth()->user();
                
                $lockKey = 'connection_points_lock_' . $user->id;
                
                // Attempt to acquire a lock for 3 seconds
                if (Cache::lock($lockKey, 3)->get()) {
                    try {
                        DB::beginTransaction();
                        
                        $etudiant = MindBridgeEtudiant::with('points', 'badges')
                            ->where('user_id', $user->id)
                            ->lockForUpdate()
                            ->first();
                        
                        if (!$etudiant) {
                            DB::rollBack();
                            Log::error('No student record found for user ID: ' . $user->id);
                            return $next($request);
                        }
                        
                        // Process connection streak and rewards
                        $this->processConnectionRewards($etudiant);

                        DB::commit();
                    } finally {
                        Cache::lock($lockKey)->release();
                    }
                } else {
                    Log::info('Skipping connection points processing - lock already acquired for user: ' . $user->id);
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in UpdateConnectionPoints middleware: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
        }
        
        return $next($request);
    }
    
    /**
     * Check if the current request should skip processing
     */
    private function shouldSkipProcessing($request): bool
    {
        $skipPatterns = [
            '/api/mind_bridge/etudiant/categories/board'
        ];
        
        foreach ($skipPatterns as $pattern) {
            if (strpos($request->url(), $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Log student information for debugging
     */
    private function logStudentInfo($etudiant, $request): void
    {
        Log::info('User ID: ' . auth()->id());
        Log::info('Etudiant ID: ' . $etudiant->id);
        Log::info('Etudiant Points: ' . json_encode($etudiant->points));
        Log::info('Etudiant Badges: ' . json_encode($etudiant->badges));
        Log::info('Etudiant Last Connection: ' . $etudiant->last_connection_at);
        Log::info('Etudiant Connection Streak: ' . $etudiant->connection_streak);
        Log::info('Etudiant Test Profiling Completed: ' . $etudiant->test_profiling_completed);
        Log::info('Request: ' . json_encode($request->all()));
        Log::info('Request URL: ' . $request->url());
        Log::info('Request Method: ' . $request->method());
    }
    
    /**
     * Process connection streak and rewards
    */
    private function processConnectionRewards($etudiant): void
    {
        $today = Carbon::today();
        $lastConnection = $etudiant->last_connection_at 
            ? Carbon::parse($etudiant->last_connection_at)->startOfDay()
            : null;
        
        if ($lastConnection && $lastConnection->eq($today)) {
            Log::info("Connection already processed today for student ID: {$etudiant->id}");
            return;
        }
        
        $streak = $etudiant->connection_streak ?? 0;
        
        if ($lastConnection && $lastConnection->eq($today->copy()->subDay())) {
            $streak++;
        } else {
            $streak = 1;
        }
        $etudiant->connection_streak = $streak;
        $etudiant->last_connection_at = Carbon::now();
        $etudiant->save();
        
        $service = new BadgePointService($etudiant);
        $service->processAction('daily_connection');
        
        if ($streak === 7) {
            $service->processAction('7_day_streak');
        }
        
        if ($streak === 30) {
            $service->processAction('30_day_streak');
        }
    }
}