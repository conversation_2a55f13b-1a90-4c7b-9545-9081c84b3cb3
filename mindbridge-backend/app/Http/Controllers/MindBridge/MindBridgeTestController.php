<?php

namespace App\Http\Controllers\MindBridge;


use DB;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\NiveauCentrale;
use App\Models\MatiereCentrale;
use App\Models\MindBridge\Step;
use App\Models\MindBridge\Test;
use App\Models\MindBridge\Chapter;
use App\Models\MindBridge\Content;
use Illuminate\Support\Collection;
use App\Models\MindBridge\Category;
use App\Models\MindBridge\Question;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use App\Models\MindBridge\Observation;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Service\TemplateNotificationService;
use App\Http\Requests\MindBridge\TestRequest;
use App\Models\MindBridge\EtudiantTestStatus;
use App\Models\MindBridge\MindBridgeEtudiant;
use App\Models\MindBridge\EtudiantTestAnswers;
use Illuminate\Validation\ValidationException;
use App\Models\MindBridge\EtudiantMatiereLevel;
use App\Models\MindBridge\EtudiantTestAssignment;
use App\Http\Resources\MindBridge\CategoryResource;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Http\Resources\MindBridge\EtudiantMindBridgeResource;


class MindBridgeTestController extends Controller
{

    public function index(Request $request)
    {
        try {
            $categoryId = $request->query('category_id');
            $matiereId = $request->query('matiere');
            $niveauId = $request->query('niveau');
            $targetId = $request->query('target');
            $limit = $request->query('limit', 10);
            $page = $request->query('page', 1);

            $query = Test::query();

            if ($categoryId) {
                $query->where('category_id', $categoryId);
            }

            if ($matiereId) {
                $query->where('matiere_id', $matiereId);
            }

            if ($niveauId) {
                $query->where('niveau_id', $niveauId);
            }

            if ($targetId) {

                $query->where('target', $targetId)
                    ->where(function ($q) {
                        $q->where('type', 'exam_simulation')
                            ->orWhere('type', 'culture_general');
                    });
            }

            $total = $query->count();
            $tests = $query->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->with(['matiere', 'niveau', 'content', 'content.niveau', 'category', 'creator'])
                ->get();

            $totalPages = ceil($total / $limit);

            return response()->json([
                'data' => $tests,
                'current_page' => (int) $page,
                'total_pages' => (int) $totalPages,
                'per_page' => (int) $limit,
                'total_items' => (int) $total,
            ], 200);
        } catch (\Exception $e) {
            \Log::error("Error fetching tests: ", ['exception' => $e]);
            return response()->json(['error' => 'An error occurred while fetching tests.'], 500);
        }
    }



    public function store(Request $request)
    {

        try {

            $request->validate([
                'name' => 'required|string|max:255',
                'target' => 'nullable|string|max:255',
                'category_id' => 'required',
            ]);

            $test = Test::create($request->all());

            return response()->json($test, 201);
        } catch (ValidationException $e) {
            \Log::info("Validation error: " . json_encode($e->errors()));

            return response()->json([
                'error' => $e->errors()
            ], 422);
        } catch (Exception $e) {
            Log::error($e);
        }
    }


    public function show($id)
    {
        try {
            $test = Test::with([
                'matiere',
                'niveau',
                'chapters',
                'content',
                'content.niveau',
                'category',
                'creator',
                'steps',
                'steps.question',
                'steps.question.options'
            ])->findOrFail($id);
            return response()->json($test, 200);
        } catch (ModelNotFoundException $e) {
            \Log::error("Test not found: ID {$id}", ['exception' => $e]);
            return response()->json([
                'error' => 'Test not found.',
            ], 404);
        } catch (Exception $e) {
            \Log::error($e);
        }
    }



    public function storeAll(TestRequest $request)
    {
        try {
            DB::beginTransaction();
            Log::info("====== Entering MindBridgeTestController::storeAll Method ======");
            $testData = $request->input('test');
            $categoryId = $testData['category_id'];

            // Remove created_by from request to prevent foreign key violations
            // Only set it if the user actually exists in the centrale database
            unset($testData['created_by']);

            if (auth()->check() && auth()->user()) {
                $userId = auth()->user()->id;
                // Verify the user exists in the centrale database using raw query
                $userExists = DB::connection('centrale')->table('users')->where('id', $userId)->exists();
                if ($userExists) {
                    $testData['created_by'] = $userId;
                    Log::info("User ID: {$userId} exists in centrale database");
                } else {
                    Log::warning("User ID: {$userId} does NOT exist in centrale database - created_by will be null");
                    $testData['created_by'] = null;
                }
            } else {
                Log::info("No authenticated user - created_by will be null");
                $testData['created_by'] = null;
            }
            if ($categoryId === 4) {
                $contentId = $testData['content_id'];
                if (!is_numeric($contentId)) {
                    return response()->json([
                        'error' => 'invalid content_id.',
                    ], 422);
                }
                $content = Content::find($contentId);
                if (!$content) {
                    return response()->json([
                        'error' => 'The target lesson does not exist.',
                    ], 404);
                }
            }

            if ($categoryId === 8) {
                if (!is_numeric($testData['niveau_id']) || !is_numeric($testData['matiere_id'])) {
                    return response()->json([
                        'error' => 'invalid niveau_id or matiere_id.',
                    ], 422);
                }
                $matiere = MatiereCentrale::find($testData['matiere_id']);
                $niveux = NiveauCentrale::find($testData['niveau_id']);
                if (!$matiere || !$niveux) {
                    return response()->json([
                        'error' => 'The target matiere or niveau does not exist.',
                    ], 404);
                }
                $testData['matiere_id'] = $matiere->id;
                $testData['niveau_id'] = $niveux->id;

                $chapters = $request->input('chapters');
                if (!is_array($chapters) || empty($chapters)) {
                    return response()->json([
                        'error' => 'The chapters field is required and must be an array.',
                    ], 422);
                }

                $validChapterIds = Chapter::whereIn('id', $chapters)->pluck('id')->toArray();
    
                if (count($validChapterIds) !== count($chapters)) {
                    return response()->json([
                        'error' => 'One or more of the provided chapters do not exist.',
                    ], 404);
                }

                $testData['chapters'] = $chapters;

            }

            if ($categoryId === 5) {
                if (!is_numeric($testData['niveau_id']) || !is_numeric($testData['matiere_id'])) {
                    return response()->json([
                        'error' => 'invalid niveau_id or matiere_id.',
                    ], 422);
                }
                $matiere = MatiereCentrale::find($testData['matiere_id']);
                $niveux = NiveauCentrale::find($testData['niveau_id']);
                if (!$matiere || !$niveux) {
                    return response()->json([
                        'error' => 'The target matiere or niveau does not exist.',
                    ], 404);
                }
                $testData['matiere_id'] = $matiere->id;
                $testData['niveau_id'] = $niveux->id;
            }

            $test = Test::create($testData);
            // attach chapters to the test
            if (isset($testData['chapters']) && is_array($testData['chapters'])) {
                $test->chapters()->sync($testData['chapters']);
            }

            $steps = $request->input('steps');

            foreach ($steps as $index => $stepData) {
                $stepType = $request->input("steps.{$index}.type");
                $options = $request->input("steps.{$index}.question.options");
                $questionData = $stepData['question'];

                if ($stepType === 'true_false') {
                    $questionData['is_true'] = $questionData['is_true'] ?? false;
                    $questionData['is_false'] = !$questionData['is_true'];

                    \Log::info($questionData);
                }else {
                    if (is_null($options) || empty($options)) {
                        throw ValidationException::withMessages([
                            'options' => 'The options should be provided for this question.'
                        ]);
                    }

                    $validator = Validator::make($request->all(), [
                        "steps.{$index}.question.options.*.name" => 'required|string',
                        "steps.{$index}.question.options.*.icon" => 'nullable|string',
                        "steps.{$index}.question.options.*.isCorrect" => 'nullable',
                    ]);

                    if ($validator->fails()) {
                        throw new ValidationException($validator);
                    }
                }

                if (isset($stepData['question_id']) && !empty($stepData['question_id'])) {
                    $stepData['question_id'] = $stepData['question_id'];
                    unset($stepData['question']);
                    $test->steps()->create($stepData);
                    continue;
                }

                $options = $questionData['options'] ?? [];
                unset($questionData['options']);

                if ($questionData['type'] !== 'text' && $request->hasFile("steps.{$index}.question.content")) {
                    $file = $request->file("steps.{$index}.question.content");
                    $directory = "mindbridge/uploads/{$questionData['type']}";
                    if (!Storage::disk('public')->exists($directory)) {
                        Storage::disk('public')->makeDirectory($directory);
                    }

                    $uniqueFilename = time() . '-' . Str::random(5) . '.' . $file->getClientOriginalExtension();
                    $path = $file->storeAs($directory, $uniqueFilename, 'public');
                    $questionData['content'] = $path;
                } elseif ($questionData['type'] === 'text' && isset($questionData['content'])) {
                    $questionData['content'] = $questionData['content'];
                } else {
                    throw new ValidationException(Validator::make([], [
                        'content' => 'required|string',
                    ], [
                        'content.required' => 'The content field is required for the question.',
                    ]));
                }

                $questionData['category_id'] = $categoryId;

                if ($request->hasFile("steps.{$index}.question.image_path")) {
                    $file = $request->file("steps.{$index}.question.image_path");
                    $directory = "mindbridge/uploads/questionsImages";
                    $uniqueFilename = time() . '-' . Str::random(5) . '.' . $file->getClientOriginalExtension();
                    $path = $file->storeAs($directory, $uniqueFilename, 'public');
                    $questionData['image_path'] = $path;
                }
                $question = Question::create($questionData);
                foreach ($options as $optionData) {
                    $question->options()->create($optionData);
                }

                $stepData['question_id'] = $question->id;
                unset($stepData['question']);
                $test->steps()->create($stepData);
            }

            DB::commit();
            return response()->json(['message' => 'Test Created Successfully.', 'object' => $test], 201);
        } catch (ValidationException $e) {
            DB::rollBack();
            Log::error("Validation Error in storeAll: ", ['exception' => $e]);
            return response()->json(['error' => $e->errors()], 422);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Error in storeAll: ", ['exception' => $e]);
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    public function updateAll(Request $request, $id)
    {
        try {
            DB::connection('mind_bridge')->beginTransaction();
            Log::info("====== Entering MindBridgeTestController::updateAll Method ======");
    
            
            $test = Test::find($id);
    
            if (!$test) {
                return response()->json(['error' => 'Test not found.'], 404);
            }
    
            
            $testData = $request->input('test');
            unset($testData['type']); 
            $test->update($testData);
    
            
            $steps = $request->input('steps');
            $existingStepIds = $test->steps()->pluck('id')->toArray();
    
            $newStepIds = [];
            foreach ($steps as $stepData) {
                if (isset($stepData['id']) && in_array($stepData['id'], $existingStepIds)) {
                    
                    $step = Step::find($stepData['id']);
                    $step->update(['order' => $stepData['order']]);
                    $newStepIds[] = $stepData['id'];
                } elseif (isset($stepData['question'])) {
                    
                    $questionData = $stepData['question'];
                    $options = $questionData['options'] ?? [];
                    unset($questionData['options']); 
    
                    
                    $question = Question::create($questionData);
    
                    
                    foreach ($options as $optionData) {
                        $question->options()->create($optionData);
                    }
    
                    
                    $stepData['question_id'] = $question->id;
                    unset($stepData['question']); 
    
                    
                    $newStep = $test->steps()->create($stepData);
                    $newStepIds[] = $newStep->id;
                }
            }
    
            
            $stepsToDelete = array_diff($existingStepIds, $newStepIds);
            Step::destroy($stepsToDelete);
    
            DB::connection('mind_bridge')->commit();
            return response()->json(['message' => 'Test Updated Successfully.', 'object' => $test], 200);
        } catch (ValidationException $e) {
            DB::connection('mind_bridge')->rollBack();
            Log::error("Validation Error in updateAll: ", ['exception' => $e]);
            return response()->json(['error' => $e->errors()], 422);
        } catch (Exception $e) {
            DB::connection('mind_bridge')->rollBack();
            Log::error("Error in updateAll: ", ['exception' => $e]);
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }
    
    





    public function getTestFirsTStep($id)
    {
        try {
            $test = Test::find($id);
            $firstStep = $test->steps()->with('question', 'question.options')->where('order', 1)->first();

            if (!$firstStep) {
                return response()->json([
                    'error' => 'First step not found',
                ], 404);
            }
            return response()->json([
                "test" => $test,
                "step" =>  $firstStep
            ], 200);
        } catch (Exception $e) {
            Log::error($e);
            return response()->json([
                'error' => 'An unexpected error occurred.',
            ], 404);
        }
    }


    public function destroy($id)
    {
        try {
            $test = Test::find($id);
            $test->delete();
            return response()->json([
                'message' => 'Test deleted successfully'
            ], 204);
        } catch (Exception $e) {
            Log::error($e);
        }
    }

    public function testBoardCategories(Request $request)
    {
        try {
            $categories = Category::with(['subcategories' => function ($query) {
                $query->where('is_bo', true)->with('subcategories');
            }])->get();


            if (count($categories) === 0) {
                return response()->json([
                    'error' => 'Categories is empty',
                ], 404);
            }
            return response()->json([
                'test_models' => CategoryResource::make($categories->firstWhere('id', 1)),
                'mental_health' => CategoryResource::make($categories->firstWhere('id', 2)),
            ], 200);
        } catch (Exception $exception) {
            Log::error('<===== Error in testBoardCategories =====>: ' . $exception);
            return response()->json([
                'error' => 'An unexpected error occurred.',
            ], 500);
        }
    }

    public function testBoardCategoriesMo(Request $request)
    {
        try {
            $categories = Category::with(['subcategories' => function ($query) {
                $query->where('is_mobile', true)
                    ->orderBy('position', 'asc');
            }])
                ->where('id', 1)
                ->first();

            $testId = $request->input('test_id');


            if (empty($categories)) {
                return response()->json([
                    'error' => 'Categories is empty',
                ], 404);
            }

            $user_id = auth()->user()->id;
            $etudiant = MindBridgeEtudiant::where('user_id', $user_id)->first();
            if (!$etudiant) {
                return response()->json(['message' => 'Etudiant not found for user'], 404);
            }

            $welcomeMessage = null;
            $cacheKey = "welcome_message_user_{$user_id}";
            $cacheDuration = 360;
    
            if (!Cache::has($cacheKey)) {
                $welcomeMessages = config('motivational_messages.welcome');
                $randomWelcomeMessage = $welcomeMessages[array_rand($welcomeMessages)];
    
                $randomWelcomeMessage = str_replace(':prenom', $etudiant->first_name, $randomWelcomeMessage);
    
                Cache::put($cacheKey, $randomWelcomeMessage, $cacheDuration);
    
                $welcomeMessage = $randomWelcomeMessage;
            } else {
                // on peux recuperer le message de bienvenue depuis le cache
                $welcomeMessage = null;
            }

            $assignment = EtudiantTestAssignment::where('etudiant_id', $etudiant->id)
                ->where('status', 'ATTRIBUE')
                ->with('test')
                ->first();

            $testToDo = null;
            if ($assignment) {
                $testToDo = Test::with(['content', 'steps', 'steps.question', 'steps.question.options'])
                    ->where("id", $assignment->test_id)
                    ->first();
            }

            $completedSondageIds = EtudiantTestStatus::where('etudiant_id', $etudiant->id)
                ->pluck('test_id')
                ->toArray();

            $testSondage = Test::with(['content', 'steps', 'steps.question', 'steps.question.options'])
                ->where('category_id', 7)
                ->whereNotIn('id', $completedSondageIds)
                ->first();
            
            $mentalHealthTest = null;
            \Log::info("testId: " . $testId);

            if ($testId){
                $mentalHealthTest = Test::with(['content', 'steps', 'steps.question', 'steps.question.options'])
                ->where("id", $testId)
                ->first();
            }


            return response()->json([
                'test_models' => new CategoryResource($categories),
                'welcome_message' => $welcomeMessage,
                'test_to_do' => $testToDo,
                'sondages_to_do' => $testSondage,
                'mental_health_to_do' => $mentalHealthTest
            ], 200);
        } catch (Exception $exception) {
            Log::error('<===== Error in testBoardCategoriesMo =====>: ' . $exception);
            return response()->json([
                'error' => 'An unexpected error occurred.',
            ], 500);
        }
    }


    public function assignTestToEtudiant($etudiantId, $testId)
    {
        $existingAssignment = EtudiantTestAssignment::where('etudiant_id', $etudiantId)
            ->where('status', 'ATTRIBUE')
            ->where('test_id', $testId)
            ->first();

        if ($existingAssignment) {
            return response()->json(['message' => 'Test already assigned to this student'], 200);
        }

        EtudiantTestAssignment::create([
            'etudiant_id' => $etudiantId,
            'test_id'     => $testId,
            'assigned_at' => now(),
        ]);

        return response()->json(['message' => 'Test successfully assigned to the student'], 201);
    }



    public function getTestByCategoryId($categoryId)
    {
        try {
            $tests = Test::with('steps', 'steps.question', 'steps.question.options')->where('category_id', $categoryId)->get();

            if ($tests->isEmpty()) {
                return response()->json(['message' => 'No test for this category', 'data' => []], 404);
            }

            return response()->json(['message' => 'Test for this category', 'data' => $tests], 200);
        } catch (Exception $exception) {
            Log::error('Error in getTestByCategoryId: ' . $exception->getMessage());
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    /**
     * Get all tests from all children categories of a parent category
     * This endpoint gets all child categories and returns all tests in one list
     *
     * @param int $categoryId - Parent category ID (e.g., 2 for Sante Mentale)
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllTestsByParentCategory($categoryId)
    {
        try {
            // Get the parent category
            $parentCategory = Category::find($categoryId);

            if (!$parentCategory) {
                return response()->json(['message' => 'Parent category not found', 'data' => []], 404);
            }

            // Get all child category IDs recursively
            $childCategoryIds = $this->getChildCategoryIds($categoryId);

            // Add the parent category ID itself
            $allCategoryIds = array_merge([$categoryId], $childCategoryIds);

            // Get all tests from all these categories
            $tests = Test::with('steps', 'steps.question', 'steps.question.options')
                ->whereIn('category_id', $allCategoryIds)
                ->get();

            if ($tests->isEmpty()) {
                return response()->json(['message' => 'No tests found for this category and its children', 'data' => []], 404);
            }

            return response()->json([
                'message' => 'All tests from category and its children',
                'data' => $tests,
                'total' => $tests->count(),
                'parent_category' => $parentCategory->name
            ], 200);
        } catch (Exception $exception) {
            Log::error('Error in getAllTestsByParentCategory: ' . $exception->getMessage());
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    /**
     * Recursively get all child category IDs
     *
     * @param int $parentId - Parent category ID
     * @return array - Array of all child category IDs
     */
    private function getChildCategoryIds($parentId)
    {
        $childIds = [];

        // Get direct children
        $children = Category::where('parent_id', $parentId)->get();

        foreach ($children as $child) {
            $childIds[] = $child->id;
            // Recursively get grandchildren
            $grandchildIds = $this->getChildCategoryIds($child->id);
            $childIds = array_merge($childIds, $grandchildIds);
        }

        return $childIds;
    }

    public function getStudentsWithTestsByCategoryId(Request $request, $categoryId)
    {
        try {
            $limit = $request->query('limit', 10);
            $page = $request->query('page', 1);
    
            $query = EtudiantTestStatus::with(['etudiant.niveau', 'test', 'test.category'])
                ->whereHas('test', function ($query) use ($categoryId) {
                    $query->where('category_id', $categoryId);
                });
    
            $total = $query->count();
            $students = $query->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->get()
                ->map(function ($testStatus) {
                    return [
                        'etudiant_name' => $testStatus->etudiant->name ?? null,
                        'etudiant_avatar' => $testStatus->etudiant->avatar ?? null,
                        'niveau_name' => $testStatus->etudiant->niveau->name ?? null,
                        'student_score' => $testStatus->score ?? 0,
                        'total_score' => $testStatus->score ?? 0,
                        'student_comment' => $testStatus->comment ?? null,
                        'test_title' => $testStatus->test->title ?? null,
                        'test_status' => $testStatus->status ?? 'EN COURS',
                        'test_category' => $testStatus->test->category->name ?? null,
                        'date_creation' => $testStatus->created_at ?? null,
                        'started_at' => $testStatus->started_at ?? null,
                        'completed_at' => $testStatus->completed_at ?? null,
                    ];
                });
    
            $totalPages = ceil($total / $limit);
    
            if ($students->isEmpty()) {
                return response()->json([
                    'message' => 'No students participated in tests for this category',
                    'data' => []
                ], 200);
            }
    
            return response()->json([
                'data' => $students,
                'current_page' => (int) $page,
                'total_pages' => (int) $totalPages,
                'per_page' => (int) $limit,
                'total_items' => (int) $total,
            ], 200);
        } catch (Exception $exception) {
            Log::error('Error in getStudentsWithTestsByCategoryId: ' . $exception->getMessage());
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    public function getParticipantsMenalHealth(Request $request)
    {
        try {
            $limit = $request->query('limit', 10);
            $page = $request->query('page', 1);
            $categoryId = $request->query('category_id');
            $niveauId = $request->query('niveau');
            $etudiant = $request->query('etudiant');
    
            $query = EtudiantTestStatus::with(['etudiant.niveau', 'test', 'test.category', 'test.niveau'])
                ->whereHas('test.category', function ($query) {
                    $query->where('parent_id', 2);
                })
                ->when($categoryId, function ($q) use ($categoryId) {
                    $q->whereHas('test', function ($query) use ($categoryId) {
                        $query->where('category_id', $categoryId);
                    });
                })
                ->when($niveauId, function ($q) use ($niveauId) {
                    $q->whereHas('etudiant', function ($query) use ($niveauId) {
                        $query->where('niveau_id', $niveauId);
                    });
                })
                ->when($etudiant, function ($q) use ($etudiant) {
                    $q->whereHas('etudiant', function ($query) use ($etudiant) {
                        $query->where('first_name', 'like', "%$etudiant%")
                            ->orWhere('last_name', 'like', "%$etudiant%")
                            ->orWhere('identifiant', 'like', "%$etudiant%");
                    });
                })
                ->latest('created_at');
    
            $total = $query->count();
            $students = $query->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->get()
                ->map(function ($testStatus) {
                    return [
                        'id' => $testStatus->id,
                        'etudiant' => new EtudiantMindBridgeResource($testStatus->etudiant),
                        'test_title' => $testStatus->test->title ?? null,
                        'category' => $testStatus->test->category->name ?? null,
                        'date_creation' => $testStatus->created_at ?? null,
                        'comment' => fake()->sentence(),
                        'test_status' => $testStatus->status ?? 'EN COURS',
                        'total_score' => $testStatus->score ?? 0,
                        'student_score' => $testStatus->score ?? 0,
                        'started_at' => $testStatus->started_at ?? null,
                        'completed_at' => $testStatus->completed_at ?? null,
                    ];
                });
    
            $totalPages = ceil($total / $limit);
    
            return response()->json([
                'data' => $students,
                'current_page' => (int) $page,
                'total_pages' => (int) $totalPages,
                'per_page' => (int) $limit,
                'total_items' => (int) $total,
            ], 200);
        } catch (Exception $exception) {
            Log::error('Error in getParticipantsMenalHealth: ' . $exception->getMessage());
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }


    public function runMentalHealthTestBySubCategory(Request $request){
        try {
            $request->validate([
                'subcategory_id' => 'required',
                'etudiant_id' => 'required',
            ]);

            $subcategoryId = $request->input('subcategory_id');
            $etudiantId = $request->input('etudiant_id');

            $test = Test::with(['steps', 'steps.question', 'steps.question.options'])
                ->where('id', $subcategoryId)
                ->first();
            
            if (!$test){
                return response()->json(['error' => 'Test not found.'], 404);
            }

            $etudiant = MindBridgeEtudiant::with('users')->where('id', $etudiantId)->first();


            if (!$etudiant) {
                return response()->json(['error' => 'Etudiant not found.'], 404);
            }

            $user = $etudiant->users;

            $existingAssignment = EtudiantTestAssignment::where('etudiant_id', $etudiantId)
            ->where('status', 'ATTRIBUE')
            ->where('test_id', $test->id)
            ->first();

            if ($existingAssignment) {
                return response()->json(['message' => 'Test already assigned to this student'], 200);
            }

            EtudiantTestAssignment::create([
                'etudiant_id' => $etudiantId,
                'test_id'     => $test->id,
                'assigned_at' => now(),
            ]);

            if ($user && $user->fcm_token) {
                TemplateNotificationService::sendNotification(
                    $user->fcm_token,
                    'Nouveau test',
                    'Vous avez un nouveau test.',
                    [
                        'type' => 'mental_health_test',
                        'test_id' => (string) $test->id,  // Converted to string
                    ]
                );
            }

            return response()->json(['message' => 'Test successfully assigned to the student'], 201);

        } catch (ValidationException $e){
            Log::error("Validation error: " . json_encode($e->errors()));
            return response()->json([
                'error' => $e->errors()
            ], 422);
        }  catch (Exception $e) {
            Log::error($e);
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    public function latestHebdomadaireTest(Request $request)
    {
        \Log::info("-----------LatestHebdomadaireTest--------------");
    
        try {
            $today = Carbon::now()->format('Y-m-d');
            \Log::info($today);

            $etudiant = MindBridgeEtudiant::where('user_id', auth()->user()->id)->first();
            //TODO: Fix attempt to read property "id" on null
            $etudiantId = $etudiant->id;

            $tests = Test::with('content', 'steps', 'steps.question', 'steps.question.options', 'matiere')
                ->where('category_id', 8)
                ->where('niveau_id', $etudiant->niveau_id)
                ->whereDate('challenge_date_start', '<=', $today)
                ->whereDate('challenge_date_end', '>=', $today)
                ->whereDoesntHave('etudiantTestStatuses', function ($query) use ($etudiantId) {
                    $query->where('etudiant_id', $etudiantId)
                          ->where('status', 'TERMINE');
                })
                ->orderBy('created_at', 'desc')
                ->get();
    
            if ($tests->isEmpty()) {
                return response()->json(['error' => 'No Challenge'], 404);
            }
    
            return response()->json([
                'tests' => $tests,
                'next_difficulty_level' => null,
                'restarted' => true,
            ], 200);
    
        } catch (\Exception $e) {
            \Log::info("-----------LatestHebdomadaireTest-Error--------------");
            \Log::info($e);
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }


    public function recommandationTest(Request $request)
    {
        try {
            $userId = auth()->id();
            Log::info("[RecommandationTest] Start for user {$userId}");

            // 1) Load student + their niveau→matieres + notes + existing levels
            $etudiant = MindBridgeEtudiant::with([
                'notes',
                'etudiantMatiereLevel',
                'niveau.matieres',
            ])->where('user_id', $userId)->firstOrFail();

            $etudiantNiveauMatiereIds = $etudiant->niveau->matieres->pluck('id')->all();
            Log::info('[RecommandationTest] Allowed matières: ' . implode(',', $etudiantNiveauMatiereIds));

            // 2) Compute & upsert levels where we have notes
            $etudiant->notes
                ->whereIn('matiere_id', $etudiantNiveauMatiereIds)
                ->groupBy('matiere_id')
                ->each(function (Collection $notesForMatiere, $matiereId) use ($etudiant) {
                    $latestNote = $notesForMatiere->sortByDesc('created_at')->first();
                    $minScore   = collect($latestNote->notes)->min();

                    $level = $minScore < 10
                        ? 'faible'
                        : ($minScore < 15 ? 'moyen' : 'fort');

                    EtudiantMatiereLevel::updateOrCreate(
                        ['etudiant_id' => $etudiant->id, 'matiere_id' => $matiereId],
                        ['level'       => $level]
                    );

                    Log::info("[RecommandationTest] Matière #{$matiereId} → level “{$level}”");
                });

            // Reload current levels for allowed matières
            $levels = EtudiantMatiereLevel::where('etudiant_id', $etudiant->id)
                ->whereIn('matiere_id', $etudiantNiveauMatiereIds)
                ->get();

            $knownEtidiantMatiersLevelIds   = $levels->pluck('matiere_id')->all();
            $unknownEtudiantMatiersLevelIds = array_diff($etudiantNiveauMatiereIds, $knownEtidiantMatiersLevelIds);

            Log::info('[RecommandationTest] Known matières: ' . implode(',', $knownEtidiantMatiersLevelIds));
            Log::info('[RecommandationTest] Unknown matières: ' . implode(',', $unknownEtudiantMatiersLevelIds));

            // 3) Select matière & gather questions
            if (count($unknownEtudiantMatiersLevelIds) > 0) {
                // New matière → MIX test
                $selectedId    = Arr::random($unknownEtudiantMatiersLevelIds);
                $selectedLevel = 'mix';
                Log::info("[RecommandationTest] Selected NEW matière #{$selectedId} → MIX test");

                $questions = collect();
                foreach (['facile' => 3, 'moyen' => 3, 'difficile' => 3] as $diff => $cnt) {
                    $batch = Question::with('options')
                        ->where('matiere_id', $selectedId)
                        ->where('niveau_id', $etudiant->niveau_id)
                        ->where('difficulty', $diff)
                        ->inRandomOrder()
                        ->limit($cnt)
                        ->get();

                    if ($batch->count() < $cnt) {
                        Log::warning("[RecommandationTest] Not enough “{$diff}” questions");
                        return response()->json([
                            'error' => "Pas assez de questions “{$diff}” pour construire le test."
                        ], 404);
                    }
                    $questions = $questions->concat($batch);
                }

            } else {
                // Existing matières → priorité faible → moyen → fort
                $byLevel = $levels
                    ->groupBy('level')
                    ->map->pluck('matiere_id')
                    ->toArray();  // ensure plain arrays for Arr::random()

                // Pick the highest-priority non-empty bucket
                if (! empty($byLevel['faible'] ?? [])) {
                    $selectedLevel = 'faible';
                }
                elseif (! empty($byLevel['moyen'] ?? [])) {
                    $selectedLevel = 'moyen';
                }
                elseif (! empty($byLevel['fort'] ?? [])) {
                    $selectedLevel = 'fort';
                }
                else {
                    Log::warning("[RecommandationTest] No existing matières in any level");
                    return response()->json([
                        'error' => 'Aucune matière existante disponible pour générer le test.'
                    ], 404);
                }

                $questionCount = 5;
                Log::info("[RecommandationTest] Selected EXISTING level “{$selectedLevel}”");

                // Map student‐level → question difficulty
                $difficultyMap = [
                    'faible' => 'facile',
                    'moyen'  => 'moyen',
                    'fort'   => 'difficile',
                ];
                $diffKey = $difficultyMap[$selectedLevel];

                // Now safe to random-pick matière
                $pool       = $byLevel[$selectedLevel];
                $selectedId = Arr::random($pool);
                Log::info("[RecommandationTest] Selected EXISTING matière #{$selectedId} at “{$selectedLevel}” " .
                        "→ querying “{$diffKey}” ({$questionCount} q)");

                $questions = Question::with('options')
                    ->where('matiere_id', $selectedId)
                    ->where('niveau_id', $etudiant->niveau_id)
                    ->where('difficulty', $diffKey)
                    ->inRandomOrder()
                    ->limit($questionCount)
                    ->get();

                if ($questions->count() < $questionCount) {
                    Log::warning("[RecommandationTest] Not enough “{$diffKey}” questions");
                    return response()->json([
                        'error' => "Pas assez de questions “{$diffKey}” pour construire le test."
                    ], 404);
                }
            }

            // Shuffle & wrap for response
            $questions = $questions->shuffle();
            $steps = $questions->values()->map(function ($q, $i) {
                return [
                    'order'         => $i + 1,
                    'required'      => true,
                    'type'          => $q->type,
                    'question_id'   => $q->id,
                    'question'      => [
                        'id'            => $q->id,
                        'content'       => $q->content,
                        'description'   => $q->description,
                        'image'         => $q->image_path,
                        'answer'        => $q->answer,
                        'response_type' => $q->response_type,
                        'is_true'       => $q->is_true,
                        'is_false'      => $q->is_false,
                        'options'       => $q->options->map(fn($o) => [
                            'id'        => $o->id,
                            'name'      => $o->name,
                            'isCorrect' => $o->isCorrect,
                            'icon'      => $o->icon,
                        ]),
                    ],
                ];
            });

            $test = [
                'matiere_id' => $selectedId,
                'level'      => $selectedLevel,
                'title'      => "Recommandation test",
                'steps'      => $steps,
            ];

            return response()->json(['test' => $test], 200);

        } catch (\Throwable $e) {
            Log::error('[RecommandationTest] ERROR: ' . $e->getMessage());
            return response()->json([
                'error' => 'Une erreur est survenue lors de la génération du test'
            ], 500);
        }
    }




    public function saveRecommandationResult(Request $request)
    {
        try {
            Log::info("----------- saveRecommandationResult --------------");
            Log::info("Request: " . json_encode($request->all()));

            $request->validate([
                'matiere_id'               => 'required',
                'correct_answers_count'    => 'required|integer',
            ], [
                'matiere_id.required'               => 'Matière ID is required.',
                'correct_answers_count.required'    => 'Correct answers count is required.',
                'correct_answers_count.integer'     => 'Correct answers count must be an integer.',
            ]);

            $etudiant = MindBridgeEtudiant::where('user_id', auth()->id())->first();
            if (!$etudiant) {
                return response()->json(['error' => 'Etudiant not found.'], 404);
            }

            $matiere = MatiereCentrale::find($request->input('matiere_id'));
            if (! $matiere) {
                return response()->json(['error' => 'Matiere not found.'], 404);
            }

            $correctAnswersCount = $request->input('correct_answers_count');

            if ($correctAnswersCount < 5) {
                $level = 'faible';
            } elseif ($correctAnswersCount < 8) {
                $level = 'moyen';
            } else {
                $level = 'fort';
            }

            EtudiantMatiereLevel::updateOrCreate(
                ['etudiant_id' => $etudiant->id, 'matiere_id' => $matiere->id],
                ['level'       => $level]
            );

            Log::info("[RecommandationTest] Matière #{$matiere->id} → level “{$level}”");

            Log::info("Correct answers count: {$correctAnswersCount}");
            Log::info("----------- saveRecommandationResult – OK --------------");

            return response()->json([
                'message'          => 'Tests stored successfully.',
                'feedback_message' => 'Merci pour votre participation.',
            ], 200);

        } catch (ValidationException $e) {
            Log::error("Validation error: " . json_encode($e->errors()));
            return response()->json(['error' => $e->errors()], 422);

        } catch (Exception $e) {
            Log::error($e);
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    /**
     * Get student mental health test history with all details
     * GET /api/mind_bridge/etudiant/{id}/test-history
     */
    public function getStudentTestHistory($etudiantId)
    {
        try {
            $etudiant = MindBridgeEtudiant::find($etudiantId);
            if (!$etudiant) {
                return response()->json(['error' => 'Student not found'], 404);
            }

            // Get all completed mental health tests for the student
            $testHistory = EtudiantTestStatus::where('etudiant_id', $etudiantId)
                ->where('status', 'TERMINE')
                ->with([
                    'test' => function ($query) {
                        $query->with(['category', 'scoringRules']);
                    }
                ])
                ->orderBy('completed_at', 'desc')
                ->get();

            // Enrich each test with additional details
            $enrichedHistory = $testHistory->map(function ($testStatus) use ($etudiantId) {
                $test = $testStatus->test;

                // Get test answers
                $answers = EtudiantTestAnswers::where('etudiant_id', $etudiantId)
                    ->where('test_id', $test->id)
                    ->with(['question', 'question.options'])
                    ->get();

                // Get scoring rules and calculate interpretation
                $scoringRules = $test->scoringRules;

                \Log::info('Scoring rules: ' . json_encode($scoringRules));
                $interpretation = null;
                $feedback = null;
                $recommendation = null;

                if ($scoringRules && $testStatus->score !== null) {
                    // Calculate percentage
                    $totalQuestions = $answers->count();
                    $percentage = $totalQuestions > 0 ? ($testStatus->score / $totalQuestions) * 100 : 0;

                    // Find matching scoring rule
                    $matchingRule = $scoringRules->first(function ($rule) use ($percentage) {
                        return $percentage >= $rule->min_percentage && $percentage <= $rule->max_percentage;
                    });

                    if ($matchingRule) {
                        $interpretation = $matchingRule->interpretation;
                        $feedback = $matchingRule->feedback;
                        $recommendation = $matchingRule->recommendation;
                    }
                }

                // Get observations related to this test
                $observations = Observation::where('test_id', $test->id)
                    ->where('active', true)
                    ->get();

                return [
                    'id' => $testStatus->id,
                    'test' => [
                        'id' => $test->id,
                        'title' => $test->title,
                        'description' => $test->description,
                        'type' => $test->type,
                        'category' => $test->category,
                        'timer' => $test->timer,
                        'difficulty_level' => $test->difficulty_level,
                    ],
                    'status' => $testStatus->status,
                    'score' => $testStatus->score,
                    'started_at' => $testStatus->started_at,
                    'completed_at' => $testStatus->completed_at,
                    'answers' => $answers,
                    'scoring' => [
                        'total_questions' => $answers->count(),
                        'score' => $testStatus->score,
                        'percentage' => $totalQuestions > 0 ? round(($testStatus->score / $totalQuestions) * 100, 2) : 0,
                        'interpretation' => $interpretation,
                        'feedback' => $feedback,
                        'recommendation' => $recommendation,
                    ],
                    'observations' => $observations,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $enrichedHistory,
                'total_tests' => $enrichedHistory->count(),
            ], 200);
        } catch (Exception $e) {
            Log::error('Error in getStudentTestHistory: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred while fetching test history'], 500);
        }
    }

    /**
     * Get Mental Health Risk Dashboard Analytics
     * GET /api/mind_bridge/mental-health-risk-dashboard
     */
    public function getMentalHealthRiskDashboard(Request $request)
    {
        try {
            $currentYear = Carbon::now()->year;
            $dateFrom = $request->query('date_from', Carbon::now()->startOfYear()->format('Y-m-d'));
            $dateTo = $request->query('date_to', Carbon::now()->format('Y-m-d'));
            $niveauId = $request->query('niveau_id');
            $riskLevel = $request->query('risk_level'); // 'high', 'medium', 'low'

            // Mental Health category ID (parent category)
            $mentalHealthCategoryId = 2;

            // Get all mental health test categories (including subcategories)
            $mentalHealthCategories = Category::where('parent_id', $mentalHealthCategoryId)
                ->orWhere('id', $mentalHealthCategoryId)
                ->pluck('id');

            // Base query for completed mental health tests
            // Use the same date filtering as studentsWithMultipleTests query for consistency
            $baseQuery = EtudiantTestStatus::where('status', 'TERMINE')
                ->whereHas('test', function ($query) use ($mentalHealthCategories) {
                    $query->whereIn('category_id', $mentalHealthCategories);
                })
                ->whereYear('completed_at', $currentYear);

            if ($niveauId) {
                $baseQuery->whereHas('etudiant', function ($query) use ($niveauId) {
                    $query->where('niveau_id', $niveauId);
                });
            }





            \Log::info('mentalHealthCategories: ' . json_encode($mentalHealthCategories));
            \Log::info('currentYear: ' . $currentYear);
            \Log::info('dateFrom: ' . $dateFrom);
            \Log::info('dateTo: ' . $dateTo);



            


            // Get students with multiple tests (>=2 tests in current year)
            // First, let's debug the query step by step
            $debugQuery = EtudiantTestStatus::select('etudiant_id', \DB::raw('COUNT(*) as test_count'))
                ->where('status', 'TERMINE')
                ->whereHas('test', function ($query) use ($mentalHealthCategories) {
                    $query->whereIn('category_id', $mentalHealthCategories);
                })
                ->whereYear('completed_at', $currentYear)
                ->groupBy('etudiant_id')
                ->get();

            \Log::info('All students with test counts: ' . json_encode($debugQuery));

            // Now get students with 2 or more tests (changed from > 2 to >= 2)
            $studentsWithMultipleTests = EtudiantTestStatus::select('etudiant_id')
                ->where('status', 'TERMINE')
                ->whereHas('test', function ($query) use ($mentalHealthCategories) {
                    $query->whereIn('category_id', $mentalHealthCategories);
                })
                ->whereYear('completed_at', $currentYear)
                ->groupBy('etudiant_id')
                ->havingRaw('COUNT(*) >= 2')
                ->pluck('etudiant_id');

            \Log::info('Students with multiple tests (>=2): ' . json_encode($studentsWithMultipleTests));

            // Debug: Check what baseQuery returns before filtering by studentsWithMultipleTests
            $baseQueryResults = $baseQuery->clone()->select('etudiant_id', 'completed_at', 'score', 'percentage')->get();
            \Log::info('Base query results (all completed tests in date range): ' . json_encode($baseQueryResults));

            // Debug: Check if percentage column exists and what columns are available
            $sampleRecord = $baseQuery->clone()->first();
            if ($sampleRecord) {
                \Log::info('Sample record attributes: ' . json_encode($sampleRecord->getAttributes()));
                \Log::info('Sample record has percentage: ' . (isset($sampleRecord->percentage) ? 'YES' : 'NO'));
            } else {
                \Log::info('No sample record found in base query');
            }


            // Define risk levels based on percentages (not fixed scores)
            // High risk: < 30% of total possible score
            // Medium risk: 30-60% of total possible score
            // Low risk: > 60% of total possible score
            $highRiskPercentage = 30;
            $mediumRiskPercentage = 60;

            \Log::info('Risk thresholds - High: <' . $highRiskPercentage . '%, Medium: ' . $highRiskPercentage . '-' . $mediumRiskPercentage . '%, Low: >' . $mediumRiskPercentage . '%');

            // Get at-risk students with detailed information
            $atRiskStudentsQuery = $baseQuery->clone()
                ->with([
                    'etudiant' => function ($query) {
                        $query->with('niveau');
                    },
                    'test' => function ($query) {
                        $query->with('category');
                    }
                ])
                ->whereIn('etudiant_id', $studentsWithMultipleTests);



            
           
            // Apply risk level filter if specified (using percentage instead of raw score)
            if ($riskLevel) {
                switch ($riskLevel) {
                    case 'high':
                        $atRiskStudentsQuery->where('percentage', '<', $highRiskPercentage);
                        break;
                    case 'medium':
                        $atRiskStudentsQuery->whereBetween('percentage', [$highRiskPercentage, $mediumRiskPercentage]);
                        break;
                    case 'low':
                        $atRiskStudentsQuery->where('percentage', '>', $mediumRiskPercentage);
                        break;
                }
            }

            $atRiskStudents = $atRiskStudentsQuery->get();



            \Log::info('At risk students: ' . json_encode($atRiskStudents));
            // Group students by etudiant_id to get their test history
            $studentTestHistory = $atRiskStudents->groupBy('etudiant_id')->map(function ($tests, $etudiantId) use ($highRiskPercentage, $mediumRiskPercentage) {
                $student = $tests->first()->etudiant;
                $testResults = $tests->map(function ($testStatus) {
                    return [
                        'test_id' => $testStatus->test->id,
                        'test_title' => $testStatus->test->title,
                        'category_name' => $testStatus->test->category->name,
                        'score' => $testStatus->score,
                        'completed_at' => $testStatus->completed_at,
                        'percentage' => $testStatus->percentage,
                        'interpretation' => $testStatus->interpretation,
                    ];
                });

                $averageScore = $tests->avg('score');
                $averagePercentage = $tests->avg('percentage');
                $lowestPercentage = $tests->min('percentage');

                // Determine risk level based on lowest percentage (not raw score)
                $riskLevel = 'low';
                if ($lowestPercentage < $highRiskPercentage) {
                    $riskLevel = 'high';
                } elseif ($lowestPercentage < $mediumRiskPercentage) {
                    $riskLevel = 'medium';
                }

                // Get trigger information from observations
                $triggerInfo = $this->getStudentTriggerInfo($etudiantId, $tests->pluck('test_id'));

                return [
                    'etudiant_id' => $etudiantId,
                    'student_name' => $student->first_name . ' ' . $student->last_name,
                    'niveau' => $student->niveau ? $student->niveau->name : 'N/A',
                    'niveau_id' => $student->niveau_id,
                    'test_count' => $tests->count(),
                    'average_score' => round($averageScore, 2),
                    'average_percentage' => round($averagePercentage, 2),
                    'lowest_percentage' => round($lowestPercentage, 2),
                    'risk_level' => $riskLevel,
                    'tests' => $testResults,
                    'triggers' => $triggerInfo,
                    'last_test_date' => $tests->max('completed_at'),
                ];
            })->values();

            // Calculate summary statistics
            $totalAtRiskStudents = $studentTestHistory->count();
            $totalTestsCompleted = $atRiskStudents->count();
            $averageScore = $atRiskStudents->avg('score');
            $averagePercentage = $atRiskStudents->avg('percentage');

            $highRiskCount = $studentTestHistory->where('risk_level', 'high')->count();
            $mediumRiskCount = $studentTestHistory->where('risk_level', 'medium')->count();
            $lowRiskCount = $studentTestHistory->where('risk_level', 'low')->count();

            // Students needing immediate attention (high risk with recent tests)
            $studentsNeedingAttention = $studentTestHistory->filter(function ($student) {
                return $student['risk_level'] === 'high' &&
                       Carbon::parse($student['last_test_date'])->diffInDays(Carbon::now()) <= 30;
            })->count();

            // Chart data: Students by niveau (grade level)
            $studentsByNiveau = $studentTestHistory->groupBy('niveau')->map(function ($students, $niveau) {
                return [
                    'niveau' => $niveau,
                    'count' => $students->count(),
                    'high_risk' => $students->where('risk_level', 'high')->count(),
                    'medium_risk' => $students->where('risk_level', 'medium')->count(),
                    'low_risk' => $students->where('risk_level', 'low')->count(),
                ];
            })->values();

            // Chart data: Test frequency by category
            $testsByCategory = $atRiskStudents->groupBy('test.category.name')->map(function ($tests, $categoryName) {
                return [
                    'category' => $categoryName,
                    'count' => $tests->count(),
                    'average_score' => round($tests->avg('score'), 2),
                ];
            })->values();

            // Chart data: Trigger type distribution
            $triggerDistribution = $this->getTriggerDistribution($studentTestHistory);

            // Chart data: Score trends over time (last 6 months)
            $scoreTrends = $this->getScoreTrends($mentalHealthCategories, $dateFrom, $dateTo);

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => [
                        'total_at_risk_students' => $totalAtRiskStudents,
                        'total_tests_completed' => $totalTestsCompleted,
                        'average_score' => round($averageScore, 2),
                        'students_needing_attention' => $studentsNeedingAttention,
                        'risk_distribution' => [
                            'high_risk' => $highRiskCount,
                            'medium_risk' => $mediumRiskCount,
                            'low_risk' => $lowRiskCount,
                        ],
                    ],
                    'students' => $studentTestHistory,
                    'charts' => [
                        'students_by_niveau' => $studentsByNiveau,
                        'tests_by_category' => $testsByCategory,
                        'trigger_distribution' => $triggerDistribution,
                        'score_trends' => $scoreTrends,
                    ],
                ],
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error in getMentalHealthRiskDashboard: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'An error occurred while fetching dashboard data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get trigger information for a student's tests
     */
    private function getStudentTriggerInfo($etudiantId, $testIds)
    {
        // Get observations related to the student's tests
        $observations = Observation::whereIn('test_id', $testIds)
            ->where('active', true)
            ->get();

        $triggerCounts = [
            'parent' => 0,
            'teacher' => 0,
            'simulated_grade_shutdown' => 0,
        ];

        $triggerDetails = [];

        foreach ($observations as $observation) {
            $triggerType = $observation->trigger_type ?? 'parent';
            $triggerCounts[$triggerType]++;

            $triggerDetails[] = [
                'type' => $triggerType,
                'label' => $observation->label,
                'description' => $observation->description,
                'test_id' => $observation->test_id,
            ];
        }

        return [
            'counts' => $triggerCounts,
            'details' => $triggerDetails,
        ];
    }

    /**
     * Get trigger type distribution across all students
     */
    private function getTriggerDistribution($studentTestHistory)
    {
        $totalTriggers = [
            'parent' => 0,
            'teacher' => 0,
            'simulated_grade_shutdown' => 0,
        ];

        foreach ($studentTestHistory as $student) {
            if (isset($student['triggers']['counts'])) {
                foreach ($student['triggers']['counts'] as $type => $count) {
                    $totalTriggers[$type] += $count;
                }
            }
        }

        $total = array_sum($totalTriggers);

        return [
            [
                'trigger_type' => 'Parent',
                'count' => $totalTriggers['parent'],
                'percentage' => $total > 0 ? round(($totalTriggers['parent'] / $total) * 100, 2) : 0,
            ],
            [
                'trigger_type' => 'Teacher',
                'count' => $totalTriggers['teacher'],
                'percentage' => $total > 0 ? round(($totalTriggers['teacher'] / $total) * 100, 2) : 0,
            ],
            [
                'trigger_type' => 'Simulated Grade Shutdown',
                'count' => $totalTriggers['simulated_grade_shutdown'],
                'percentage' => $total > 0 ? round(($totalTriggers['simulated_grade_shutdown'] / $total) * 100, 2) : 0,
            ],
        ];
    }

    /**
     * Get score trends over time for mental health tests
     */
    private function getScoreTrends($mentalHealthCategories, $dateFrom, $dateTo)
    {
        $startDate = Carbon::parse($dateFrom);
        $endDate = Carbon::parse($dateTo);

        // Generate monthly intervals
        $months = [];
        $current = $startDate->copy()->startOfMonth();

        while ($current->lte($endDate)) {
            $months[] = $current->copy();
            $current->addMonth();
        }

        $trends = [];

        foreach ($months as $month) {
            $monthStart = $month->copy()->startOfMonth();
            $monthEnd = $month->copy()->endOfMonth();

            $monthlyTests = EtudiantTestStatus::where('status', 'TERMINE')
                ->whereHas('test', function ($query) use ($mentalHealthCategories) {
                    $query->whereIn('category_id', $mentalHealthCategories);
                })
                ->whereBetween('completed_at', [$monthStart, $monthEnd])
                ->get();

            $trends[] = [
                'month' => $month->format('M Y'),
                'month_key' => $month->format('Y-m'),
                'average_score' => $monthlyTests->count() > 0 ? round($monthlyTests->avg('score'), 2) : 0,
                'test_count' => $monthlyTests->count(),
                'high_risk_count' => $monthlyTests->where('score', '<', 30)->count(),
                'medium_risk_count' => $monthlyTests->whereBetween('score', [30, 60])->count(),
                'low_risk_count' => $monthlyTests->where('score', '>', 60)->count(),
            ];
        }

        return $trends;
    }

}
