<form [formGroup]="testCreationFormGroup" (ngSubmit)="saveOrUpdate()">
  <div class="px-7 pt-7">
    <div class="flex place-content-between">
      @if (id) {
        <h2>Details de test ({{ testCategory() | testCategoryName }})</h2>
      } @else {
        <h2>Création de test ({{ testCategory() | testCategoryName }})</h2>
      }
    </div>
    <div class="scrolling_container mt-2.5">
      <div
        class="mi-auto mt-4 flex w-10/12 flex-col gap-4 rounded-lg bg-white px-6 pb-6 pt-7"
      >
        <div class="flex gap-4">
          <div class="flex w-6/12 flex-col gap-2">
            <label class="font-medium" for="inputTitle">Titre du test</label>
            <input
              class="box-border min-h-10 w-full rounded border-2 border-solid border-gray-200 px-3"
              type="text"
              id="inputTitle"
              placeholder="titre du test"
              [required]="true"
              [formControl]="testCreationFormGroup.controls.test.controls.title"
            />
            @if (
              testCreationFormGroup.controls.test.controls.title.hasError(
                'required'
              )
            ) {
              <mat-error class="text-red-500">* Le titre est requis.</mat-error>
            }
          </div>

          @if (
            testCategory() === TestCategoryEnum.TestContenu ||
            testCategory() === TestCategoryEnum.ExamenSimule ||
            testCategory() === TestCategoryEnum.QuizCultureGenerale ||
            testCategory() === TestCategoryEnum.ChallengeHebdomadaire
          ) {
            <div class="flex w-1/3 flex-col gap-2">
              <label class="font-medium" for="inputNiveau">
                Niveau de difficulté
              </label>
              <select
                class="box-border min-h-10 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
                id="inputNiveauDifficulty"
                [formControl]="
                  testCreationFormGroup.controls.test.controls.difficulty_level
                "
                [required]="true"
              >
                @for (difficulty of difficultyLevels; track difficulty) {
                  <option [ngValue]="difficulty">
                    {{ difficulty | formatDifficultyLevel }}
                  </option>
                }
              </select>
              @if (
                testCreationFormGroup.controls.test.controls.difficulty_level.hasError(
                  'required'
                )
              ) {
                <mat-error class="text-red-500">
                  * Le niveau de difficulté est requis.
                </mat-error>
              }
            </div>
          }
        </div>

        <!-- test_content -->
        @if (testCategory() === TestCategoryEnum.TestContenu) {
          <div class="flex gap-4">
            <div class="flex w-1/3 flex-col gap-2">
              <label class="font-medium" for="inputNiveau">Niveau</label>
              <select
                class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
                id="inputNiveau"
                [formControl]="
                  testCreationFormGroup.controls.test.controls.niveau_id
                "
                [required]="true"
                (change)="niveauChange()"
              >
                @for (niveau of allNiveauxByMatiere(); track niveau.id) {
                  <option [ngValue]="niveau.id">
                    {{ niveau.name }}
                  </option>
                }
              </select>
              @if (
                testCreationFormGroup.controls.test.controls.niveau_id.hasError(
                  'required'
                )
              ) {
                <mat-error class="text-red-500">
                  * Le niveau est requise.
                </mat-error>
              }
            </div>

            @if (testCreationFormGroup.getRawValue().test.niveau_id) {
              <div class="flex w-1/3 flex-col gap-2">
                <label class="font-medium" for="inputMatiere">Matière</label>
                <select
                  class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
                  id="inputMatiere"
                  [formControl]="
                    testCreationFormGroup.controls.test.controls.matiere_id
                  "
                  [required]="true"
                  (change)="matiereChange()"
                >
                  @for (matiere of matieres(); track matiere.id) {
                    <option [ngValue]="matiere.id">
                      {{ matiere.name_fr }}
                    </option>
                  }
                </select>
                @if (
                  testCreationFormGroup.controls.test.controls.matiere_id.hasError(
                    'required'
                  )
                ) {
                  <mat-error class="text-red-500">
                    * La matière est requise.
                  </mat-error>
                }
              </div>
            }
            @if (testCreationFormGroup.getRawValue().test.matiere_id) {
              <div class="flex w-1/3 flex-col gap-2">
                <label class="font-medium" for="inputLecon">Leçon</label>
                <select
                  class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
                  id="inputLecon"
                  [formControl]="
                    testCreationFormGroup.controls.test.controls.content_id
                  "
                  [required]="true"
                >
                  @for (contenu of contenues(); track contenu.id) {
                    <option [ngValue]="contenu.id">{{ contenu.title }}</option>
                  }
                </select>
                @if (
                  testCreationFormGroup.controls.test.controls.content_id.hasError(
                    'required'
                  )
                ) {
                  <mat-error class="text-red-500">
                    * La leçon est requise.
                  </mat-error>
                }
              </div>
            }
          </div>
        }

        <!-- exam_simulation ou challenge hebodmadaire -->
        @if (
          testCategory() === TestCategoryEnum.ExamenSimule ||
          testCategory() === TestCategoryEnum.ChallengeHebdomadaire
        ) {
          <div class="flex gap-4">
            <div class="flex w-1/3 flex-col gap-2">
              <label class="font-medium" for="inputNiveauExam">Niveau</label>
              <select
                class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
                id="inputNiveauExam"
                [formControl]="
                  testCreationFormGroup.controls.test.controls.niveau_id
                "
                [required]="true"
                (change)="niveauChange()"
              >
                @for (niveau of allNiveauxByMatiere(); track niveau.id) {
                  <option [ngValue]="niveau.id">
                    {{ niveau.name }}
                  </option>
                }
              </select>
              @if (
                testCreationFormGroup.controls.test.controls.niveau_id.hasError(
                  'required'
                )
              ) {
                <mat-error class="text-red-500">
                  * Le niveau est requise.
                </mat-error>
              }
            </div>
            @if (testCreationFormGroup.getRawValue().test.niveau_id) {
              <div class="flex w-1/3 flex-col gap-2">
                <label class="font-medium" for="inputMatiereExam">
                  Matière
                </label>
                <select
                  class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
                  id="inputMatiereExam"
                  [formControl]="
                    testCreationFormGroup.controls.test.controls.matiere_id
                  "
                  [required]="true"
                >
                  @for (matiere of matieres(); track matiere.id) {
                    <option [ngValue]="matiere.id">
                      {{ matiere.name_fr }}
                    </option>
                  }
                </select>
                @if (
                  testCreationFormGroup.controls.test.controls.matiere_id.hasError(
                    'required'
                  )
                ) {
                  <mat-error class="text-red-500">
                    * La matière est requise.
                  </mat-error>
                }
              </div>
            }
          </div>
        }

        <!-- culture_general -->
        @if (testCategory() === TestCategoryEnum.QuizCultureGenerale) {
          <div class="flex gap-4">
            <div class="flex w-1/3 flex-col gap-2">
              <label class="font-medium" for="inputNiveauCulture">Niveau</label>
              <select
                class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
                id="inputNiveauCulture"
                [formControl]="
                  testCreationFormGroup.controls.test.controls.niveau_id
                "
                [required]="true"
              >
                @for (niveau of allNiveauxByMatiere(); track niveau.id) {
                  <option [ngValue]="niveau.id">
                    {{ niveau.name }}
                  </option>
                }
              </select>
              @if (
                testCreationFormGroup.controls.test.controls.niveau_id.hasError(
                  'required'
                )
              ) {
                <mat-error class="text-red-500">
                  * Le niveau est requise.
                </mat-error>
              }
            </div>
          </div>
        }

        <div class="flex w-full flex-row">
          <div class="flex w-full flex-col gap-2">
            <label class="font-medium" for="inputDescription">
              Description
            </label>
            <textarea
              class="box-border min-h-32 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
              id="inputDescription"
              placeholder="Entrer une description..."
              [formControl]="
                testCreationFormGroup.controls.test.controls.description
              "
            ></textarea>
            @if (
              testCreationFormGroup.controls.test.controls.description.hasError(
                'required'
              )
            ) {
              <mat-error class="text-red-500">
                * La description est requise.
              </mat-error>
            }
          </div>
        </div>

        @if (
          testCategory() === TestCategoryEnum.ExamenSimule ||
          testCategory() === TestCategoryEnum.ChallengeHebdomadaire ||
          testCategory() === TestCategoryEnum.Cognitifs ||
          testCategory() === TestCategoryEnum.NeuroDeveloppement ||
          testCategory() === TestCategoryEnum.Apprentissage ||
          testCategory() === TestCategoryEnum.Personnalite ||
          testCategory() === TestCategoryEnum.EmotionnelEtComportemental ||
          testCategory() === TestCategoryEnum.MotivationEtEstimeDeSoi ||
          testCategory() === TestCategoryEnum.AttentionExecutif ||
          testCategory() === TestCategoryEnum.RelationsFamilialesSociales
        ) {
          <div class="flex flex-row gap-4">
            <div class="flex w-1/3 flex-col gap-2">
              <label class="font-medium" for="inputDescription">
                Durée (en minutes)
              </label>
              <input
                class="box-border min-h-10 w-full rounded border-2 border-solid border-gray-200 px-3"
                id="duration"
                placeholder="Entrer la durée en minutes..."
                type="number"
                min="1"
                step="1"
                [formControl]="
                  testCreationFormGroup.controls.test.controls.timer
                "
                [disabled]="readOnlyMode"
                [required]="true"
              />
              @if (
                testCreationFormGroup.controls.test.controls.timer.hasError(
                  'required'
                )
              ) {
                <mat-error class="text-red-500">
                  * La durée est requise.
                </mat-error>
              } @else if (
                testCreationFormGroup.controls.test.controls.timer.hasError(
                  'min'
                )
              ) {
                <mat-error class="text-red-500">
                  * La valeur minimum est 1
                </mat-error>
              }
            </div>
          </div>
        }
        @if (testCategory() === TestCategoryEnum.ChallengeHebdomadaire) {
          <div class="flex flex-row gap-4">
            <div class="flex w-1/3 flex-col gap-2">
              <label class="font-medium" for="dateChallenge">
                Date du challenge
              </label>
              <input
                class="box-border min-h-10 w-full rounded border-2 border-solid border-gray-200 px-3"
                id="dateChallenge"
                placeholder="Date du challenge..."
                type="date"
                [min]="today.toDateString() | date: 'yyyy-MM-dd'"
                [formControl]="
                  testCreationFormGroup.controls.test.controls.challenge_date
                "
                [required]="true"
                [disabled]="readOnlyMode"
              />
              @if (
                testCreationFormGroup.controls.test.controls.challenge_date.hasError(
                  'required'
                )
              ) {
                <mat-error class="text-red-500">
                  * La date du challenge est requise.
                </mat-error>
              }
            </div>
          </div>
        }
      </div>

      <!-- Liste des Steps (Questions) -->
      <div
        class="scrolling_container mt-2.5"
        formArrayName="steps"
        [cdkDropListDisabled]="readOnlyMode"
        cdkDropList
        (cdkDropListDropped)="drop($event)"
      >
        @for (
          step of testCreationFormGroup.controls.steps.controls;
          track step;
          let sIndex = $index
        ) {
          <div
            class="mi-auto step-container flex w-10/12 place-content-between gap-4"
            [formGroupName]="sIndex"
            [ngClass]="{ isImported: step.get('isImported')?.value }"
            cdkDrag
          >
            <div
              class="drag mt-4 flex w-full flex-col gap-4 rounded-lg bg-white px-7 pb-4 pt-12 pt-14"
              [class.pointer-events-none]="!(step.getRawValue().id === null)"
              [class.opacity-50]="!(step.getRawValue().id === null)"
            >
              @if (!readOnlyMode) {
                <img
                  class="img_drag"
                  src="assets/icones/drag_indicator.png"
                  alt=""
                />
              }
              <div class="flex w-1/4 flex-col gap-1">
                <label class="font-medium" for="questionForm">
                  Type de réponse
                </label>
                <select
                  class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
                  id="questionForm"
                  formControlName="type"
                  [required]="true"
                  (change)="questionTypeChange(sIndex)"
                >
                  <option [ngValue]="TypeQuestionEnum.one">
                    Un seul choix possible
                  </option>
                  <option [ngValue]="TypeQuestionEnum.many">
                    Plusieurs choix possibles
                  </option>
                  <option [ngValue]="TypeQuestionEnum.TrueOrFalse">
                    Vrai/Faux
                  </option>
                </select>

                @if (
                  testCreationFormGroup.controls.steps.controls
                    .at(sIndex)
                    .controls.type.hasError('required')
                ) {
                  <mat-error class="text-red-500">
                    * Le type de la question requis.
                  </mat-error>
                }
              </div>
              <div formGroupName="question">
                <!-- Détails du Step -->
                <div class="flex gap-4">
                  <div class="flex w-3/4 flex-col gap-2">
                    <input
                      class="border_b box-border h-16 w-full px-3"
                      type="text"
                      placeholder="Question"
                      formControlName="content"
                      [required]="true"
                    />
                    @if (
                      testCreationFormGroup.controls.steps.controls
                        .at(sIndex)
                        .controls.question.controls.content.hasError('required')
                    ) {
                      <mat-error class="text-red-500">
                        * Le contenu de la question requis.
                      </mat-error>
                    }
                  </div>
                  @if (false) {
                    <!-- le contenu du question est toujours text, l'image/video/audio sont des pieces jointes -->
                    <div class="flex w-1/4 flex-col gap-1">
                      <label class="font-medium" for="questionFormDetailStep">
                        Question form
                      </label>
                      <select
                        class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
                        id="questionFormDetailStep"
                        formControlName="type"
                        [required]="true"
                      >
                        <option value="text">Texte</option>
                        <!--                      <option value="image">Image</option>-->
                        <!--                      <option value="video">Vidéo</option>-->
                        <!--                      <option value="audio">Audio</option>-->
                      </select>

                      @if (
                        testCreationFormGroup.controls.steps.controls
                          .at(sIndex)
                          .controls.question.controls.type.hasError('required')
                      ) {
                        <mat-error class="text-red-500">
                          * Le champ supporté est requis.
                        </mat-error>
                      }
                    </div>
                  }
                </div>
                <!-- Description Toggle -->
                <mat-slide-toggle
                  class="example-margin"
                  color="primary mt-6 mb-3"
                  [checked]="!!getQuestion(sIndex).controls.description.value"
                  (change)="toggleDescription(sIndex, $event.checked)"
                  #toggle="matSlideToggle"
                  [disabled]="readOnlyMode"
                >
                  <span style="padding-left: 10px">Description</span>
                </mat-slide-toggle>
                <!-- Description Textarea -->
                <div class="flex w-full flex-row">
                  <div class="flex w-full flex-col gap-2">
                    @if (toggle.checked) {
                      <textarea
                        class="box-border min-h-32 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
                        placeholder="Entrer une description..."
                        formControlName="description"
                      ></textarea>
                    }
                  </div>
                </div>
                <div>
                  @if (
                    testCreationFormGroup.controls.steps.at(sIndex).value
                      .question.image_path
                  ) {
                    <img
                      [src]="
                        displayImage(
                          testCreationFormGroup.controls.steps.at(sIndex).value
                            .question.image_path
                        )
                      "
                      height="200"
                      class="max-w-full"
                      alt="image importée"
                    />
                  }
                </div>
                <!-- Options liées à la Question -->
                @if (
                  testCreationFormGroup.controls.steps.at(sIndex).value.type ===
                    TypeQuestionEnum.one ||
                  testCreationFormGroup.controls.steps.at(sIndex).value.type ===
                    TypeQuestionEnum.many
                ) {
                  <div formArrayName="options">
                    @for (
                      option of getOptions(sIndex).controls;
                      track option;
                      let oIndex = $index
                    ) {
                      <div
                        [formGroupName]="oIndex"
                        class="input_answer flex flex-row items-center gap-2"
                        (click)="setEditingOption(sIndex, oIndex)"
                        style="margin-top: 15px"
                      >
                        <div class="flex w-full flex-row items-center gap-2">
                          <div class="upload_icon">
                            @if (option.get('icon')?.value) {
                              <div class="emoji_display">
                                {{ option.get('icon')?.value | unicodeToEmoji }}
                              </div>
                            }
                            @if (!option.get('icon')?.value) {
                              <img src="assets/icones/updoad_icon.svg" alt="" />
                            }
                            <button
                              #emojiPickerBtn
                              type="button"
                              [attr.data-step-index]="sIndex"
                              [attr.data-option-index]="oIndex"
                              class="emoji-button"
                              [disabled]="readOnlyMode"
                            ></button>
                          </div>
                          <!-- Nom de l'option -->
                          <input
                            class="border_b box-border min-h-10 flex-1 px-3"
                            type="text"
                            [placeholder]="'Option ' + (oIndex + 1)"
                            formControlName="name"
                            [required]="true"
                          />
                          <!-- Toggle pour marquer la réponse correcte -->

                          @if (
                            isToggleVisible(sIndex, oIndex) &&
                            testCategory() !== TestCategoryEnum.TestProfiling
                          ) {
                            @if (readOnlyMode) {
                              <div
                                class="flex rounded-md border border-green-700 bg-green-50 px-2 py-0.5 align-middle text-green-700"
                              >
                                <mat-icon class="mr-2">check</mat-icon>
                                Réponse correcte
                              </div>
                            } @else {
                              <mat-slide-toggle
                                style="text-wrap: nowrap"
                                class="example-margin"
                                color="primary"
                                formControlName="isCorrect"
                                [disabled]="
                                  testCategory() ===
                                  TestCategoryEnum.TestProfiling
                                "
                                (change)="toggleCorrectAnswer(sIndex, oIndex)"
                                aria-label="Indicate as correct answer"
                              >
                                <span style="padding-left: 10px">
                                  Indiquer comme réponse correcte
                                </span>
                              </mat-slide-toggle>
                            }
                          }
                        </div>
                        <!-- Bouton Ajouter/Supprimer Option -->
                        @if (!step.get('isImported')?.value) {
                          <img
                            class="icon_answer"
                            [class.disabled_element]="readOnlyMode"
                            [src]="
                              oIndex !== getOptions(sIndex).length - 1
                                ? 'assets/icones/delete.svg'
                                : 'assets/icones/add.svg'
                            "
                            alt=""
                            (click)="
                              !readOnlyMode
                                ? oIndex !== getOptions(sIndex).length - 1
                                  ? removeOption(sIndex, oIndex)
                                  : addOption(sIndex)
                                : null
                            "
                          />
                        }
                      </div>
                      @if (
                        testCreationFormGroup.controls.steps.controls
                          .at(sIndex)
                          .controls.question.controls.options.at(oIndex)
                          .controls.name.hasError('required')
                      ) {
                        <mat-error class="text-red-500">
                          * Le titre de l'option est réquise.
                        </mat-error>
                      }
                    }

                    @if (
                      testCreationFormGroup.controls.steps.controls.at(sIndex)
                        .controls.question.controls.options.controls.length ===
                      0
                    ) {
                      <mat-error class="text-red-500">
                        * Il faut ajouter des options pour la question.
                      </mat-error>
                    }

                    @if (
                      testCreationFormGroup.controls.steps.controls.at(sIndex)
                        .controls.question.controls.options.controls.length ===
                      0
                    ) {
                      <mat-error class="text-red-500">
                        * Il faut ajouter des options pour la question.
                      </mat-error>
                    }
                  </div>
                } @else if (
                  testCreationFormGroup.controls.steps.at(sIndex).value.type ===
                  TypeQuestionEnum.TrueOrFalse
                ) {
                  <div>
                    <mat-radio-group formControlName="answer">
                      <mat-radio-button
                        class="green-radio-button"
                        [value]="true"
                      >
                        Vrai
                      </mat-radio-button>
                      <mat-radio-button
                        class="red-radio-button"
                        [value]="false"
                      >
                        Faux
                      </mat-radio-button>
                    </mat-radio-group>
                  </div>
                }

                <!-- Question obligatoire Toggle -->
                <mat-slide-toggle
                  class="example-margin"
                  color="primary mt-6 mb-3"
                  [checked]="!!getQuestion(sIndex).controls.required.value"
                  [disabled]="readOnlyMode"
                >
                  <span style="padding-left: 10px">Obligatoire</span>
                </mat-slide-toggle>
              </div>
            </div>
            <!-- Boutons d'action pour chaque step -->

            @if (!readOnlyMode) {
              <div
                class="action-buttons mb-4 mt-4 flex h-fit flex-col items-center gap-2.5 space-y-2 rounded-lg py-2"
                style="
                  background-color: #fff;
                  border: 1px solid #d5d7da;
                  width: 50px;
                "
              >
                <!-- TODO: images not yet supported-->
                @if (
                  testCategory() !== TestCategoryEnum.TestProfiling &&
                  !step.get('isImported')?.value
                ) {
                  <button
                    type="button"
                    (click)="imageInput.click()"
                    style="width: 40%; height: 30px"
                    class="flex items-center justify-center"
                  >
                    <img
                      class="min-h-9"
                      src="assets/icones/imagesmode.svg"
                      alt=""
                    />
                  </button>

                  <input
                    type="file"
                    (change)="onImagePicked($event, sIndex)"
                    #imageInput
                    accept="image/*"
                    [multiple]="false"
                    hidden
                  />
                }

                @if (!step.get('isImported')?.value) {
                  <button
                    type="button"
                    (click)="openSelectQuestionDialog($event, sIndex)"
                    style="width: 40%; height: 30px"
                    class="flex items-center justify-center"
                  >
                    <img
                      class="min-h-9"
                      src="assets/icones/upload.svg"
                      alt=""
                    />
                  </button>
                }

                <button
                  type="button"
                  (click)="addStep(sIndex); $event.stopPropagation()"
                  style="width: 40%; height: 30px"
                  class="flex items-center justify-center"
                >
                  <img
                    class="min-h-9"
                    src="assets/icones/add_circle.svg"
                    alt=""
                  />
                </button>
                @if (testCreationFormGroup.controls.steps.controls.length > 1) {
                  <button
                    type="button"
                    (click)="removeStep(sIndex)"
                    style="width: 35%; height: 20px"
                    class="flex items-center justify-center"
                  >
                    <img
                      class="min-h-9"
                      src="assets/icones/remove.svg"
                      alt=""
                    />
                  </button>
                }
              </div>
            }
          </div>
        }
      </div>

      <!-- Boutons de Soumission -->
      <div class="mt-4 flex justify-end">
        <button
          type="button"
          class="bg_btn_censel btn mr-4 flex items-center gap-1 rounded-lg px-4 py-2"
          (click)="cancel()"
        >
          <span class="color-white font-medium">
            {{ readOnlyMode ? 'Retour' : 'Annuler' }}
          </span>
        </button>


        @if (!readOnlyMode) {
          <button
            type="submit"
            class="bg_btn btn flex items-center gap-1 rounded-lg px-4 py-2"
            [disabled]="testCreationFormGroup.invalid"
          >
            <span class="color-white font-medium">Enregistrer</span>
          </button>
        }
      </div>
    </div>
  </div>
</form>
