# Seeders - Quick Start Guide

## 🚀 Quick Commands

### Run All Seeders
```bash
cd /Users/<USER>/Desktop/MindBridge/mindbridge-backend
php artisan db:seed --database=mind_bridge
```

### Run Only Mental Health Tests Seeder
```bash
php artisan db:seed --seeder=MentalHealthTestsSeeder --database=mind_bridge
```

### Fresh Database with All Seeds
```bash
php artisan migrate:fresh --database=mind_bridge --seed
```

### Run Specific Seeder Type
```bash
# For tenant database (MindBridge)
php artisan db:seed --database=mind_bridge --seeder-type=tenant

# For central database
php artisan db:seed --database=central

# For data synchronization
php artisan db:seed --database=mind_bridge --seeder-type=sync
```

---

## 📊 What Gets Created

### MentalHealthTestsSeeder (Main One)
- **12 Mental Health Tests**
- **36+ Questions** (3-4 per test)
- **100+ Answer Options**
- **4 Test Categories:**
  - Cognitive Tests (3 tests)
  - Emotional Tests (3 tests)
  - Behavioral Tests (3 tests)
  - Social Tests (3 tests)

### CategoriesSeeder
- **2 Root Categories**
- **12 Subcategories**
- **Category hierarchy setup**

### BadgetsSeeder
- **10 Achievement Badges**
- **Points system** (100 to 10,000 points)

### Other Seeders
- **RecommandationQuestionsSeeder:** 100+ questions
- **StepsSeeder:** Test profiling questions
- **ContentSeeder:** Learning materials
- **MindBridgeDataSyncSeeder:** Data synchronization

---

## 🔍 Verify Seeded Data

### Using Laravel Tinker
```bash
php artisan tinker
```

Then run:
```php
// Check tests
Test::count()  // Should be 12+

// Check questions
Question::count()  // Should be 36+

// Check categories
Category::count()  // Should be 14+

// Check badges
Badges::count()  // Should be 10

// View specific test
Test::first()

// View test with questions
Test::with('steps.question.options')->first()

// Check mental health category
Category::find(2)->with('subcategories')->first()
```

### Using Database Queries
```sql
-- Count tests
SELECT COUNT(*) FROM tests;

-- Count questions
SELECT COUNT(*) FROM questions;

-- Count options
SELECT COUNT(*) FROM options;

-- View mental health tests
SELECT * FROM tests WHERE category_id IN (19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29);

-- View categories
SELECT * FROM categories WHERE parent_id = 2;

-- View badges
SELECT * FROM badges;
```

---

## 📁 Seeder Files Location

```
mindbridge-backend/database/seeders/
├── DatabaseSeeder.php (Main orchestrator)
├── MindBridgeDataSyncSeeder.php
└── mindBridge/
    ├── MentalHealthTestsSeeder.php ⭐
    ├── CategoriesSeeder.php
    ├── StepsSeeder.php
    ├── ContentSeeder.php
    ├── BadgetsSeeder.php
    ├── RecommandationQuestionsSeeder.php
    └── MindBridgeUsersSeeder.php
```

---

## 🔄 Seeder Execution Order

1. **MindBridgeUsersSeeder** - Create users
2. **CategoriesSeeder** - Create categories
3. **StepsSeeder** - Create test profiling
4. **ContentSeeder** - Create content
5. **MentalHealthTestsSeeder** ⭐ - Create mental health tests
6. **RecommandationQuestionsSeeder** - Create recommendation questions
7. **BadgetsSeeder** - Create badges

---

## 🧪 Test Seeded Data

### Check API Endpoints
```bash
# Get categories
curl http://localhost:8000/api/mind_bridge/categories/board

# Get mental health tests
curl http://localhost:8000/api/mind_bridge/categories/2/tests

# Get test by ID
curl http://localhost:8000/api/mind_bridge/tests/1
```

### Check Admin Panel
1. Open admin panel
2. Navigate to "Sante Mentale"
3. Should see mental health test categories
4. Should see tests in each category

### Check Mobile App
1. Open mobile app
2. Go to home screen
3. Should see mental health tests in "mentalHealthToDo"
4. Should be able to start a test

---

## 🛠️ Troubleshooting

### Seeder Not Found
```bash
composer dump-autoload
php artisan cache:clear
```

### Foreign Key Constraint Error
- Ensure categories exist first
- Check category IDs in seeder
- Run seeders in correct order

### Duplicate Data
```bash
# Truncate tables
php artisan tinker
>>> DB::table('tests')->truncate();
>>> DB::table('questions')->truncate();
>>> DB::table('options')->truncate();
>>> DB::table('steps')->truncate();
```

### Check Seeder Status
```bash
# List all seeders
php artisan list

# Check database connection
php artisan tinker
>>> DB::connection('mind_bridge')->getPdo()
```

---

## 📝 Mental Health Tests Created

### Cognitive Tests (Category 19-20)
1. **Test de Mémoire et Attention** - 4 questions
2. **Test de Résolution de Problèmes** - 4 questions
3. **Test de Créativité et Innovation** - 4 questions

### Emotional Tests (Category 21-23)
1. **Test de Gestion du Stress** - 4 questions
2. **Test de Gestion des Émotions** - 4 questions
3. **Test d'Estime de Soi** - 4 questions

### Behavioral Tests (Category 24-26)
1. **Test de Motivation et Engagement** - 4 questions
2. **Test de Discipline et Persévérance** - 4 questions
3. **Test de Gestion du Temps** - 4 questions

### Social Tests (Category 27-29)
1. **Test de Communication** - 4 questions
2. **Test d'Empathie et Compassion** - 4 questions
3. **Test d'Interactions Sociales** - 4 questions

---

## 🎯 Question Types

- **one:** Single choice (radio button)
- **many:** Multiple choice (checkboxes)
- **true_false:** True/False question

---

## 💡 Tips

1. **Always backup** before running seeders
2. **Use fresh database** for clean state
3. **Verify data** after seeding
4. **Test API endpoints** with seeded data
5. **Check admin panel** for seeded tests
6. **Test mobile app** with seeded tests

---

## 📚 Related Documentation

- **Full Seeder Guide:** `MENTAL_HEALTH_TESTS_SEEDERS_GUIDE.md`
- **Complete Documentation:** `MENTAL_HEALTH_TESTS_DOCUMENTATION.md`
- **Quick Reference:** `MENTAL_HEALTH_TESTS_QUICK_REFERENCE.md`

---

## ✅ Checklist

- [ ] Navigate to backend directory
- [ ] Run migrations: `php artisan migrate --database=mind_bridge`
- [ ] Run seeders: `php artisan db:seed --database=mind_bridge`
- [ ] Verify data in database
- [ ] Test API endpoints
- [ ] Check admin panel
- [ ] Test mobile app
- [ ] All working? ✅

---

**Ready to seed your database!** 🌱

