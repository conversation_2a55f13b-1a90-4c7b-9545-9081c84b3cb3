import { patchState, signalState } from '@ngrx/signals';
import { inject, Injectable } from '@angular/core';
import { pipe } from 'rxjs';
import { finalize, switchMap, tap } from 'rxjs/operators';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { SetLoading } from '../shared/store/shared.actions';
import { Store } from '@ngrx/store';
import { AppState } from '../../core/app.state';
import { ApiResponse } from '../../core/models/api-response';
import {
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from '../shared/global.config';
import { Pagination } from '../shared/helpers/query.helper';
import { QuestionRepositoryService } from './services/questions-repository.services';
import { QuestionResponse } from './models/questions-response';
import {
  IQuestionSearchForm,
  QuestionSearchForm,
} from './models/question-search-request';
import { CommonService } from '../shared/common.service';

export type QuestionsState = {
  questions: QuestionResponse[];
  questionsSearchForm: IQuestionSearchForm;
  current_page: number;
  total_pages: number;
  per_page: number;
  total_items: number;
};

export const initialQuestionsState: QuestionsState = {
  questions: [],
  questionsSearchForm: new QuestionSearchForm(),
  current_page: DEFAULT_CURRENT_PAGE,
  total_pages: 1,
  per_page: DEFAULT_PAGE_SIZE,
  total_items: 0,
};

@Injectable()
export class QuestionsStore {
  store = inject<Store<AppState>>(Store);
  #commonService = inject(CommonService);

  readonly state = signalState<QuestionsState>(initialQuestionsState);
  #questionRepositoryService = inject(QuestionRepositoryService);

  setSearchForm = (questionsSearchForm: IQuestionSearchForm) =>
    patchState(this.state, { questionsSearchForm });

  setCurrentPage = (current_page: number) =>
    patchState(this.state, { current_page });

  searchQuestions = rxMethod<void>(
    pipe(
      switchMap(() => {
        const pagination: Pagination = {
          current_page: this.state.current_page(),
          per_page: this.state.per_page(),
        };
        return this.#questionRepositoryService
          .searchQuestions(this.state.questionsSearchForm(), pagination)
          .pipe(
            tap((response: ApiResponse<QuestionResponse>) => {
              patchState(this.state, {
                questions: response.data,

                current_page: response.current_page,
                per_page: response.per_page,
                total_pages: response.total_pages,
                total_items: response.total_items,
              });
            }),
            finalize(() => {
              this.store.dispatch(SetLoading({ isAppLoading: false }));
            })
          );
      }),
      finalize(() => {})
    )
  );

  uploadImage = rxMethod<FormData>(
    pipe(
      switchMap((formData: FormData) => {
        return this.#questionRepositoryService.uploadImage(formData).pipe(
          tap((response: any) => {
            if (response.success) {
              this.#commonService.showToast('Image uploaded successfully', 'SUCCESS');
            } else {
              this.#commonService.showToast(response.message || 'Upload failed', 'ERROR');
            }
          }),
          finalize(() => {
            this.store.dispatch(SetLoading({ isAppLoading: false }));
          })
        );
      })
    )
  );
}
