<?php

namespace App\Models\MindBridge;

use App\Models\User;
use App\Models\School;
use App\Models\NiveauCentrale;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MindBridgeEtudiant extends Model
{
    use SoftDeletes;

    protected $connection = 'mind_bridge';
    protected $table = 'etudiants';
    protected $fillable = ['first_name','last_name','email','identifiant','niveau_id','avatar','nni', 'test_profiling_completed', 'user_id','school_id', 'last_connection_at', 'connection_streak'];

    public function users()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function niveau()
    {
        return $this->belongsTo(NiveauCentrale::class, 'niveau_id');
    }
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id');
    }

    public function notes()
    { 
        return $this->hasMany(MindeBridgeStudentNotes::class, 'etudiant_id');
    }

    public function etudiantMatiereLevel()
    {
        return $this->hasMany(EtudiantMatiereLevel::class, 'etudiant_id');
    }

    public function badges()
    {
        return $this->hasMany(EtudaintBadges::class, 'eb_etudiant_id');
    }
    public function points()
    {
        return $this->hasOne(EtudiantPoints::class, 'ep_etudiant_id');
    }
}
