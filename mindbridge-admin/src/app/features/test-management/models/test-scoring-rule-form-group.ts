import { FormControl, FormGroup, Validators } from '@angular/forms';
import { TestScoringRuleRequest } from './test-scoring-rule';

export class TestScoringRuleFormGroup {
  min_percentage: FormControl<number>;
  max_percentage: FormControl<number>;
  interpretation: FormControl<string>;
  feedback: FormControl<string>;
  recommendation: FormControl<string>;

  constructor(rule = new TestScoringRuleRequest()) {
    this.min_percentage = new FormControl(rule.min_percentage, [
      Validators.required,
      Validators.min(0),
      Validators.max(100),
    ]);
    this.max_percentage = new FormControl(rule.max_percentage, [
      Validators.required,
      Validators.min(0),
      Validators.max(100),
    ]);
    this.interpretation = new FormControl(rule.interpretation, [
      Validators.required,
      Validators.maxLength(255),
    ]);
    this.feedback = new FormControl(rule.feedback || '');
    this.recommendation = new FormControl(rule.recommendation || '');
  }
}

