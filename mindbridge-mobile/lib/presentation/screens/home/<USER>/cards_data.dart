// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// import '../../../../app/routes.dart';
// import '../../../../core/constants/app_strings.dart';
// import '../../../../core/utilities/app_images.dart';
// import '../model/card_data.dart';

// final List<CardData> cardsData = [
//   CardData(
//     title: AppStrings.weeklyChallenge,
//     imagePath: AppImages.challenge,
//     gradientColors: [
//       const Color(0xFF365672).withOpacity(0.9),
//       const Color(0xFFADBCCA).withOpacity(0.9)
//     ],
//     borderGradientColors: [
//       const Color(0xFFD3E4EB).withOpacity(0.1),
//       const Color(0xFF406271).withOpacity(0.1)
//     ],
//     buttonText: AppStrings.acceptChallenge,
//     onPressed: () {},
//   ),
//   CardData(
//     title: AppStrings.takeSurvey,
//     imagePath: AppImages.sondages,
//     gradientColors: [
//       const Color(0xFFBE1A87).withOpacity(0.9),
//       const Color(0xFFECBADB).withOpacity(0.9)
//     ],
//     borderGradientColors: [
//       const Color(0xFFF2D1E7).withOpacity(0.1),
//       const Color(0xFFF2EEF4).withOpacity(0.1)
//     ],
//     buttonText: AppStrings.participateSurvey,
//     onPressed: () {},
//   ),
//   CardData(
//     title: AppStrings.generalQuiz,
//     titleColor: Colors.black,
//     imagePath: AppImages.quizCulture,
//     gradientColors: [
//       const Color(0xFFCDF2F9).withOpacity(0.9),
//       const Color(0xFFECFAFD).withOpacity(0.9)
//     ],
//     borderGradientColors: [
//       const Color(0xFFF3FCFD).withOpacity(0.1),
//       const Color(0xFFECFAFD).withOpacity(0.1)
//     ],
//     buttonText: AppStrings.takeQuiz,
//     onPressed: () {},
//   ),
//   CardData(
//       title: AppStrings.recommendations,
//       imagePath: AppImages.recommandation,
//       gradientColors: [
//         const Color(0xFF9879A5).withOpacity(0.9),
//         const Color(0xFF89B6CA).withOpacity(0.9)
//       ],
//       borderGradientColors: [
//         const Color(0xFFD3E4EB).withOpacity(0.1),
//         const Color(0xFF406271).withOpacity(0.1)
//       ],
//       buttonText: AppStrings.chooseLesson,
//       onPressed: () {}),
//   CardData(
//     title: AppStrings.selectLesson,
//     imagePath: AppImages.selectLesson,
//     gradientColors: [
//       const Color(0xFF654672).withOpacity(0.9),
//       const Color(0xFFCBBCD2).withOpacity(0.9)
//     ],
//     borderGradientColors: [
//       const Color(0xFFCBBCD2).withOpacity(0.1),
//       const Color(0xFF7E588E).withOpacity(0.1)
//     ],
//     buttonText: AppStrings.chooseLesson,
//     onPressed: () {
//       Get.toNamed(Routes.selectSubject);
//     },
//   ),
//   CardData(
//     title: AppStrings.examSimulation,
//     imagePath: AppImages.examSimulation,
//     gradientColors: [
//       const Color(0xFF568397).withOpacity(0.9),
//       const Color(0xFFC4DBE5).withOpacity(0.9)
//     ],
//     borderGradientColors: [
//       const Color(0xFFD3E4EB).withOpacity(0.1),
//       const Color(0xFF568397).withOpacity(0.1)
//     ],
//     buttonText: AppStrings.takeExam,
//     onPressed: () {},
//   ),
// ];
