<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Database\Seeders\MindBridgeDataSyncSeeder;

class ResyncMindBridgeData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mindbridge:resync-data 
                            {--fresh : Remove existing synced data before re-syncing}
                            {--users-only : Only sync users}
                            {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Re-synchronize MindBridge data with central database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 MindBridge Data Re-synchronization Tool');
        $this->info('=====================================');

        $fresh = $this->option('fresh');
        $usersOnly = $this->option('users-only');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            if ($fresh && !$dryRun) {
                $this->cleanupExistingData();
            }

            if ($usersOnly) {
                $this->syncUsersOnly($dryRun);
            } else {
                $this->runFullSync($dryRun);
            }

            if (!$dryRun) {
                $this->info('✅ Data re-synchronization completed successfully!');
            } else {
                $this->info('✅ Dry run completed - review the changes above');
            }

        } catch (\Exception $e) {
            $this->error('❌ Error during re-synchronization: ' . $e->getMessage());
            Log::error('MindBridge resync error: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Clean up existing synced data
     */
    private function cleanupExistingData()
    {
        $this->info('🧹 Cleaning up existing synced data...');

        // Remove MindBridge users (keep other users)
        $mindBridgeUsers = DB::connection('centrale')
            ->table('users')
            ->where('is_mind_bridge_user', true)
            ->pluck('id');

        if ($mindBridgeUsers->isNotEmpty()) {
            // Remove user-school relationships
            DB::connection('centrale')
                ->table('user_school')
                ->whereIn('user_id', $mindBridgeUsers)
                ->delete();

            // Remove users
            DB::connection('centrale')
                ->table('users')
                ->whereIn('id', $mindBridgeUsers)
                ->delete();

            $this->info("🗑️  Removed {$mindBridgeUsers->count()} MindBridge users");
        }

        // Remove MindBridge school
        $school = DB::connection('centrale')
            ->table('schools')
            ->where('database', 'mind_bridge')
            ->first();

        if ($school) {
            DB::connection('centrale')
                ->table('schools')
                ->where('id', $school->id)
                ->delete();

            $this->info('🗑️  Removed MindBridge school');
        }

        $this->info('✅ Cleanup completed');
    }

    /**
     * Sync users only
     */
    private function syncUsersOnly($dryRun = false)
    {
        $this->info('👥 Syncing users only...');

        // Get etudiant user IDs
        $etudiantUsers = DB::connection('mind_bridge')
            ->table('etudiants')
            ->whereNotNull('user_id')
            ->distinct()
            ->pluck('user_id');

        $this->info("Found {$etudiantUsers->count()} etudiant user IDs: " . $etudiantUsers->implode(', '));

        if ($dryRun) {
            $this->table(['Action', 'Details'], [
                ['Sync Users', "Would sync {$etudiantUsers->count()} users from etudiants table"],
                ['Exclude', 'testCreators (not real users)'],
                ['Update', 'etudiant.user_id to match new central user IDs'],
            ]);
            return;
        }

        // Ensure school exists
        $school = DB::connection('centrale')
            ->table('schools')
            ->where('database', 'mind_bridge')
            ->first();

        if (!$school) {
            DB::connection('centrale')->table('schools')->insert([
                'name' => 'Mind Bridge',
                'domain' => 'mindbridge-admin.awlyg.tech',
                'database' => 'mind_bridge',
                'migrations_path' => 'database/migrations/mindBridge/',
                'organization_id' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $school = DB::connection('centrale')
                ->table('schools')
                ->where('database', 'mind_bridge')
                ->first();
            $this->info('✅ Created MindBridge school');
        }

        $syncedCount = 0;
        $updatedCount = 0;

        foreach ($etudiantUsers as $oldUserId) {
            // Get etudiant data
            $etudiantData = DB::connection('mind_bridge')
                ->table('etudiants')
                ->where('user_id', $oldUserId)
                ->first();

            if ($etudiantData) {
                // Check if user already exists by email
                $existingUser = DB::connection('centrale')
                    ->table('users')
                    ->where('email', $etudiantData->email)
                    ->first();

                if (!$existingUser) {
                    // Create new user
                    $newUserId = DB::connection('centrale')->table('users')->insertGetId([
                        'name' => trim(($etudiantData->first_name ?? '') . ' ' . ($etudiantData->last_name ?? '')) ?: "User {$oldUserId}",
                        'email' => $etudiantData->email,
                        'phone' => $etudiantData->phone ?? "phone_{$oldUserId}",
                        'password' => bcrypt('password'),
                        'avatar' => $etudiantData->avatar ?? 'users/profiles/dev-user-avatar.png',
                        'is_mind_bridge_user' => true,
                        'type' => 'etudiant',
                        'active' => true,
                        'status' => 'active',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    // Link to school
                    DB::connection('centrale')->table('user_school')->insert([
                        'user_id' => $newUserId,
                        'school_id' => $school->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    $syncedCount++;
                    $this->info("✅ Created user: {$etudiantData->email} (Old ID: {$oldUserId}, New ID: {$newUserId})");
                } else {
                    $newUserId = $existingUser->id;
                    $this->info("ℹ️  User already exists: {$etudiantData->email} (ID: {$newUserId})");
                }

                // Update etudiant record if user ID changed
                if ($oldUserId != $newUserId) {
                    DB::connection('mind_bridge')
                        ->table('etudiants')
                        ->where('user_id', $oldUserId)
                        ->update(['user_id' => $newUserId]);
                    $updatedCount++;
                    $this->info("🔄 Updated etudiant user_id: {$oldUserId} → {$newUserId}");
                }
            }
        }

        $this->info("✅ Synced {$syncedCount} new users, updated {$updatedCount} etudiant records");
    }

    /**
     * Run full synchronization
     */
    private function runFullSync($dryRun = false)
    {
        if ($dryRun) {
            $this->info('🧪 Would run full MindBridge data synchronization...');
            $this->table(['Step', 'Action'], [
                ['1', 'Ensure MindBridge school exists'],
                ['2', 'Sync Niveaux (Educational Levels)'],
                ['3', 'Sync Matieres (Subjects)'],
                ['4', 'Sync Users (etudiants only, no testCreators)'],
                ['5', 'Sync Niveau-Matiere relationships'],
                ['6', 'Update MindBridge references'],
            ]);
            return;
        }

        $this->info('🚀 Running full data synchronization...');
        
        $seeder = new MindBridgeDataSyncSeeder();
        $seeder->run();
    }
}
