<?php

namespace App\Http\Middleware;
use Closure;

class BeforeAuthentication
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next)
    {
        $app_secret = $request->header('APPSECRET') ? $request->header('APPSECRET') : $request->APP_SECRET;
        if($app_secret){
            if(array_search($app_secret, config('platforms_secrets.app_secrets'))){
            } else {
                return response()->json(['_response' => [
                    'code' => '401',
                    'message' => 'Invalid APP_SECRET.'
                ]], 401);
            }

            return $next($request);
        } else {
            return response()->json(['_response' => [
                'code' => '401',
                'message' => 'Missing APP SECRET!'
            ]], 401);
        }

    }
}
