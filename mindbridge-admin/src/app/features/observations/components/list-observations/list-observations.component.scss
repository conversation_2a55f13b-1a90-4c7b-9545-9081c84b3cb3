.observations-container {
  max-width: 1400px;
  margin: 0 auto;

  h1 {
    color: #333;
  }

  button {
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  table {
    border-collapse: collapse;

    thead {
      background-color: #f9fafb;

      th {
        font-weight: 600;
        color: #374151;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
      }
    }

    tbody {
      tr {
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f3f4f6;
        }

        td {
          color: #374151;
        }
      }
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

