import { Action, createReducer, on } from "@ngrx/store";
import * as sharedActions from '../store/shared.actions'

export interface SharedState {
    isAppLoading: boolean;
}
export const initialAuthState: SharedState = {
  isAppLoading: false,
};

const featureReducer = createReducer(
  initialAuthState,
  on(
    sharedActions.SetLoading, (state, action) => ({
      ...state,
      isAppLoading: action.isAppLoading
    })),
);
  
  export function sharedReducer(state: SharedState | undefined, action: Action) {
    return featureReducer(state, action);
  }