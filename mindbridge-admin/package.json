{"name": "mind-bridge-bo", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "dev": "npx ng serve --host 0.0.0.0 --port 8888 --disable-host-check", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.0.5", "@angular/cdk": "^19.0.4", "@angular/common": "^19.0.5", "@angular/compiler": "^19.0.5", "@angular/core": "^19.0.5", "@angular/forms": "^19.0.5", "@angular/material": "^19.0.4", "@angular/platform-browser": "^19.0.5", "@angular/platform-browser-dynamic": "^19.0.5", "@angular/router": "^19.0.5", "@joeattardi/emoji-button": "^4.6.4", "@ngrx/effects": "^19.0.0", "@ngrx/entity": "^19.0.0", "@ngrx/operators": "^19.0.0", "@ngrx/signals": "^19.0.0", "@ngrx/store": "^19.0.0", "@ngrx/store-devtools": "^19.0.0", "chart.js": "^4.4.7", "ng2-charts": "^8.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.6", "@angular/cli": "^19.0.6", "@angular/compiler-cli": "^19.0.5", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.16", "typescript": "~5.6.3"}}