<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EtudiantTestAnswers extends Model
{
    use SoftDeletes;

    protected $connection = 'mind_bridge';

    protected $table = 'etudiant_test_answers';
    protected $fillable = ['test_id', 'etudiant_id', 'question_id', 'selected_options', 'comment', 'score'];

    protected $casts = [
        'selected_options' => 'array',
    ];

    public function test()
    {
        return $this->belongsTo(Test::class, 'test_id');
    }

    public function etudiant()
    {
        return $this->belongsTo(MindBridgeEtudiant::class, 'etudiant_id');
    }

    public function question()
    {
        return $this->belongsTo(Question::class, 'question_id');
    }
}
