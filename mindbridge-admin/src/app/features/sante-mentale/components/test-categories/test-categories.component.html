<div class="mt-7 flex flex-col items-center px-7">
  <div class="flex w-full flex-row justify-start">
    <h2>Catégories et Tests</h2>
  </div>
  <div class="ml-2.5 mt-4 flex w-full flex-col justify-between text-start">
    <div class="flex space-x-6">
      @for (
        category of testCategoriesHealthMental().children;
        track category.id
      ) {
        <ng-container>
          <button
            class="cursor-pointer py-2 text-xs text-[#667085] transition duration-300 hover:text-[#6BA4BD]"
            [ngClass]="{
              'text-[#6BA4BD]': activeCategory?.id === category?.id,
            }"
            [ngStyle]="{
              'border-bottom':
                activeCategory.id === category.id
                  ? '2px solid #6BA4BD'
                  : '1px solid #667085',
            }"
            (click)="setActiveCategory(category)"
          >
            {{ category.name }}
          </button>
        </ng-container>
      }
    </div>

    <div class="tab-content mt-4">
      <ng-container>
        @if (categoryTests()) {
          <div class="tab-pane">
            <app-category-tests
              [category]="activeCategory"
            ></app-category-tests>
          </div>
        }
      </ng-container>
    </div>
  </div>
</div>
