import {
  Component,
  effect,
  inject,
  Input,
  OnChanges,
  Signal,
} from '@angular/core';
import { Ng<PERSON>lass, NgStyle } from '@angular/common';
import { SanteMentaleStore } from '../../sante-mentale.store';
import { CategoryTest } from '../../models/category-tests-response.model';
import { CategoryTestDetailsComponent } from '../category-test-details/category-test-details.component';
import { IChildren } from '../../../test-management/models/test-categories-board';
import { AddTestComponent } from '../../../test-management/components/add-test/add-test.component';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../core/app.state';

@Component({
  selector: 'app-category-tests',

  imports: [NgClass, NgStyle, CategoryTestDetailsComponent, AddTestComponent],
  templateUrl: './category-tests.component.html',
  styleUrl: './category-tests.component.scss',
})
export class CategoryTestsComponent implements OnChanges {
  private store = inject<Store<AppState>>(Store);
  @Input({ required: true }) category: IChildren;

  #santeMentaleStore = inject(SanteMentaleStore);

  categoryTests: Signal<CategoryTest[]> =
    this.#santeMentaleStore.state.categoryTests;

  activeTest!: CategoryTest;

  modeCreation = false;

  constructor() {
    effect(() => {
      if (this.categoryTests()) {
        this.activeTest = this.categoryTests()[0];
      }
    });
  }

  ngOnChanges() {
    this.modeCreation = false;
  }

  setActiveTest(test: CategoryTest) {
    this.activeTest = test;
    this.modeCreation = false;
  }

  createTest() {
    this.modeCreation = true;
  }
}
