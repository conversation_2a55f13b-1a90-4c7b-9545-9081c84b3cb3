<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EtudaintBadges extends Model
{
    use HasFactory;
    protected $connection = 'mind_bridge';

    protected $table = 'etudiant_badges';

    protected $fillable = [
        'eb_etudiant_id',
        'eb_badge_id',
    ];

    public function badge()
    {
        return $this->belongsTo(Badges::class, 'eb_badge_id');
    }
}