<?php

namespace App\Http\Controllers\MindBridge;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\MindBridge\Step;
use Exception;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class StepController extends Controller
{
    public function index(Request $request)
    {
        try {
            $steps = Step::orderBy("created_at", "desc")->get();
            return response()->json($steps, 200);
        } catch (Exception $e) {
            \Log::error("Error in StepController@index: ", ['exception' => $e]);
            return response()->json(['message' => 'Internal Server Error'], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            $this->validate($request, [
                'order' => 'required|integer',
                'description' => 'required|string',
                'test_id' => 'required|integer|exists:tests,id',
                'question_id' => 'required|integer|exists:questions,id',
                'condition' => 'nullable|string',
            ]);
            $step = Step::create($request->all());
            return response()->json($step, 201);
        } catch (ValidationException $e) {
            \Log::error('Error in StepController@store', ['exception' => $e]);
            return response()->json(['message' => 'Validation Failed', 'errors' => $e->errors()], 400);
        } catch (Exception $e) {
            \Log::error("Error in StepController@store: ", ['exception' => $e]);
            return response()->json(['message' => 'Internal Server Error'], 500);
        }
    }

    public function show($id)
    {
        try {
            $step = Step::with('test', 'question', 'question.options')->findOrFail($id);
            return response()->json($step, 200);
        } catch (ModelNotFoundException $e) {
            \Log::error("Error in StepController@show: ", ['exception' => $e]);
            return response()->json(['message' => 'Step not found'], 404);
        } catch (Exception $e) {
            \Log::error("Error in StepController@show: ", ['exception' => $e]);
            return response()->json(['message' => 'Internal Server Error'], 500);
        }
    }

    public function getNextStep(Request $request)
    {
        try {
            $request->validate([
                'current_step_id' => 'required|integer|exists:steps,id',
                'selected_options' => 'required|array',
                'selected_options.*' => 'integer',
            ], [
                'selected_options.required' => 'The selected options field is required.',
                'selected_options.*.integer' => 'Each selected option must be an integer.',
            ]);

            $nextStep = Step::getNextStep(
                $request->current_step_id,
                $request->selected_options,
            );
            if ($nextStep === null) {
                return response()->json(['message' => 'No next step found'], 404);
            }
            return response()->json($nextStep, 200);

        } catch (ValidationException $e) {
            \Log::error('Error in StepController@getNextStep', ['exception' => $e]);
            return response()->json(['message' => 'Validation Failed', 'errors' => $e->errors()], 400);
        } catch (Exception $e) {
            \Log::error('Error in StepController@getNextStep', ['exception' => $e]);
            return response()->json(['message' => 'Internal Server Error'], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $step = Step::findOrFail($id);
            $step->update($request->all());
            return response()->json($step, 200);
        } catch (ModelNotFoundException $e) {
            \Log::error("Error in StepController@update: ", ['exception' => $e]);
            return response()->json(['message' => 'Step not found'], 404);
        } catch (Exception $e) {
            \Log::error("Error in StepController@update: ", ['exception' => $e]);
            return response()->json(['message' => 'Internal Server Error'], 500);
        }
    }
}
