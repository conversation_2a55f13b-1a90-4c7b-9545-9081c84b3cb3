<?php

namespace App\Http\Controllers\MindBridge;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

use App\Models\MindBridge\HootyIA;

class HootyIAController extends Controller
{
    
    public function index()
    {
        $hootyIAs = HootyIA::all();

        return response()->json([
            'data' => $hootyIAs,
        ]);
    }
    public function store(Request $request)
    {  
    
        $validator = \Validator::make($request->all(), [
            'file' => 'required|file|mimes:zip,mp3,wav,pdf,jpeg,png,jpg',
            'action' => 'required|string',
        ], [
            'file.required' => 'The file is required',
            'file.mimes' => 'The file must be a zip, mp3, wav, pdf, jpeg or png',
            'action.required' => 'The action is required',
            'action.string' => 'The action must be string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->messages(),
            ], 422);
        }

        $file = $request->file('file');
        $filePath = $file->storeAs('hooty_ia', $file->getClientOriginalName());

        $hootyIA = new HootyIA();
        $hootyIA->file_path = $filePath;
        $hootyIA->action = $request->input('action');
        $hootyIA->save();

        return response()->json([
            'message' => 'File uploaded successfully',
            'filePath' => $hootyIA->file_path,
        ]);
    }

    public function get($id)
    {
        $hootyIA = HootyIA::findOrFail($id);

        return response()->json([
            'file_path' => $hootyIA->file_path,
            'action' => $hootyIA->action,
            'result' => $hootyIA->result,
        ]);
    }
}
