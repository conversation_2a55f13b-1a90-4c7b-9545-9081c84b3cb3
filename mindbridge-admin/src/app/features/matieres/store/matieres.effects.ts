import { HostListener, Injectable, inject } from "@angular/core";
import { Store } from "@ngrx/store";
import { catchError, finalize, map, switchMap, tap } from 'rxjs/operators';
import { of } from "rxjs";
import { AppState } from "../../../core/app.state";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { CommonService } from "../../shared/common.service";
import { SetLoading } from "../../shared/store/shared.actions";
import * as Matieres from "./matieres.actions";
import { HttpErrorResponse } from "@angular/common/http";
import { MatieresService } from "../matieres.service";
import {ChapterResponse} from "../models/chapter-response.model";

@Injectable()
export class MatieresEffects {
    private actions$ = inject(Actions);
    store = inject<Store<AppState>>(Store);
    private matieresService = inject(MatieresService);
    private commonService = inject(CommonService);


    isScreenSizeValid(): boolean {
        return window.innerWidth >= 600;
    }

    @HostListener('window:resize', ['$event'])
    onResize() {
        this.isScreenSizeValid();
    }

    // Matieres effects
    AllMatieresEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.AllMatieres),
            switchMap((action) =>
                this.matieresService.allMatieres().pipe(
                    map((matieres: any) => Matieres.AllMatieresSuccess({ matieres })),
                    catchError(() =>
                        of(
                            Matieres.AllMatieresError()
                        ).pipe(
                            tap(() => this.commonService.showToast('Credentials error!', 'ERROR'))
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    AddMatiereEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.AddMatiere),
            switchMap((action) =>
                this.matieresService.addMatiere(action.payload).pipe(
                    map((matiere: any) => Matieres.AddMatiereSuccess({ matiere })),
                    tap(() => {
                        this.commonService.showToast('Matiere ajoutée avec succès', 'SUCCESS');
                        this.store.dispatch(Matieres.AllMatieres());
                    }),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            Matieres.AddMatiereError()
                        ).pipe(
                            tap(() => {
                                this.commonService.showToast(err.error, 'ERROR')
                            })
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    UpdateMatiereEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.UpdateMatiere),
            switchMap((action) =>
                this.matieresService.updateMatiere(action.payload, action.id).pipe(
                    map((matiere: any) => Matieres.UpdateMatiereSuccess({ matiere })),
                    tap(() => {
                        this.commonService.showToast('Matiere mise à jour avec succès', 'SUCCESS');
                        this.store.dispatch(Matieres.AllMatieres());
                    }),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            Matieres.UpdateMatiereError()
                        ).pipe(
                            tap(() => {
                                this.commonService.showToast(err.error, 'ERROR');
                            })
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    DeleteMatiereEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.DeleteMatiere),
            switchMap((action) =>
                this.matieresService.deleteMatiere(action.id).pipe(
                    map((message: string) => Matieres.DeleteMatiereSuccess({ message })),
                    tap((res: any) => {
                        this.commonService.showToast(res.message.message, 'SUCCESS');
                        this.store.dispatch(Matieres.AllMatieres());
                    }),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            Matieres.DeleteMatiereError()
                        ).pipe(
                            tap(() => {
                                this.commonService.showToast(err.error.error, 'ERROR')
                            })
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    // Niveaus effects
    AllNiveausEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.AllNiveaus),
            switchMap((action) =>
                this.matieresService.allNiveaus().pipe(
                    map((niveaus: any) => Matieres.AllNiveausSuccess({ niveaus })),
                    catchError(() =>
                        of(
                            Matieres.AllNiveausError()
                        ).pipe(
                            tap(() => this.commonService.showToast('Credentials error!', 'ERROR'))
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    // Chapitres effects
    AllChapitresEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.AllChapitres),
            switchMap((action) =>
                this.matieresService.allChapitres().pipe(
                    map((chapitres: any) => Matieres.AllChapitresSuccess({ chapitres })),
                    catchError(() =>
                        of(
                            Matieres.AllChapitresError()
                        ).pipe(
                            tap(() => this.commonService.showToast('Credentials error!', 'ERROR'))
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    AddChapitreEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.AddChapitre),
            switchMap((action) =>
                this.matieresService.addChapitre(action.payload).pipe(
                    map((chapitre: any) => Matieres.AddChapitreSuccess({ chapitre })),
                    tap(() => {
                        this.commonService.showToast('Chapitre ajoutée avec succès', 'SUCCESS');
                        this.store.dispatch(Matieres.AllChapitres());
                    }),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            Matieres.AddChapitreError()
                        ).pipe(
                            tap(() => {
                                this.commonService.showToast(err.error, 'ERROR')
                            })
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    UpdateChapitreEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.UpdateChapitre),
            switchMap((action) =>
                this.matieresService.updateChapitre(action.payload, action.id).pipe(
                    map((chapitre: any) => Matieres.UpdateChapitreSuccess({ chapitre })),
                    tap(() => {
                        this.commonService.showToast('Chapitre mise à jour avec succès', 'SUCCESS');
                        this.store.dispatch(Matieres.AllChapitres());
                    }),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            Matieres.UpdateChapitreError()
                        ).pipe(
                            tap(() => {
                                this.commonService.showToast(err.error, 'ERROR');
                            })
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    DeleteChapitreEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.DeleteChapitre),
            switchMap((action) =>
                this.matieresService.deleteChapitre(action.id).pipe(
                    map((message: string) => Matieres.DeleteChapitreSuccess({ message })),
                    tap((res: any) => {
                        this.commonService.showToast(res.message.message, 'SUCCESS');
                        this.store.dispatch(Matieres.AllChapitres());
                    }),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            Matieres.DeleteChapitreError()
                        ).pipe(
                            tap(() => {
                                this.commonService.showToast(err.error.error, 'ERROR')
                            })
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    // Contenues effects
    AllContenuesEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.AllContenues),
            switchMap((action) =>
                this.matieresService.allContenues().pipe(
                    map((contenues: any) => Matieres.AllContenuesSuccess({ contenues })),
                    catchError(() =>
                        of(
                            Matieres.AllContenuesError()
                        ).pipe(
                            tap(() => this.commonService.showToast('Credentials error!', 'ERROR'))
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    AddContenueEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.AddContenue),
            switchMap((action) =>
                this.matieresService.addContenue(action.payload).pipe(
                    map((contenue: any) => Matieres.AddContenueSuccess({ contenue })),
                    tap(() => {
                        this.commonService.showToast('Contenue ajoutée avec succès', 'SUCCESS');
                        this.store.dispatch(Matieres.AllContenues());
                    }),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            Matieres.AddContenueError()
                        ).pipe(
                            tap(() => {
                                this.commonService.showToast(err.error, 'ERROR')
                            })
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    UpdateContenueEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.UpdateContenue),
            switchMap((action) =>
                this.matieresService.updateContenue(action.payload, action.id).pipe(
                    map((contenue: any) => Matieres.UpdateContenueSuccess({ contenue })),
                    tap(() => {
                        this.commonService.showToast('Contenue mise à jour avec succès', 'SUCCESS');
                        this.store.dispatch(Matieres.AllContenues());
                    }),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            Matieres.UpdateContenueError()
                        ).pipe(
                            tap(() => {
                                this.commonService.showToast(err.error, 'ERROR');
                            })
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    DeleteContenueEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType(Matieres.DeleteContenue),
            switchMap((action) =>
                this.matieresService.deleteContenue(action.id).pipe(
                    map((message: string) => Matieres.DeleteContenueSuccess({ message })),
                    tap((res: any) => {
                        this.commonService.showToast(res.message.message, 'SUCCESS');
                        this.store.dispatch(Matieres.AllContenues());
                    }),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            Matieres.DeleteContenueError()
                        ).pipe(
                            tap(() => {
                                this.commonService.showToast(err.error.error, 'ERROR')
                            })
                        )
                    ),
                    finalize(() => {
                        this.store.dispatch(SetLoading({ isAppLoading: false }));
                    })
                )
            ),
            finalize(() => { })
        )
    );

    // get chapters
  GetChaptersEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType(Matieres.GetChapiters),
      switchMap((action) =>
        this.matieresService.chapterByMatiereAndNiveau( {matiereId: action.matierId, niveauId: action.niveauId}).pipe(
          map((chapters: ChapterResponse[]) => Matieres.GetChapitersSuccess({ chapters })),
          tap(() => {
            // this.commonService.showToast('Matiere mise à jour avec succès', 'SUCCESS');
          }),
          catchError((err: HttpErrorResponse) =>
            of(
              Matieres.GetChapitersError()
            ).pipe(
              tap(() => {
                console.log(err)
                this.commonService.showToast(err.error.message, 'ERROR');
              })
            )
          )
        )
      )
    )
  );
}
