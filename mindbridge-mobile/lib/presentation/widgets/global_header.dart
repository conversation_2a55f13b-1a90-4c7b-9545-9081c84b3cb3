import 'package:flutter/material.dart';
import 'package:mindbridge/core/utilities/constants.dart';
import 'package:sizer/sizer.dart';

import '../../app/theme/colors.dart';
import '../../core/utilities/app_images.dart';

class GlobalHeader extends StatelessWidget implements PreferredSizeWidget {
  final bool showSearchIcon;
  final bool showNotificationIcon;
  final List<Widget>? customActions;

  const GlobalHeader({
    super.key,
    this.showSearchIcon = true,
    this.showNotificationIcon = true,
    this.customActions,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      elevation: 0,
      title: Row(
        children: [
          Image.asset(
            AppImages.logo,
            height: 4.h,
          ),
          SizedBox(width: 2.w),
          Flexible(
            child: Text(
              appName,
              style: TextStyle(
                color: AppColors.black,
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      actions: customActions ??
          [
            // if (showSearchIcon)
            // IconButton(
            //   onPressed: () {
            //     // Search action
            //   },
            //   icon: Image.asset(AppImages.search),
            // ),
            if (showNotificationIcon)
              IconButton(
                onPressed: () {
                  // Notifications action
                },
                icon: Image.asset(AppImages.notification),
              ),
          ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(7.h);
}
