import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

import '../../app/theme/colors.dart';

class ReturnBackButton extends StatelessWidget {
  final VoidCallback? onTap;
  final IconData? customIcon;
  const ReturnBackButton({super.key, this.onTap, this.customIcon});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.secondary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(2.h),
      ),
      child: IconButton(
        icon: Icon(
          customIcon ?? Icons.arrow_back,
          color: AppColors.secondary,
          size: 20,
        ),
        onPressed: onTap ?? () => Get.back(),
      ),
    );
  }
}
