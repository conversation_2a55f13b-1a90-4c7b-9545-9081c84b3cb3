import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/core/utilities/extensions.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import '../../../widgets/avatar_btm_sheet.dart';
import '../../../widgets/avatar_clipper.dart';
import '../../../widgets/profile_info_section.dart';
import '../controller/home_controller.dart';

class ProfileCard extends StatelessWidget {
  final double avatarSize;
  final String avatarId;
  final Function(String) onAvatarChange;

  const ProfileCard({
    super.key,
    required this.avatarSize,
    required this.avatarId,
    required this.onAvatarChange,
  });

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find<HomeController>();

    return Obx(() {
      final profile = controller.profile.value;

      return Stack(
        alignment: Alignment.center,
        clipBehavior: Clip.none,
        children: [
          ClipPath(
            clipper: AvatarClipper(fromTop: true),
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 4.w),
              padding: EdgeInsets.only(
                left: 4.w,
                right: 4.w,
                bottom: 3.h,
                top: avatarSize * 0.8,
              ),
              decoration: BoxDecoration(
                color: profile?.niveau?.color!.toColor(),
                borderRadius: BorderRadius.circular(20),
              ),
              child: ProfileInfoSection(
                name: profile?.name ?? "...",
                gamificationName: profile?.nameGamification ?? "...",
                levelName: profile?.niveau?.name ?? "...",
                balance: "${profile?.points ?? 0} pts",
                ranking:
                    "${profile?.classement ?? 0} / ${profile?.countEtudiant ?? 0}",
                bgGradient: AppColors.customGradient,
              ),
            ),
          ),
          Positioned(
              top: -avatarSize * 0.5,
              child: AvatarBottomSheet(
                avatarSize: avatarSize,
                avatarId: avatarId,
                onAvatarChange: onAvatarChange,
              )),
        ],
      );
    });
  }
}
