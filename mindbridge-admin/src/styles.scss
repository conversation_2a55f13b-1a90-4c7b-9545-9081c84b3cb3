/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

.under_construction {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 78vh;
    text-align: center;
    font-size: 2rem;
    color: #000;
    font-weight: 600;
}

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }

.flex-column {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.flex-row {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* You can add global styles to this file, and also import other style files */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body {
    width: 100%;
    height: 100vh;
}

html,
body {
    height: 100%;
}

body {
    margin: 0;
    font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

// Global style

input,
button,
textarea,
select {
    all: unset;
}

select,
.mat-select,
.mat-mdc-select {
    background-image: url('assets/icones/arrow_select.png');
    background-position: 96% 50%;
    background-repeat: no-repeat;
}

button {
    cursor: pointer;
}

// Colors global style
.bg-block {
    background-color: #FFFFFF;
    box-shadow: 0px 4px 8px 0px #6BA4BD40;
}

.bg-color-head {
    background-color: #FFFFFF;
    border-bottom: 0.5px solid #6BA4BD4D
}

.bg-color-btn {
    background-color: #ffffff;
    color: #6BA4BD;
    border-radius: 6px;
    padding: 10px 18px;
    letter-spacing: 1.1px;
    white-space: pre;
    font-size: medium;
    font-weight: normal;
    height: calc(100% - 20px);

    .mat-icon {
        font-size: 20px !important;
        width: auto !important;
    }
}

.bg-active-btn {
    background-color: #6BA4BD !important;
    color: #ffffff !important;
}

.justify_between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bg_color_gray {
    background-color: #FCFCFD;
}

// Fonts global style
.font-head {}

.font-btn {
    font-weight: 500;
    font-size: 16px;
}

.font-card-head {}

// Flex global style
.df {
    display: flex;
    align-items: center;
    gap: 8px;
}

.flex-column {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.db {
    display: block;
}

.flex-column-card {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.under_construction {
    width: 100%;
    height: calc(100vh - 100px);
    font-size: 25px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center
}

.dimensions_container {
    width: 95%;
    margin: 0 auto;
}

.left-btn {
    margin-left: auto;
}

// Filters global style
.filters {
    height: 45px;
    margin: 21px 0;
    display: flex;
    align-items: center;
    justify-content: center;

    .search {
        width: 315px;
        height: 100%;
        border-radius: 6px;
        padding: 0 0 0 0;
        position: relative;

        >.mat-icon {
            position: absolute;
            top: 50%;
            left: 7px;
            transform: translateY(-50%);
            height: 20px;
            font-size: 20px !important;
        }

        input {
            width: 100%;
            height: 100%;
            border-radius: 6px;
            padding: 0 14px 0 35px;
            border: 1px solid #D0D5DD;
        }
    }

    .input_bar {
        width: 16%;
        min-width: 160px;

        .label_field {
            margin-left: 7px;
            color: #707070;
            font-size: 15px;
        }
    }

    .filter-btn {
        height: 70%;
        padding: 0 15px;
        border-radius: 6px;
        background-color: #f4f4f3;
    }
}

// Table global style
.table {
    width: 100%;
    height: calc(100vh - 180px);
    max-height: calc(100vh - 180px);
    border: 2px solid #d0d5dd;
    overflow: auto;
    border-radius: 6px;

    table {
        width: 100%;

        tr:first-child {
            position: sticky;
            top: 0;
            background-color: #ffffff;
        }

        tr {
            min-height: 55px;
            display: grid;
            grid-template-columns: .4fr .2fr .2fr .2fr .5fr .5fr .25fr;

            th,
            td {
                text-align: left;
                color: #475467;
                font-size: 15px;
                font-weight: 500;
                display: flex;
                align-items: center;
            }

            th:first-child,
            td:first-child {
                padding-left: 30px;
            }

            th:last-child,
            td:last-child {
                justify-content: center;
            }

            td {
                .mat-icon {
                    width: 29px;
                    height: 29px;
                }
            }
        }

        tr:not(:first-child) {
            border-top: 1px solid #D0D5DD;
        }
    }
}

// Fixed style material
.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background {
    border-color: #6BA4BD !important;
    background-color: #6BA4BD !important;
}

.input_field {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.input_date .mat-datepicker-toggle {
    position: absolute !important;
    right: 5px !important;
    top: 22px !important;
}

.mdc-snackbar__label,
.mdc-snackbar__surface {
    padding: 0 !important;
}

.input_required {
    border: 1px solid red !important;
}

.required {
    color: red !important;
}

.truncate {
    width: 85%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

button:disabled,
select:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.mat-mdc-progress-bar {
    .mdc-linear-progress__bar-inner {
        border-color: #6BA4BD !important;
    }
}

button:disabled {
    cursor: not-allowed !important;
    opacity: 0.5 !important;
}

// Navbar style of app
.navbar {
    padding: 17px 28px;
    gap: 20px;

    .content_navbar {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .icon {
        width: 45px;
        height: 45px;

        .svg_left_sidebar {
            width: 100%;
            height: 100%;
        }
    }

    h1 {
        font-weight: 600;
        margin: 0;
        color: #1D242E80;
    }
}

.arrow {
    width: 15px;
    height: 15px;
    transform: rotate(-90deg);
}

.mt-5 {
    margin-top: 5px;
}

.ml-5 {
    margin-left: 5px;
}

.pb-0 {
    padding-bottom: 0 !important;
}

.b-1 {
    border-bottom: 2px solid rgb(0 0 0 / 12%);
}

.image {
    width: 60px;
    height: 60px;
    background-color: #d0d5dd;
    position: relative;

    img {
        width: 60px;
        height: 60px;
    }

    .input_file {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        opacity: 0;
        cursor: pointer;
    }
}

.top_bar {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    margin: 10px 0;

    .add_button {
        padding: 12px 35px;
        background-color: #6BA4BD;
        display: flex;
        color: #ffffff;
        font-size: 15px;
        border: 1px solid #70707000;
        border-radius: 5px;
        font-weight: 600;
        margin-left: auto;

        .svg_btn {
            background-color: #fff;
            padding: 6px;
            border-radius: 11px;
            width: 15px;
        }
    }
}

.multiple_select_of_dialog .mat-mdc-select-arrow-wrapper {
    display: none !important;
}

.multiple_select_of_dialog .mdc-text-field--filled:not(.mdc-text-field--disabled),
.multiple_select_of_dialog .mat-mdc-form-field-focus-overlay {
    background: transparent !important;
}

.multiple_select_of_dialog .mdc-text-field,
.multiple_select_of_dialog .mdc-text-field--no-label .mat-mdc-form-field-infix {
    padding: 0 !important;
}

.multiple_select_of_dialog .mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after,
.multiple_select_of_dialog .mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before {
    border: 0 !important;
}

.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked {
    background-color: #6BA4BD !important;
}

.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text {
    color: #6BA4BD !important;
}

.multiple_select_of_dialog .mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after {
    display: none !important;
}

.multiple_select_of_dialog .mat-mdc-form-field-subscript-wrapper.mat-mdc-form-field-bottom-align {
    display: none !important;
}

.mat-mdc-form-field.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill.mat-primary.ng-tns-c3736059725-0 {
    padding: 0 !important;
    height: 47px !important;
}

.primary {
    background: linear-gradient(to bottom, #C1EFF7 0%, #6BA4BD 42%, #7E588E 100%, #BE1A87 100%);
    height: 30px;
    span {
        color: #fff;
    }
}

.secondary {
    color: #568397;
}

// .accent {
//     color: #e74c3c;
// }

.bg-primary {
    background-color: #3498db;
}

.bg-secondary {
    background-color: #5683972b;
}

// .bg-accent {
//     background-color: #e74c3c;
// }

h1, h2, h3, h4, h5, h6, p {
    margin: 0 !important;
}

.bg-gray-50 {
    background-color: rgb(249 250 251);
}

.bg-white {
    background-color: rgb(255 255 255);
}

.mi-auto {
    margin-inline: auto;
}

.mdc-switch__icons {
    display: none !important;
}

.mdc-switch__track {
    height: 18px !important;
}

.mdc-switch__handle {
    width: 90% !important;
    height: 81% !important;
    left: 3px !important;
}

.mat-mdc-slide-toggle.mat-accent {
    --mdc-switch-selected-track-color: #6ba4bda2 !important;
    --mdc-switch-selected-handle-color: #6BA4BD !important;
}

.mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
    background-color: #4caf50 !important;
    /* Green for checked state */
}

.mat-slide-toggle-thumb {
    background-color: #ff9800 !important;
    /* Orange for the thumb */
}

.bg_btn {
    background: linear-gradient(180deg, #C1EFF7 6%, #6BA4BD 100%, #7E588E 100%, #BE1A87 100%);
    color: #fff;
}

.bg_btn_censel {
    background-color: #56839729;
    color: #568397;
}

.mat-step-icon {
    display: none;
}

.mat-horizontal-stepper-header-container {
    width: fit-content !important;
    padding: 0 !important;
}

.mat-horizontal-stepper-header {
    padding-left: 0 !important;
    padding-right: 25px !important;
}

.mat-step-text-label {
    padding-left: 5px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

.mat-step-label-selected {
    color: #6BA4BD !important;
    position: relative !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;

    &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #6BA4BD !important;
    }
}

.mat-stepper-horizontal-line {
    display: none !important;
}

.mat-horizontal-content-container {
    background-color: #fff !important;
    padding: 16px !important;
}
.green-radio-button {
  --mat-radio-label-text-color: rgb(13, 115, 51) !important;
  --mdc-radio-disabled-selected-icon-color: rgb(13, 115, 51) !important;
  --mat-radio-disabled-label-color: rgb(13, 115, 51) !important;
  --mat-radio-checked-ripple-color: rgb(13, 115, 51) !important;
  --mdc-radio-selected-hover-icon-color: rgb(13, 115, 51) !important;
  --mdc-radio-selected-pressed-icon-color: rgb(13, 115, 51) !important;
  --mdc-radio-selected-focus-icon-color: rgb(13, 115, 51) !important;
  --mdc-radio-selected-icon-color: rgb(13, 115, 51) !important;
}

.red-radio-button {
  --mat-radio-label-text-color: rgb(239, 68, 68) !important;
  --mdc-radio-disabled-selected-icon-color: rgb(239, 68, 68) !important;
  --mat-radio-disabled-label-color: rgb(239, 68, 68) !important;
  --mat-radio-checked-ripple-color: rgb(239, 68, 68) !important;
  --mdc-radio-selected-hover-icon-color: rgb(239, 68, 68) !important;
  --mdc-radio-selected-pressed-icon-color: rgb(239, 68, 68) !important;
  --mdc-radio-selected-focus-icon-color: rgb(239, 68, 68) !important;
  --mdc-radio-selected-icon-color: rgb(239, 68, 68) !important;
}
.nbr-niv {
  background-color: #F2F4F7;
}
