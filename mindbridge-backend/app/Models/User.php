<?php

namespace App\Models;

use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable
{   
    use SoftDeletes, HasApiTokens, Notifiable, HasRoles; 
     /**
    * The connection name for the model.
    *
    * @var string
    */
   protected $connection = 'centrale';
   protected $table = 'users';


    protected $fillable = ['name', 'email', 'password', 'avatar', 'active','phone',"first_login_completed","fcm_token","type","status", 'is_mind_bridge_user'];

    protected $hidden = ['password', 'remember_token'];

    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function schools()
    {
        return $this->belongsToMany(School::class, 'user_school');
    }
}
