<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('etudiants', function (Blueprint $table) {
            $table->id();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('email')->nullable()->unique();
            $table->datetime('last_connection_at')->nullable();
            $table->integer('connection_streak')->nullable()->default(0);
            $table->string('identifiant')->nullable();
            $table->string('nni')->nullable();
            $table->string('avatar')->default('path/to/default/avatar.png');
            $table->string('test_profiling_completed')->default(false);
            $table->unsignedBigInteger("user_id");
            $table->foreign('user_id')->references('id')->on('centrale.users');
            $table->unsignedBigInteger("school_id");
            $table->foreign('school_id')->references('id')->on('centrale.schools');
            $table->unsignedBigInteger('niveau_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('etudiants');
    }
};
