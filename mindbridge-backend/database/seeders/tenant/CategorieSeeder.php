<?php

namespace Database\Seeders\tenant;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorieSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currentTimestamp = now();
        $categories = [
            [
                'name' => 'Terminale',
                'position' => 1
            ],
            [
                'name' => 'Lycée',
                'position' => 2
            ],
            [
                'name' => 'Collège',
                'position' => 3
            ],
            [
                'name' => 'Primaire',
                'position' => 4
            ],
            [
                'name' => 'Jardin',
                'position' => 5
            ],
        ];
        $categories = array_map(function ($category) use ($currentTimestamp) {
            return array_merge($category, [
                'created_at' => $currentTimestamp,
                'updated_at' => $currentTimestamp,
            ]);
        }, $categories);

        \DB::table('categories')->insert($categories);
    }
}
