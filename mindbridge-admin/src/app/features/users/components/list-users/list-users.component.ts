import { Component, inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { AddUserComponent } from '../add-user/add-user.component';
import { MatIcon } from '@angular/material/icon';


@Component({
    selector: 'app-list-users',
    templateUrl: './list-users.component.html',
    styleUrl: './list-users.component.scss',
    imports: [ReactiveFormsModule, MatIcon]
})
export class ListUsersComponent {
  private dialog = inject(MatDialog);

  form: FormGroup = new FormGroup({});

  add() {
    this.dialog.open(AddUserComponent, {
      data: {},
      disableClose: true
    });
  }
}
