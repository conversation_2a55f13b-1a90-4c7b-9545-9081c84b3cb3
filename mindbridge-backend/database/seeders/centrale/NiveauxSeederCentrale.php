<?php

namespace Database\Seeders\centrale;

use App\Models\AnneeScolaire;
use App\Models\Semestre;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NiveauxSeederCentrale extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        

        \DB::table('niveaux')->insert([ // Orange clair
            ['name' => 'Seconde', 'color' => '#1E90FF', 'background' => '#B0E0E6'],
            // // École Primaire
            // ['name' => 'CP', 'color' => '#FFD700', 'background' => '#FFF8DC'],  // Doré clair
            // ['name' => 'CE1', 'color' => '#FF8C00', 'background' => '#FFE4B5'], // Bleu pastel
            // ['name' => 'CM1', 'color' => '#32CD32', 'background' => '#98FB98'], // Vert clair
            // ['name' => 'CM2', 'color' => '#FF69B4', 'background' => '#FFB6C1'], // Rose pastel
        
            // // Collège
            // ['name' => '6e', 'color' => '#6A5ACD', 'background' => '#D8BFD8'],  // Lavande
            // ['name' => '5e', 'color' => '#20B2AA', 'background' => '#AFEEEE'], // Turquoise pâle
            // ['name' => '4e', 'color' => '#DC143C', 'background' => '#F08080'], // Rouge clair
            // ['name' => '3e', 'color' => '#FF4500', 'background' => '#FFA07A'], // Saumon clair
        
            // // Lycée Général
            // ['name' => '2nde', 'color' => '#2E8B57', 'background' => '#90EE90'], // Vert pâle
            // ['name' => '1ère', 'color' => '#4B0082', 'background' => '#9370DB'], // Mauve clair
            // ['name' => 'Terminale', 'color' => '#800000', 'background' => '#CD5C5C'], // Rouge terre
        
            // // Lycée Professionnel
            // ['name' => 'CAP 1', 'color' => '#008080', 'background' => '#AFEEEE'], // Turquoise clair
            // ['name' => 'CAP 2', 'color' => '#A0522D', 'background' => '#DEB887'], // Beige
            // ['name' => 'BEP', 'color' => '#4682B4', 'background' => '#B0C4DE'],  // Bleu clair
        ]);
        
        
    }
}
