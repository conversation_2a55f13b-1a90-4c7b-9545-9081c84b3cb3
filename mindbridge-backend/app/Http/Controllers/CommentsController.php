<?php

namespace App\Http\Controllers;

use App\Models\Comments;
use App\Models\User;
use App\Notifications\CommentNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log; 
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;


class CommentsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $comments = Comments::all();    
        return response()->json($comments);
    }

    

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $rules = [
                'first_name' => 'required|string',
                'last_name'  => 'nullable|string',
                'email'      => 'required|email',
                'phone'      => 'required|string',
                'comment'    => 'required|string',
            ];
            $messages = [
                'first_name.required'=> 'first name is required',
                'email.required'=> 'email is required',
                'phone.required'=> 'phone is required',
                'comment.required'=> 'comment is required',
            ];

            $request->validate($rules, $messages);

            $recipientEmail = '<EMAIL>';

            $comment = Comments::create($request->all());
    
            Notification::route('mail', $recipientEmail)->notify(new CommentNotification($comment));
            

            return response()->json([
                'success' => "Comment added successfully",
                "comment" => $comment
            ], Response::HTTP_OK);
    
        } catch (ValidationException $e) {
            \Log::info("Validation error: " . json_encode($e->errors()));

            return response()->json([
                'error' => $e->errors(),
            ], 422);
        }catch (\Exception $e) {
            Log::error("Exception occurred: " . $e->getMessage());
            return response()->json([
                'error' => "Error adding comment"
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}


