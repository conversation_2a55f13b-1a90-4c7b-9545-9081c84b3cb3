<?php

namespace App\Http\Middleware;

use App\Service\TenantService;
use Closure;
use App\Models\School;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class TenantsMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    // public function handle(Request $request, Closure $next): Response
    // {
    //     $user = auth()->user();
    //     if (!$user) {
    //         \Log::info("User not authenticated");
    //         throw new NotFoundHttpException('User not authenticated');
    //     }

    //     $schoolId = $request->header('X-School-ID');
    //     $tenant = !is_null($request->header('X-School-ID')) ?  $user->schools()->where('schools.id', $schoolId)->first() : $user->schools()->first();

    //     if (!$tenant) {
    //         \Log::info("School not found for user");
    //         throw new NotFoundHttpException('School not found for user');
    //     }

    //     TenantService::switchToTenant($tenant->database);

    //     return $next($request);
    // }

    public function handle(Request $request, Closure $next): Response
    {
        $origin = $request->header('Origin');
        $parsedUrl = parse_url($origin);
        $host = $parsedUrl['host'] ?? null;
        $tenant = School::where('domain', $host)->first();
        if (!$tenant) {
            $tenantDomain = $request->query('tenant');
            \Log::info($tenantDomain);
            $tenant = School::where('domain', $tenantDomain)->first();
            if(!$tenant){
                throw new NotFoundHttpException('Centre not found');
            }
        }
        TenantService::switchToTenant($tenant->database);
        return $next($request);
    }
}
