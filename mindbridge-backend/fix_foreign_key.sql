-- First, drop the old foreign key constraint
ALTER TABLE `mind_bridge`.`tests` DROP FOREIGN KEY `tests_created_by_foreign`;

-- Then, add the new foreign key constraint pointing to the correct database
ALTER TABLE `mind_bridge`.`tests` 
ADD CONSTRAINT `tests_created_by_foreign` 
FOREIGN KEY (`created_by`) 
REFERENCES `centrale`.`users` (`id`) 
ON DELETE SET NULL;

-- Verify the constraint was created
SHOW CREATE TABLE `mind_bridge`.`tests`;
