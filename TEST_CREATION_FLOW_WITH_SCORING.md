# Test Creation Flow with Scoring System

## Complete Workflow: Creating a WISC-V Style Test

### Step 1: Create Test (Existing)
```
Admin Panel → Sante Mentale → Add New Test
├── Title: "Test Cognitif - WISC-V"
├── Description: "Évalue tes compétences cognitives..."
├── Category: "Sante Mentale"
├── Difficulty: "Intermédiaire"
└── Timer: 45 minutes
```

### Step 2: Create Sections (NEW)
```
After test creation, add sections:

Section 1: Compréhension Verbale
├── Order: 1
├── Max Score: 10
└── Description: "Évalue ta compréhension verbale..."

Section 2: Raisonnement Perceptif
├── Order: 2
├── Max Score: 10
└── Description: "Évalue ta capacité à résoudre des puzzles..."

Section 3: Mémoire de Travail
├── Order: 3
├── Max Score: 10
└── Description: "Évalue ta mémoire de travail..."

Section 4: Vitesse de Traitement
├── Order: 4
├── Max Score: 10
└── Description: "Évalue ta vitesse de traitement..."
```

### Step 3: Add Questions to Sections (MODIFIED)
```
For each section, add questions:

Section 1 - Question 1:
├── Content: "Quel mot correspond à cette image ?"
├── Image: elephant.jpg (2MB, JPG/PNG/GIF)
├── Type: Multiple Choice (one)
├── Points: 1
├── Options:
│   ├── A) Lion (incorrect)
│   ├── B) Cheval (incorrect)
│   └── C) Éléphant (✓ correct)
└── Feedback:
    ├── Correct: "Bien joué ! C'est un éléphant."
    └── Incorrect: "C'était un éléphant. Pas de souci..."

Section 1 - Question 2:
├── Content: "Quel est le synonyme de 'rapide' ?"
├── Type: Multiple Choice (one)
├── Points: 1
├── Options:
│   ├── A) Lent (incorrect)
│   ├── B) Vif (✓ correct)
│   └── C) Lourd (incorrect)
└── Feedback: [as above]

... (repeat for all questions)
```

### Step 4: Configure Scoring Rules (NEW)
```
For each section, define score thresholds:

Section 1 Scoring Rules:
├── Rule 1: 85-100% → "Très bonnes capacités verbales"
│   └── Recommendation: "Pas de besoin de consultation"
├── Rule 2: 60-84% → "Capacités verbales satisfaisantes"
│   └── Recommendation: "Proposer des exercices supplémentaires"
├── Rule 3: 40-59% → "Difficulté notable en compréhension verbale"
│   └── Recommendation: "Consultation recommandée avec un psychologue"
└── Rule 4: 0-39% → "Faiblesses significatives"
    └── Recommendation: "Consultation fortement recommandée"

(Repeat for Sections 2, 3, 4)
```

### Step 5: Configure Overall Test Rules (NEW)
```
Overall Test Scoring:
├── Rule 1: 85-100% → "Très bonnes capacités cognitives"
│   └── Recommendation: "Pas de besoin de consultation"
├── Rule 2: 60-84% → "Capacités cognitives globalement satisfaisantes"
│   └── Recommendation: "Proposer des exercices supplémentaires"
├── Rule 3: 40-59% → "Difficulté notable dans certaines fonctions"
│   └── Recommendation: "Consultation recommandée"
└── Rule 4: 0-39% → "Faiblesses significatives"
    └── Recommendation: "Consultation fortement recommandée"
```

---

## Student Test Taking Flow

### Mobile App: Taking the Test
```
1. Student opens test
   ├── Sees: "Test Cognitif - WISC-V"
   ├── Sees: Overall description
   └── Sees: "Commencer le test" button

2. Student starts test
   ├── Sees: "Partie 1: Compréhension Verbale"
   ├── Sees: Section description
   └── Sees: Question 1 with image

3. For each question:
   ├── Displays: Question content
   ├── Displays: Image (if present)
   ├── Displays: Answer options
   ├── Student selects answer
   └── Shows: Feedback (after submission)

4. After all questions:
   ├── Shows: "Partie 2: Raisonnement Perceptif"
   └── Repeats process for each section

5. Test completion:
   ├── Calculates: Section scores
   ├── Calculates: Overall score
   ├── Generates: Personalized feedback
   └── Shows: Results screen
```

### Results Screen (NEW)
```
Test Cognitif - WISC-V
Résultats Finaux

Overall Score: 30/40 (75%)
Status: Capacités cognitives globalement satisfaisantes

Section Results:
├── Compréhension Verbale: 8/10 (80%)
│   └── Feedback: "Tes compétences verbales sont très bonnes..."
│   └── Recommendation: "Continue à lire et découvrir..."
│
├── Raisonnement Perceptif: 7/10 (70%)
│   └── Feedback: "Tu as montré une bonne capacité..."
│   └── Recommendation: "Super !"
│
├── Mémoire de Travail: 6/10 (60%)
│   └── Feedback: "Tu peux améliorer un peu ta mémoire..."
│   └── Recommendation: "Joue à des jeux de mémoire"
│
└── Vitesse de Traitement: 9/10 (90%)
    └── Feedback: "Ta rapidité est impressionnante !"
    └── Recommendation: "Continue comme ça !"

Overall Recommendation:
"Capacités cognitives globalement satisfaisantes.
Proposer des exercices supplémentaires."

[Retour au Dashboard]
```

---

## Database State After Test Creation

### tests table
```
id | title                    | category_id | difficulty_level
1  | Test Cognitif - WISC-V   | 11          | intermediaire
```

### test_sections table
```
id | test_id | title                      | order | max_score
1  | 1       | Compréhension Verbale      | 1     | 10
2  | 1       | Raisonnement Perceptif     | 2     | 10
3  | 1       | Mémoire de Travail         | 3     | 10
4  | 1       | Vitesse de Traitement      | 4     | 10
```

### steps table (MODIFIED)
```
id | test_id | question_id | section_id | order | points_value
1  | 1       | 101         | 1          | 1     | 1
2  | 1       | 102         | 1          | 2     | 1
3  | 1       | 103         | 1          | 3     | 1
4  | 1       | 104         | 2          | 4     | 1
... (etc)
```

### test_scoring_rules table (NEW)
```
id | test_id | section_id | min_score | max_score | interpretation
1  | 1       | 1          | 85        | 100       | Très bonnes capacités
2  | 1       | 1          | 60        | 84        | Capacités satisfaisantes
3  | 1       | 1          | 40        | 59        | Difficulté notable
4  | 1       | 1          | 0         | 39        | Faiblesses significatives
... (repeat for sections 2, 3, 4)
```

### etudiant_test_status table (ENHANCED)
```
id | etudiant_id | test_id | status   | score | section_scores | feedback | recommendations
1  | 5           | 1       | TERMINE  | 30    | {...}          | {...}    | {...}
```

---

## Admin Panel UI Changes Needed

### Test Details View (MODIFIED)
```
Test: Test Cognitif - WISC-V
├── [Edit Test Info]
├── Sections
│   ├── [+ Add Section]
│   ├── Section 1: Compréhension Verbale [Edit] [Delete]
│   ├── Section 2: Raisonnement Perceptif [Edit] [Delete]
│   ├── Section 3: Mémoire de Travail [Edit] [Delete]
│   └── Section 4: Vitesse de Traitement [Edit] [Delete]
├── Questions
│   ├── [+ Add Question]
│   ├── Q1 (Section 1): Quel mot... [Edit] [Delete]
│   ├── Q2 (Section 1): Quel est le synonyme... [Edit] [Delete]
│   └── ... (etc)
└── Scoring Rules
    ├── [+ Add Rule]
    ├── Section 1: 85-100% → "Très bonnes..." [Edit] [Delete]
    └── ... (etc)
```


