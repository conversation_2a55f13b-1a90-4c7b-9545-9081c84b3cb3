<?php

namespace App\Models\MindBridge;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;
    protected $connection = 'mind_bridge';
    protected $table = 'categories';

    protected $fillable = [
        'name',
        'parent_id',
        'image_url',
        'description',
        'is_active',
        'position',
        'code',
        'gradient_background',
        'button_text',
        'count',
        'gradient_border',
        'action_type',
        'is_mobile',
        'icon',
        'is_bo',
    ];

    public function subcategories()
    {
        return $this->hasMany(Category::class, 'parent_id', 'id');
    }
}
