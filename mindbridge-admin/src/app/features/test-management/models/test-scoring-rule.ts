export interface TestScoringRule {
  id?: number;
  test_id: number;
  min_percentage: number;
  max_percentage: number;
  interpretation: string;
  feedback?: string;
  recommendation?: string;
  created_at?: string;
  updated_at?: string;
}

export class TestScoringRuleRequest {
  min_percentage: number;
  max_percentage: number;
  interpretation: string;
  feedback?: string;
  recommendation?: string;

  constructor() {
    this.min_percentage = null;
    this.max_percentage = null;
    this.interpretation = '';
    this.feedback = '';
    this.recommendation = '';
  }
}

export interface TestScoringRuleResponse {
  data: TestScoringRule[];
  test_id: number;
}

