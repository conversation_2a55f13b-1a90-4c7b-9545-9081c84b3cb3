import { Action, createReducer, on } from "@ngrx/store";
import * as <PERSON>ier<PERSON> from "./matieres.actions";
import {ChapterResponse} from "../models/chapter-response.model";
import {GetChapitersSuccess} from "./matieres.actions";

export interface MatiereState {
    matieres: any,
    matiere: any,
    message: string,
    niveaus: any,
    chapitres: any,
    chapitre: any,
    contents: any,
    content: any,
    chapters: ChapterResponse[],
}
export const initialMatiereState: MatiereState = {
    matieres: null,
    matiere: null,
    message: '',
    niveaus: null,
    chapitres: null,
    chapitre: null,
    contents: null,
    content: null,
    chapters: []
}

const featureReducer = createReducer(
    initialMatiereState,
    on(
        Matiere.AllMatieresSuccess,
        Matiere.AddMatiereSuccess,
        Matiere.UpdateMatiereSuccess,
        Matiere.DeleteMatiereSuccess,
        Matiere.AllNiveausSuccess,
        Matiere.AllChapitresSuccess,
        Matiere.AddChapitreSuccess,
        Matiere.UpdateChapitreSuccess,
        Matiere.DeleteChapitreSuccess,
        Matiere.AllContenuesSuccess,
        Matiere.AddContenueSuccess,
        Matiere.UpdateContenueSuccess,
        Matiere.DeleteContenueSuccess,

        (state, action) => ({ ...state, ...action })
    ),
    on(
      Matiere.GetChapitersSuccess,
      (state, action) => ({
        ...state,
        chapters: action.chapters
      } )
    ),
    on(Matiere.ClearChapters, (state) => ({
      ...state,
      chapters: []
    }))
)

export function matiereReducer(state: MatiereState | undefined, action: Action) {
    return featureReducer(state, action);
}
