import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { TestResponse } from './models/test-response';
import { environment } from '../../../environments/environment';
import { ApiResponse } from '../../core/models/api-response';
import {
  getQuery,
  objectToFormData,
  Pagination,
  removeEmptyValues,
} from '../shared/helpers/query.helper';
import { ITestSearchForm } from './models/test-search-request';
import { ITestCategoriesBoardResponse } from './models/test-categories-board';
import { TestCreationRequestWrapper } from './models/test-creation-form';
import { TestCreationResponseWrapper } from './models/test-creation-response';
import { TestUpdateRequestWrapper } from './models/update-creation-form';
import { TestUpdateResponseWrapper } from './models/test-update-response';

@Injectable({
  providedIn: 'root',
})
export class TestManagementService {
  private http = inject(HttpClient);

  getListeTests(
    searchForm: ITestSearchForm,
    pagination: Pagination
  ): Observable<ApiResponse<TestResponse>> {
    const params = getQuery(searchForm, pagination);

    return this.http.get<ApiResponse<TestResponse>>(
      environment.BASE_URL_API +
        'mind_bridge/tests?tenant=mind-bridge-test.awlyg.local',
      { params }
    );
  }

  getTestById(testId: number): Observable<TestResponse> {
    return this.http.get<TestResponse>(
      environment.BASE_URL_API +
        'mind_bridge/tests/' +
        testId +
        '?tenant=mind-bridge-test.awlyg.local'
    );
  }
  getTestCategoriesBoard(): Observable<ITestCategoriesBoardResponse> {
    const url = environment.BASE_URL_API + 'mind_bridge/categories/board';
    console.log('[TestManagementService] Fetching categories board from URL:', url);
    return this.http.get<ITestCategoriesBoardResponse>(url).pipe(
      tap((response) => {
        console.log('[TestManagementService] Full response object:', response);
        console.log('[TestManagementService] Response keys:', Object.keys(response));
        console.log('[TestManagementService] test_models:', response?.test_models);
        console.log('[TestManagementService] mental_health:', response?.mental_health);
        console.log('[TestManagementService] mental_health children:', response?.mental_health?.children);
      }),
      catchError((error) => {
        console.error('[TestManagementService] Error fetching categories board:', error);
        console.error('[TestManagementService] Error status:', error.status);
        console.error('[TestManagementService] Error statusText:', error.statusText);
        console.error('[TestManagementService] Error response:', error.error);
        throw error;
      })
    );
  }

  createTest(data: TestCreationRequestWrapper): Observable<any> {
    const formData: FormData = objectToFormData(removeEmptyValues(data));
    return this.http.post<TestCreationResponseWrapper>(
      environment.BASE_URL_API + 'mind_bridge/tests/storeAll',
      formData
    );
  }

  deleteTest(id: number): Observable<void> {
    return this.http.delete<void>(
      environment.BASE_URL_API + 'mind_bridge/tests/' + id
    );
  }

  editTest(id: number, data: TestUpdateRequestWrapper): Observable<any> {
    return this.http.put<TestUpdateResponseWrapper>(
      environment.BASE_URL_API + 'mind_bridge/tests/updateAll/' + id,
      data
    );
  }
}
