<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\User;
use App\Http\Requests\LoginRequest;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Hash;
use App\Models\MindBridge\MindBridgeEtudiant;

class AuthController extends Controller
{

    public function mindberdgeLogin(LoginRequest $loginRequest)
    {
            \Log::info("mindberdgeLogin");
            $user = User::where("type", "admin")->where("is_mind_bridge_user", true)
                ->whereNull('deleted_at')->where('email', $loginRequest->email)->first();

            if (!$user || !Hash::check($loginRequest->password, $user->password)) {
                return response()->json(['message' => trans("globalConst.auth.login_incorrect")], 401);
            }

            $token = $user->createToken('authToken')->plainTextToken;

            $user->tokens()->latest()->first()->update([
                'expires_at' => Carbon::now()->addMinutes(720),
            ]);

            if (!$user->active) {
                return response()->json(['message' => "Votre compte n'est pas activé. Veuillez contacter l'administrateur pour activer votre compte.!"], 401);
            }

            return response([
                'token' => $token,
                'user' => new UserResource($user),
            ]);
    }

    public function loginEtudiant(LoginRequest $loginRequest)
    {
        $credentials = $loginRequest->only('email', 'phone', 'password');
        $fcmToken = $loginRequest->input('fcm_token');

        if (empty($credentials['email']) && empty($credentials['phone'])) {
            return response()->json(['message' => 'Email or phone must be provided'], 400);
        }

        $query = User::with('schools')->whereNull('deleted_at');

        if (!empty($credentials['phone'])) {
            $query->where('phone', $credentials['phone']);
        } elseif (!empty($credentials['email'])) {
            $query->where('email', $credentials['email']);
        }

        $query->where('is_mind_bridge_user', true);

        $user = $query->first();

        if (!$user || !Hash::check($credentials['password'], $user->password) || $user->status == "inactive") {
            \Log::info("inactive");
            return response()->json(['message' => trans("globalConst.auth.login_incorrect")], 401);
        }

        if ($user->type != "etudiant") {
            return response()->json(['message' => 'Only users of type etudiant can log in'], 403);
        }

        $tenant = $user->schools()->first();

        if (!$tenant) {
            return response()->json(['message' => 'School not found for user'], 404);
        }

        $etudiant = MindBridgeEtudiant::where('user_id', $user->id)->first();
        if (!$etudiant) {
            return response()->json(['message' => 'Etudiant not found for user'], 404);
        }

        $user = User::find($user->id);
        if ($fcmToken){
            $user->update([
                "fcm_token" => $fcmToken
            ]);
        }

        $welcomeMessages = config('motivational_messages.welcome');
        $randomWelcomeMessage = $welcomeMessages[array_rand($welcomeMessages)];
    
        // Remplacer le placeholder :prenom par le prénom de l'utilisateur
        $randomWelcomeMessage = str_replace(':prenom', $etudiant->first_name, $randomWelcomeMessage);
    
        return response([
            'token' => $user->createToken('authToken')->plainTextToken,
            'user' => $user,
            'etudiant' => $etudiant,
            'welcome_message' => $randomWelcomeMessage,
        ]);
    }
}
