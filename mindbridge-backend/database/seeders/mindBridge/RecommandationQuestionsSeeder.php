<?php

namespace Database\Seeders\mindBridge;

use Illuminate\Database\Seeder;
use App\Models\MindBridge\Question;

class RecommandationQuestionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $niveauId = 1;

        $questions = [
            // ====================================
            // Français (matiere_id = 1)
            // ====================================
            // Facile (5 true/false)
            ['type'=>'text','response_type'=>'true_false','content'=>'L\'adjectif s\'accorde en genre et en nombre avec le nom qu\'il qualifie.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>1,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],
            ['type'=>'text','response_type'=>'true_false','content'=>'"Ils est partis" est une phrase correctement accordée.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>1,'niveau_id'=>$niveauId,'is_true'=>false,'is_false'=>true,'answer'=>false],
            ['type'=>'text','response_type'=>'true_false','content'=>'Le mot "châtaigne" prend un accent circonflexe sur le a.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>1,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],
            ['type'=>'text','response_type'=>'true_false','content'=>'"On s\'est vu hier" utilise correctement le pronom.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>1,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],
            ['type'=>'text','response_type'=>'true_false','content'=>'Le verbe "aller" est un verbe du troisième groupe.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>1,'niveau_id'=>$niveauId,'is_true'=>false,'is_false'=>true,'answer'=>false],

            // Moyen (5 single-choice)
            ['type'=>'text','response_type'=>'one','content'=>'Quelle est la nature grammaticale de "rapidement" ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>1,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Adverbe','isCorrect'=>true],
                ['name'=>'Adjectif','isCorrect'=>false],
                ['name'=>'Nom','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Quel est le synonyme de "serein" ?','description'=>'Choisissez la meilleure option.','difficulty'=>'moyen','matiere_id'=>1,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Calme','isCorrect'=>true],
                ['name'=>'Agité','isCorrect'=>false],
                ['name'=>'Prudent','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Dans quelle phrase trouve-t-on une subordonnée relative ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>1,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'L\'homme qui parle est professeur.','isCorrect'=>true],
                ['name'=>'Il mange rapidement.','isCorrect'=>false],
                ['name'=>'Nous partons demain.','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Comment s\'écrit le participe passé du verbe "prendre" ?','description'=>'Choisissez la forme correcte.','difficulty'=>'moyen','matiere_id'=>1,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Prenu','isCorrect'=>false],
                ['name'=>'Pris','isCorrect'=>true],
                ['name'=>'Prendu','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Quelle proposition contient une apostrophe correcte ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>1,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'J\'ai lu le livre.','isCorrect'=>true],
                ['name'=>'Je\'suis fatigué.','isCorrect'=>false],
                ['name'=>'Il va\'à l\'école.','isCorrect'=>false],
            ]],

            // Difficile (5 multiple-choice)
            ['type'=>'text','response_type'=>'many','content'=>'Cochez toutes les figures de style dans : "Le soleil se couche en silence, laissant place à la lune argentée."','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>1,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Personnification','isCorrect'=>true],
                ['name'=>'Métaphore','isCorrect'=>true],
                ['name'=>'Allitération','isCorrect'=>false],
                ['name'=>'Oxymore','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Parmi ces mots, cochez ceux qui sont homonymes.','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>1,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Vert / Verre','isCorrect'=>true],
                ['name'=>'Saut / Sceau','isCorrect'=>true],
                ['name'=>'Maire / Marre','isCorrect'=>false],
                ['name'=>'Foi / Fois','isCorrect'=>true],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Identifiez les conjonctions de coordination dans la phrase : "Il pleut, pourtant il sort sans parapluie car il est pressé."','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>1,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'pourtant','isCorrect'=>true],
                ['name'=>'car','isCorrect'=>true],
                ['name'=>'il','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Sélectionnez les modes verbaux exprimant un souhait ou une nécessité.','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>1,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Subjonctif','isCorrect'=>true],
                ['name'=>'Impératif','isCorrect'=>true],
                ['name'=>'Indicatif','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Cochez les propositions subordonnées conjonctives dans : "Bien qu\'il soit tard, nous continuerons notre travail jusqu\'à minuit."','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>1,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Bien qu\'il soit tard','isCorrect'=>true],
                ['name'=>'jusqu\'à minuit','isCorrect'=>false],
                ['name'=>'nous continuerons notre travail','isCorrect'=>false],
            ]],

            // ====================================
            // Mathématiques (matiere_id = 2)
            // ====================================
            // Facile (5 true/false)
            ['type'=>'text','response_type'=>'true_false','content'=>'La somme des angles d\'un triangle est toujours égale à 180 degrés.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>2,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],
            ['type'=>'text','response_type'=>'true_false','content'=>'Un carré est un rectangle.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>2,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],
            ['type'=>'text','response_type'=>'true_false','content'=>'0 divisé par 5 est égal à 0.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>2,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],
            ['type'=>'text','response_type'=>'true_false','content'=>'La multiplication est commutative.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>2,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],
            ['type'=>'text','response_type'=>'true_false','content'=>'Le nombre 1 est un nombre premier.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>2,'niveau_id'=>$niveauId,'is_true'=>false,'is_false'=>true,'answer'=>false],

            // Moyen (5 single-choice)
            ['type'=>'text','response_type'=>'one','content'=>'Quel est le résultat de 12 ÷ 3 ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>2,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'4','isCorrect'=>true],
                ['name'=>'3','isCorrect'=>false],
                ['name'=>'5','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Quelle est la formule de l\'aire du cercle ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>2,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'πr²','isCorrect'=>true],
                ['name'=>'2πr','isCorrect'=>false],
                ['name'=>'πd','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Quel est le prochain nombre de la suite : 2, 4, 8, 16, ___ ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>2,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'32','isCorrect'=>true],
                ['name'=>'24','isCorrect'=>false],
                ['name'=>'30','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Quelle est la valeur de x si 2x + 3 = 11 ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>2,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'4','isCorrect'=>true],
                ['name'=>'3','isCorrect'=>false],
                ['name'=>'5','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Quelle est la distance entre les points (0,0) et (3,4) ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>2,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'5','isCorrect'=>true],
                ['name'=>'6','isCorrect'=>false],
                ['name'=>'7','isCorrect'=>false],
            ]],

            // Difficile (5 multiple-choice)
            ['type'=>'text','response_type'=>'many','content'=>'Sélectionnez tous les nombres premiers dans la liste : 2, 6, 13, 21, 17, 25.','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>2,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'2','isCorrect'=>true],
                ['name'=>'6','isCorrect'=>false],
                ['name'=>'13','isCorrect'=>true],
                ['name'=>'21','isCorrect'=>false],
                ['name'=>'17','isCorrect'=>true],
                ['name'=>'25','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Cochez les propriétés vraies pour un parallélogramme.','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>2,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Les côtés opposés sont parallèles','isCorrect'=>true],
                ['name'=>'Les angles sont tous droits','isCorrect'=>false],
                ['name'=>'Les diagonales se coupent en leur milieu','isCorrect'=>true],
                ['name'=>'Il a quatre côtés égaux','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Parmi les formules suivantes, cochez celles utilisées pour calculer des aires.','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>2,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Base × hauteur','isCorrect'=>true],
                ['name'=>'2πr','isCorrect'=>false],
                ['name'=>'½ × base × hauteur','isCorrect'=>true],
                ['name'=>'πr²','isCorrect'=>true],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Identifiez les termes de la suite arithmétique de premier terme 5 et raison 3.','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>2,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'5','isCorrect'=>true],
                ['name'=>'8','isCorrect'=>true],
                ['name'=>'11','isCorrect'=>true],
                ['name'=>'14','isCorrect'=>true],
                ['name'=>'17','isCorrect'=>true],
            ]],

            // ====================================
            // Sciences Naturelles (matiere_id = 3)
            // ====================================
            // Facile (5 true/false)
            ['type'=>'text','response_type'=>'true_false','content'=>'Les plantes produisent de l\'oxygène lors de la photosynthèse.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>3,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],
            ['type'=>'text','response_type'=>'true_false','content'=>'Le cœur humain a quatre cavités.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>3,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],
            ['type'=>'text','response_type'=>'true_false','content'=>'L\'eau est composée d\'un atome d\'oxygène et de deux atomes d\'hydrogène.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>3,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],
            ['type'=>'text','response_type'=>'true_false','content'=>'Les mammifères pondent des œufs à la naissance.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>3,'niveau_id'=>$niveauId,'is_true'=>false,'is_false'=>true,'answer'=>false],
            ['type'=>'text','response_type'=>'true_false','content'=>'La Terre tourne autour du Soleil.','description'=>'Vrai ou faux ?','difficulty'=>'facile','matiere_id'=>3,'niveau_id'=>$niveauId,'is_true'=>true,'is_false'=>false,'answer'=>true],

            // Moyen (5 single-choice)
            ['type'=>'text','response_type'=>'one','content'=>'Quel gaz les humains expirent-ils principalement ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>3,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Dioxyde de carbone','isCorrect'=>true],
                ['name'=>'Oxygène','isCorrect'=>false],
                ['name'=>'Azote','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Quelle structure cellulaire contient le code génétique ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>3,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Noyau','isCorrect'=>true],
                ['name'=>'Mitochondrie','isCorrect'=>false],
                ['name'=>'Ribosome','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Quelle planète est la plus proche du Soleil ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>3,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Mercure','isCorrect'=>true],
                ['name'=>'Vénus','isCorrect'=>false],
                ['name'=>'Mars','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Quel organe filtre le sang et produit l\'urine ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>3,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Reins','isCorrect'=>true],
                ['name'=>'Foie','isCorrect'=>false],
                ['name'=>'Pancréas','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'one','content'=>'Quelle unité mesure la résistance électrique ?','description'=>'Choisissez la bonne réponse.','difficulty'=>'moyen','matiere_id'=>3,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Ohm','isCorrect'=>true],
                ['name'=>'Volt','isCorrect'=>false],
                ['name'=>'Ampère','isCorrect'=>false],
            ]],

            // Difficile (5 multiple-choice)
            ['type'=>'text','response_type'=>'many','content'=>'Parmi ces organismes, cochez ceux qui sont unicellulaires.','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>3,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Amoeba','isCorrect'=>true],
                ['name'=>'Levure','isCorrect'=>true],
                ['name'=>'Chat','isCorrect'=>false],
                ['name'=>'E. coli','isCorrect'=>true],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Cochez les phases de la mitose.','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>3,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Prophase','isCorrect'=>true],
                ['name'=>'Anaphase','isCorrect'=>true],
                ['name'=>'Interphase','isCorrect'=>false],
                ['name'=>'Télophase','isCorrect'=>true],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Sélectionnez tous les types de roches.','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>3,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Sédimentaire','isCorrect'=>true],
                ['name'=>'Ignée','isCorrect'=>true],
                ['name'=>'Métamorphique','isCorrect'=>true],
                ['name'=>'Poreuse','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Quels éléments sont essentiels à la photosynthèse ?','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>3,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Lumière','isCorrect'=>true],
                ['name'=>'Chlorophylle','isCorrect'=>true],
                ['name'=>'Dioxyde de carbone','isCorrect'=>true],
                ['name'=>'Azote','isCorrect'=>false],
            ]],
            ['type'=>'text','response_type'=>'many','content'=>'Parmi ces particules subatomiques, cochez celles qui ont une charge électrique.','description'=>'Plusieurs réponses possibles.','difficulty'=>'difficile','matiere_id'=>3,'niveau_id'=>$niveauId,'options'=>[
                ['name'=>'Électron','isCorrect'=>true],
                ['name'=>'Proton','isCorrect'=>true],
                ['name'=>'Neutron','isCorrect'=>false],
            ]],

            // ====================================
            // Finance (matiere_id = 4)
            // ====================================
            // Facile (5 true/false)
            [
                'type'           => 'text',
                'response_type'  => 'true_false',
                'content'        => 'L\'argent déposé sur un compte d\'épargne génère des intérêts.',
                'description'    => 'Vrai ou faux ?',
                'difficulty'     => 'facile',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'is_true'        => true,
                'is_false'       => false,
                'answer'         => true
            ],
            [
                'type'           => 'text',
                'response_type'  => 'true_false',
                'content'        => 'Une action représente une part de propriété d’une entreprise.',
                'description'    => 'Vrai ou faux ?',
                'difficulty'     => 'facile',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'is_true'        => true,
                'is_false'       => false,
                'answer'         => true
            ],
            [
                'type'           => 'text',
                'response_type'  => 'true_false',
                'content'        => 'Une obligation est un titre de créance émis par un gouvernement ou une entreprise.',
                'description'    => 'Vrai ou faux ?',
                'difficulty'     => 'facile',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'is_true'        => true,
                'is_false'       => false,
                'answer'         => true
            ],
            [
                'type'           => 'text',
                'response_type'  => 'true_false',
                'content'        => 'Un budget déséquilibré signifie que les revenus sont supérieurs aux dépenses.',
                'description'    => 'Vrai ou faux ?',
                'difficulty'     => 'facile',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'is_true'        => false,
                'is_false'       => true,
                'answer'         => false
            ],
            [
                'type'           => 'text',
                'response_type'  => 'true_false',
                'content'        => 'Le marché boursier est ouvert 24 heures sur 24, 7 jours sur 7.',
                'description'    => 'Vrai ou faux ?',
                'difficulty'     => 'facile',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'is_true'        => false,
                'is_false'       => true,
                'answer'         => false
            ],

            // Moyen (5 single-choice)
            [
                'type'           => 'text',
                'response_type'  => 'one',
                'content'        => 'Quel indicateur mesure la variation générale des prix à la consommation ?',
                'description'    => 'Choisissez la bonne réponse.',
                'difficulty'     => 'moyen',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'options'        => [
                    ['name' => 'Indice des prix à la consommation (IPC)', 'isCorrect' => true],
                    ['name' => 'Produit Intérieur Brut (PIB)',      'isCorrect' => false],
                    ['name' => 'Taux de chômage',                    'isCorrect' => false],
                ]
            ],
            [
                'type'           => 'text',
                'response_type'  => 'one',
                'content'        => 'Quelle institution fixe les taux directeurs en zone euro ?',
                'description'    => 'Choisissez la bonne réponse.',
                'difficulty'     => 'moyen',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'options'        => [
                    ['name' => 'Banque Centrale Européenne', 'isCorrect' => true],
                    ['name' => 'Fonds Monétaire International', 'isCorrect' => false],
                    ['name' => 'Banque Mondiale',             'isCorrect' => false],
                ]
            ],
            [
                'type'           => 'text',
                'response_type'  => 'one',
                'content'        => 'Quel document compare revenus et dépenses prévisionnels d’un projet ?',
                'description'    => 'Choisissez la bonne réponse.',
                'difficulty'     => 'moyen',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'options'        => [
                    ['name' => 'Budget prévisionnel', 'isCorrect' => true],
                    ['name' => 'Bilan comptable',      'isCorrect' => false],
                    ['name' => 'Compte de résultat',   'isCorrect' => false],
                ]
            ],
            [
                'type'           => 'text',
                'response_type'  => 'one',
                'content'        => 'Que signifie l’acronyme « PIB » ?',
                'description'    => 'Choisissez la bonne réponse.',
                'difficulty'     => 'moyen',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'options'        => [
                    ['name' => 'Produit Intérieur Brut',      'isCorrect' => true],
                    ['name' => 'Plan d’Investissement Bancaire','isCorrect' => false],
                    ['name' => 'Prime d’Intérêt Bancaire',      'isCorrect' => false],
                ]
            ],
            [
                'type'           => 'text',
                'response_type'  => 'one',
                'content'        => 'Quel est le rôle principal d’un assureur ?',
                'description'    => 'Choisissez la bonne réponse.',
                'difficulty'     => 'moyen',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'options'        => [
                    ['name' => 'Mutualiser les risques et indemniser les sinistres', 'isCorrect' => true],
                    ['name' => 'Gérer les comptes bancaires',                        'isCorrect' => false],
                    ['name' => 'Accorder des prêts aux particuliers',               'isCorrect' => false],
                ]
            ],

            // Difficile (5 multiple-choice)
            [
                'type'           => 'text',
                'response_type'  => 'many',
                'content'        => 'Quels sont les objectifs principaux de la politique monétaire ?',
                'description'    => 'Plusieurs réponses possibles.',
                'difficulty'     => 'difficile',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'options'        => [
                    ['name' => 'Stabilité des prix',         'isCorrect' => true],
                    ['name' => 'Plein emploi',               'isCorrect' => true],
                    ['name' => 'Répartition des richesses',  'isCorrect' => false],
                    ['name' => 'Croissance économique',      'isCorrect' => true],
                ]
            ],
            [
                'type'           => 'text',
                'response_type'  => 'many',
                'content'        => 'Parmi ces instruments financiers, cochez ceux considérés comme des produits dérivés.',
                'description'    => 'Plusieurs réponses possibles.',
                'difficulty'     => 'difficile',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'options'        => [
                    ['name' => 'Futures', 'isCorrect' => true],
                    ['name' => 'Obligations', 'isCorrect' => false],
                    ['name' => 'Options', 'isCorrect' => true],
                    ['name' => 'Actions', 'isCorrect' => false],
                    ['name' => 'Swaps', 'isCorrect' => true],
                ]
            ],
            [
                'type'           => 'text',
                'response_type'  => 'many',
                'content'        => 'Quelles sont les principales composantes du bilan d’une entreprise ?',
                'description'    => 'Plusieurs réponses possibles.',
                'difficulty'     => 'difficile',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'options'        => [
                    ['name' => 'Actif',   'isCorrect' => true],
                    ['name' => 'Passif',  'isCorrect' => true],
                    ['name' => 'Produit', 'isCorrect' => false],
                    ['name' => 'Charges', 'isCorrect' => false],
                ]
            ],
            [
                'type'           => 'text',
                'response_type'  => 'many',
                'content'        => 'Parmi ces ratios financiers, cochez ceux qui mesurent la solvabilité.',
                'description'    => 'Plusieurs réponses possibles.',
                'difficulty'     => 'difficile',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'options'        => [
                    ['name' => 'Ratio d’endettement',         'isCorrect' => true],
                    ['name' => 'Ratio de liquidité générale', 'isCorrect' => true],
                    ['name' => 'Marge brute',                 'isCorrect' => false],
                    ['name' => 'Rentabilité des capitaux propres', 'isCorrect' => false],
                ]
            ],
            [
                'type'           => 'text',
                'response_type'  => 'many',
                'content'        => 'Quels éléments sont nécessaires pour calculer le retour sur investissement (ROI) ?',
                'description'    => 'Plusieurs réponses possibles.',
                'difficulty'     => 'difficile',
                'matiere_id'     => 4,
                'niveau_id'      => $niveauId,
                'options'        => [
                    ['name' => 'Gain net',       'isCorrect' => true],
                    ['name' => 'Coût initial',   'isCorrect' => true],
                    ['name' => 'Durée du projet','isCorrect' => false],
                    ['name' => 'Taux d’imposition','isCorrect' => false],
                ]
            ],

        ];

        foreach ($questions as $qData) {
            $options = $qData['options'] ?? null;
            unset($qData['options']);

            $question = Question::create($qData);
            if ($options) {
                $question->options()->createMany($options);
            }
        }
    }
}
