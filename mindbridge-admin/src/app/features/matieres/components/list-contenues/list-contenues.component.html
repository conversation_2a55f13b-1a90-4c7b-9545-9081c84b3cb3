<div class="mt-2 flex flex-col items-center px-7">
  <div class="flex w-full flex-row justify-end">
    <div class="flex flex-1 items-start justify-start gap-4 text-left">
      <a routerLink="/matieres">
        <mat-icon class="p-0.5">arrow_back</mat-icon>
      </a>
      <h2>{{ this.matiereName }}</h2>
    </div>
    <button
      (click)="getContenus(matiereId())"
      class="ml-4 flex h-10 w-52 items-center justify-center gap-2 rounded-lg bg-gradient-to-b from-[#C1EFF7] via-[#6BA4BD] via-[42%] to-[#7E588E]"
    >
      <img class="p-0" src="assets/icones/plus.png" alt="" />
      <p class="text-white">Ajouter contenu</p>
    </button>
  </div>
  <form
    class="flex w-full justify-end"
    [formGroup]="form"
    (ngSubmit)="searchContents()"
  >
    <div
      class="my-4 flex flex-col items-center justify-center gap-2 md:flex-row md:items-end md:justify-end"
    >
      <div class="mr-2 rounded-lg border-2 border-gray-200 bg-white p-2">
        <select
          class="mr-10 w-full"
          formControlName="chapter"
          (change)="searchContents()"
        >
          <option [ngValue]="OPTION_ALL" selected>Tous les chapitres</option>
          @for (chapter of chaptersByMatiere(); track chapter.id) {
            <option [ngValue]="chapter.id">
              {{ chapter.chapter }}
            </option>
          }
        </select>
      </div>
      <div
        class="mr-2 rounded-lg border-2 border-gray-200 bg-white p-2 max-md:mr-2"
      >
        <select
          class="w-48"
          formControlName="niveau"
          (change)="searchContents()"
        >
          <option [ngValue]="OPTION_ALL" selected>Tous les niveaux</option>
          @for (niveau of allNiveaux(); track niveau.id) {
            <option [ngValue]="niveau.id">{{ niveau.name }}</option>
          }
        </select>
      </div>
    </div>
  </form>
  @if (true) {
    <div class="container mx-auto mt-2">
      <div class="overflow-x-auto rounded-lg border shadow-md">
        <table class="min-w-full divide-y divide-gray-200 bg-white">
          <tr class="flex w-full items-start justify-start text-left">
            <th class="w-1/5 px-4 py-3 font-medium">Nom du chapitre</th>
            <th class="w-1/5 px-4 py-3 font-medium">Titre leçon</th>
            <th class="w-1/4 px-4 py-3 font-medium">Description</th>
            <th class="w-1/12 px-4 py-3 font-medium max-md:px-0">Niveau</th>
            <th class="w-1/4"></th>
          </tr>
          @if (matiereContents()) {
            @for (contenue of matiereContents(); track contenue.id) {
              <tr class="flex items-start justify-start">
                <td class="w-1/5 px-4 py-3 font-medium">
                  {{ contenue.chapter.chapter }}
                </td>
                <td class="w-1/5 px-4 py-3 font-medium">
                  {{ contenue.title }}
                </td>
                <td class="w-1/4 px-4 py-3 font-medium">
                  {{
                    contenue.description === 'null'
                      ? '---'
                      : contenue.description
                  }}
                </td>
                <td
                  class="w-1/12 px-4 py-3 font-medium max-md:px-0"
                  style="display: flex; gap: 10px"
                >
                  <p
                    class="m-0 w-fit rounded-lg px-2.5 py-0.5 text-xs font-medium"
                    [ngStyle]="{
                      color: '#3538CD',
                      'background-color': '#EEF4FF',
                    }"
                  >
                    {{ contenue.niveau.name }}
                  </p>
                </td>
                <td
                  class="flex w-1/4 items-start justify-start gap-6 py-3 pl-4 pr-4 text-right font-medium max-md:gap-1 max-md:pl-0 max-md:pr-0.5"
                >
                  <button>Supprimer</button>
                  <button type="button" class="text-[#568397]">Modifier</button>
                  <button
                    type="button"
                    (click)="create_test(contenue)"
                    class="w-max text-[#365672]"
                  >
                    Créer test
                  </button>
                </td>
              </tr>
            }
          }
        </table>
        <hr />
        <div class="flex items-start justify-start gap-2 bg-white px-4 py-4">
          <div class="rounded-lg border border-gray-300 bg-white p-1.5">
            <button
              class="w-16 text-center font-medium"
              [disabled]="current_page() == 1"
              (click)="goToPage(Number(current_page()) - 1)"
            >
              Précédent
            </button>
          </div>
          <div class="rounded-lg border border-gray-300 bg-white p-1.5">
            <button
              class="w-14 text-center font-medium"
              [disabled]="current_page() == total_pages()"
              (click)="goToPage(Number(current_page()) + 1)"
            >
              Suivant
            </button>
          </div>
          <p class="flex-1 p-2 text-right font-medium">
            Page {{ current_page() }} de {{ total_pages() }}
          </p>
        </div>
      </div>
    </div>
  }
</div>
