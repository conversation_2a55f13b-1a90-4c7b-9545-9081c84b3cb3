<?php

namespace Database\Seeders;

use Database\Seeders\centrale\UsersSeeder;
use Database\Seeders\mindBridge\BadgetsSeeder;
use Database\Seeders\mindBridge\RecommandationQuestionsSeeder;
use Illuminate\Database\Seeder;
use Database\Seeders\centrale\TenantsSeeder;
use Database\Seeders\mindBridge\StepsSeeder;
use Database\Seeders\centrale\MatiersSeederCentrale;
use Database\Seeders\centrale\NiveauxMatiereSeederCentrale;
use Database\Seeders\centrale\NiveauxSeederCentrale;
use Database\Seeders\mindBridge\ContentSeeder;
use Database\Seeders\mindBridge\MindBridgeUsersSeeder;
use Database\Seeders\mindBridge\CategoriesSeeder;
use Database\Seeders\mindBridge\MentalHealthTestsSeeder;
use Database\Seeders\MindBridgeDataSyncSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $seederType = config('app.seeder_type', 'central');

        if ($seederType === 'sync') {
            // Run data synchronization seeder
            $this->call(MindBridgeDataSyncSeeder::class);
        } elseif ($seederType === 'tenant') {
            $originalDatabase = config('database.connections.tenant.database');
            if ($originalDatabase === 'mind_bridge') {
                $this->call(MindBridgeUsersSeeder::class);
                $this->call(CategoriesSeeder::class);
                $this->call(StepsSeeder::class);
                $this->call(ContentSeeder::class);
                $this->call(MentalHealthTestsSeeder::class);
                $this->call(RecommandationQuestionsSeeder::class);
                $this->call(BadgetsSeeder::class);
            }
        } else {
            $this->call(TenantsSeeder::class);
            $this->call(NiveauxSeederCentrale::class);
            $this->call(MatiersSeederCentrale::class);
            $this->call(NiveauxMatiereSeederCentrale::class);
        }
    }
}
