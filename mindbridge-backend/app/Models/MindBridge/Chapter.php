<?php

namespace App\Models\MindBridge;

use App\Models\MatiereCentrale;
use App\Models\NiveauCentrale;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Chapter extends Model
{
    use HasFactory;
    protected $connection = 'mind_bridge';
    protected $table = 'chapters';
    
    protected $fillable = [
        'chapter',
        'description',
        'matiere_id',
        'niveau_id'
    ];

    public function matiere()
    {
        return $this->belongsTo(MatiereCentrale::class);
    }

    public function niveau()
    {
        return $this->belongsTo(NiveauCentrale::class);
    }

    public function tests()
    {
        return $this->belongsToMany(Test::class, 'chapter_test', 'chapter_id', 'test_id');
    }
}
