import 'package:get/get.dart';

import '../../../../core/models/lesson.dart';
import '../../../../data/repositories/content_repository.dart';

class ContentController extends GetxController {
  final ContentRepository _contentRepository;

  ContentController({required ContentRepository contentRepository})
      : _contentRepository = contentRepository;

  var lessons = <Content>[].obs;
  var isLoading = false.obs;
  var isEmptyData = false.obs;
  var isError = false.obs;

  Future<void> fetchLessons(int matiereId) async {
    isLoading.value = true;
    isEmptyData.value = false;
    isError.value = false;

    try {
      final response = await _contentRepository.fetchLessons(matiereId);

      if (response.isError) {
        isError.value = true;
      } else if (response.isEmpty) {
        isEmptyData.value = true;
      } else {
        lessons.assignAll(response.data!.contents!);
      }
    } catch (e) {
      print("Error: $e");
      isError.value = true;
    } finally {
      isLoading.value = false;
    }
  }
}
