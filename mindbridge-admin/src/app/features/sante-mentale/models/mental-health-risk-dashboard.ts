export interface MentalHealthRiskDashboardResponse {
  success: boolean;
  data: MentalHealthRiskDashboardData;
}

export interface MentalHealthRiskDashboardData {
  summary: DashboardSummary;
  students: AtRiskStudent[];
  charts: DashboardCharts;
}

export interface DashboardSummary {
  total_at_risk_students: number;
  total_tests_completed: number;
  average_score: number;
  students_needing_attention: number;
  risk_distribution: RiskDistribution;
}

export interface RiskDistribution {
  high_risk: number;
  medium_risk: number;
  low_risk: number;
}

export interface AtRiskStudent {
  etudiant_id: number;
  student_name: string;
  niveau: string;
  niveau_id: number;
  test_count: number;
  average_score: number;
  lowest_score: number;
  risk_level: 'high' | 'medium' | 'low';
  tests: StudentTestResult[];
  triggers: StudentTriggerInfo;
  last_test_date: string;
}

export interface StudentTestResult {
  test_id: number;
  test_title: string;
  category_name: string;
  score: number;
  completed_at: string;
  percentage: number;
  interpretation: string;
}

export interface StudentTriggerInfo {
  counts: TriggerCounts;
  details: TriggerDetail[];
}

export interface TriggerCounts {
  parent: number;
  teacher: number;
  simulated_grade_shutdown: number;
}

export interface TriggerDetail {
  type: 'parent' | 'teacher' | 'simulated_grade_shutdown';
  label: string;
  description: string;
  test_id: number;
}

export interface DashboardCharts {
  students_by_niveau: StudentsByNiveauChart[];
  tests_by_category: TestsByCategoryChart[];
  trigger_distribution: TriggerDistributionChart[];
  score_trends: ScoreTrendsChart[];
}

export interface StudentsByNiveauChart {
  niveau: string;
  count: number;
  high_risk: number;
  medium_risk: number;
  low_risk: number;
}

export interface TestsByCategoryChart {
  category: string;
  count: number;
  average_score: number;
}

export interface TriggerDistributionChart {
  trigger_type: string;
  count: number;
  percentage: number;
}

export interface ScoreTrendsChart {
  month: string;
  month_key: string;
  average_score: number;
  test_count: number;
  high_risk_count: number;
  medium_risk_count: number;
  low_risk_count: number;
}

// Dashboard filter interfaces
export interface DashboardFilters {
  date_from?: string;
  date_to?: string;
  niveau_id?: number;
  risk_level?: 'high' | 'medium' | 'low';
}

export class DashboardFiltersForm implements DashboardFilters {
  date_from?: string;
  date_to?: string;
  niveau_id?: number;
  risk_level?: 'high' | 'medium' | 'low';

  constructor() {
    // Set default date range to current year
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    
    this.date_from = startOfYear.toISOString().split('T')[0];
    this.date_to = now.toISOString().split('T')[0];
    this.niveau_id = undefined;
    this.risk_level = undefined;
  }
}

// Chart.js data interfaces for the dashboard
export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  tension?: number;
  fill?: boolean;
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartOptions {
  responsive: boolean;
  maintainAspectRatio: boolean;
  plugins?: {
    legend?: {
      display: boolean;
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
    title?: {
      display: boolean;
      text: string;
    };
  };
  scales?: {
    x?: {
      display: boolean;
      title?: {
        display: boolean;
        text: string;
      };
    };
    y?: {
      display: boolean;
      beginAtZero: boolean;
      title?: {
        display: boolean;
        text: string;
      };
    };
  };
}

// Risk level colors and styling
export const RISK_LEVEL_COLORS = {
  high: '#FF6B6B',
  medium: '#FFD93D',
  low: '#6BCF7F'
} as const;

export const RISK_LEVEL_LABELS = {
  high: 'Risque Élevé',
  medium: 'Risque Moyen',
  low: 'Risque Faible'
} as const;

// Chart color schemes
export const CHART_COLORS = {
  primary: '#6BA4BD',
  secondary: '#2D2EFF',
  success: '#05CD99',
  warning: '#FFD93D',
  danger: '#FF6B6B',
  info: '#6EC6FF',
  light: '#F4F7FE',
  dark: '#2B3674'
} as const;
