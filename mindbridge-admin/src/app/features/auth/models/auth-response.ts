// import { Month } from "src/app/shared/emuns/months";

export interface AuthResponse {
    // months: Month;
    // allMonths: Month;
    token: string;
    user: any;
    bankilyCode: any;
    schoolName: any;
}

export interface User {
    roles?: any[];
    status: string

    id: number,
    name: string,
    phone: string,
    avatar: string,
    active: 1,
    type: string,
    fcm_token: string,
    first_login_completed: number,
    created_at: string,
    updated_at: string
}
