import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/core/utilities/extensions.dart';
import 'package:mindbridge/presentation/screens/home/<USER>/evaluation_param.dart';
import 'package:mindbridge/presentation/widgets/flat_button.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/evaluation.dart';
import '../../../widgets/btm_navigation_bar.dart';
import '../../../widgets/global_error.dart';
import '../../../widgets/global_header.dart';
import '../../../widgets/global_loader.dart';
import '../../../widgets/global_white_card.dart';
import '../../../widgets/gradient_button.dart';
import '../controller/evaluation_controller.dart';

class EvaluationView extends GetView<EvaluationController> {
  final EvaluationParam param;

  const EvaluationView({super.key, required this.param});


  @override
  Widget build(BuildContext context) {
    final evalCtrl = Get.find<EvaluationController>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (param.contentId != null){
        evalCtrl.fetchEvaluationData(param.contentId!, param.isLesonCompleted ?? false);
      }else if(param.type == "challenge_hebdomadaire"){
        evalCtrl.fetchChallengeHebdomadaireData();
      }
    });

    return BaseScreen(
      child: Scaffold(
        appBar: const GlobalHeader(),
        body: Obx(() {
          return Stack(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 5.w),
                child: Column(
                  children: [
                    SizedBox(height: 2.h),
                    Flexible(
                      child: GlobalWhiteCard(
                        title: param.type == "challenge_hebdomadaire" ? evalCtrl.evaluationData.value?.title ?? AppStrings.challengeHebdomadaire : AppStrings.evaluationTitle,
                        onBackTap: evalCtrl.previousStep,
                        child: evalCtrl.isLoading.value
                            ? const Center(child: GlobalLoader())
                            : evalCtrl.isError.value
                                ? GlobalErrorHandler(
                                    onRetry: () {
                                      if (param.contentId != null){
                                        evalCtrl.fetchEvaluationData(param.contentId!, param.isLesonCompleted ?? false);
                                      }else if(param.type == "challenge_hebdomadaire"){
                                        evalCtrl.fetchChallengeHebdomadaireData();
                                      }
                                    },
                                  )
                                : evalCtrl.isEmptyData.value
                                    ? const SizedBox.shrink()
                                    : Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          if (param.type == "challenge_hebdomadaire")
                                            Container(
                                              margin: EdgeInsets.only(bottom: 1.h),
                                              padding: EdgeInsets.symmetric(
                                                  vertical: 0.1.h,
                                                  horizontal: 5.w),
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(1.h),
                                                border: Border.all(
                                                  color: AppColors.primary,
                                                  width: 1,
                                                )
                                              ),
                                              child: Text(
                                                evalCtrl.evaluationData.value?.matiere?.nameFr ?? '',
                                                style: TextStyle(
                                                  fontSize: 12.sp,
                                                  fontWeight: FontWeight.w400,
                                                  color: AppColors.secondary,
                                                ),
                                              ),
                                            ),
                                          LinearProgressIndicator(
                                              value: evalCtrl.totalSteps == 0 || 
                                                    evalCtrl.currentStep.value.isInfinite || 
                                                    evalCtrl.currentStep.value.isNaN
                                                  ? 0.0
                                                  : (evalCtrl.currentStep.value / evalCtrl.totalSteps).clamp(0.0, 1.0),
                                              backgroundColor: AppColors.primary.withOpacity(0.2),
                                              valueColor: const AlwaysStoppedAnimation(AppColors.primary),
),
                                          SizedBox(height: 3.h),
                                          Text(
                                            'Question ${evalCtrl.currentStep.value} sur ${evalCtrl.totalSteps}',
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.secondary,
                                            ),
                                          ),
                                          SizedBox(height: 1.h),
                                          Text(
                                            evalCtrl.currentQuestion,
                                            style: TextStyle(
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.black
                                                  .withOpacity(0.7),
                                            ),
                                          ),
                                          if (evalCtrl
                                              .currentQuestionDescription
                                              .isNotEmpty)
                                            Padding(
                                              padding:
                                                  EdgeInsets.only(top: 1.h),
                                              child: Text(
                                                evalCtrl
                                                    .currentQuestionDescription,
                                                style: TextStyle(
                                                  fontSize: 11.sp,
                                                  color: AppColors.black
                                                      .withOpacity(0.6),
                                                ),
                                              ),
                                            ),
                                          if (evalCtrl
                                              .currentQuestionImagePath !=
                                              null)
                                            Padding(
                                              padding:
                                                  EdgeInsets.symmetric(
                                                      vertical: 1.5.h),
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        1.h),
                                                child: Image.network(
                                                  evalCtrl
                                                      .currentQuestionImagePath!,
                                                  fit: BoxFit.cover,
                                                  height: 25.h,
                                                  width: double.infinity,
                                                  errorBuilder: (context,
                                                      error, stackTrace) {
                                                    return Container(
                                                      height: 25.h,
                                                      width: double.infinity,
                                                      decoration: BoxDecoration(
                                                        color: Colors.grey[200],
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                                    1.h),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          'Image unavailable',
                                                          style: TextStyle(
                                                            fontSize: 12.sp,
                                                            color: Colors
                                                                .grey[600],
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                  loadingBuilder: (context,
                                                      child, loadingProgress) {
                                                    if (loadingProgress ==
                                                        null) {
                                                      return child;
                                                    }
                                                    return Container(
                                                      height: 25.h,
                                                      width: double.infinity,
                                                      decoration: BoxDecoration(
                                                        color: Colors.grey[200],
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                                    1.h),
                                                      ),
                                                      child: const Center(
                                                        child:
                                                            CircularProgressIndicator(),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ),
                                          SizedBox(height: 2.h),
                                          Expanded(
                                            child:
                                                evalCtrl.currentQuestionType ==
                                                        'true_false'
                                                    ? _buildTrueFalseOptions(
                                                        evalCtrl)
                                                    : ListView(
                                                        children: evalCtrl
                                                            .currentChoices
                                                            .map((choice) =>
                                                                _buildOption(
                                                                    choice,
                                                                    evalCtrl))
                                                            .toList(),
                                                      ),
                                          ),
                                        ],
                                      ),
                      ),
                    ),
                    SizedBox(height: 2.h),
                    evalCtrl.isLoading.value ||
                            evalCtrl.isError.value ||
                            evalCtrl.isEmptyData.value
                        ? const SizedBox.shrink()
                        : Row(
                            children: [
                              Expanded(
                                child: FlatButtonGlobal(
                                  text: AppStrings.evaluationPrevious,
                                  onPressed: evalCtrl.previousStep,
                                  useGradient: true,
                                ),
                              ),
                              SizedBox(width: 4.w),
                              Expanded(
                                child: Obx(() {
                                  final isButtonEnabled =
                                      evalCtrl.isQuestionAnswered();
                                  final isLastQuestion =
                                      evalCtrl.currentStep.value ==
                                          evalCtrl.totalSteps;

                                  return GradientButton(
                                    text: isLastQuestion
                                        ? AppStrings.finish
                                        : '${AppStrings.next} →',
                                    onPressed: isButtonEnabled
                                        ? () {
                                            evalCtrl.revealAndGoNext();
                                          }
                                        : null,
                                  );
                                }),
                              ),
                            ],
                          ),
                    SizedBox(height: 7.h),
                  ],
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildTrueFalseOptions(EvaluationController evalCtrl) {
    final question = evalCtrl.currentQuestionObj?.question;
    final isRevealed = evalCtrl.isRevealedForCurrentQuestion;
    final selectedChoices = evalCtrl.getSelectedChoices();
    final isTrueSelected = selectedChoices.contains(1);
    final isFalseSelected = selectedChoices.contains(0);
    final isTrueCorrect = question?.isTrue ?? false;
    final isFalseCorrect = question?.isTrue ?? false;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildTrueFalseOption(
          evalCtrl: evalCtrl,
          text: "Vrai",
          icon: Icons.check,
          isSelected: isTrueSelected,
          isCorrect: isRevealed && isTrueCorrect,
          isIncorrect: isRevealed && isTrueSelected && !isTrueCorrect,
          choiceId: 1,
        ),
        _buildTrueFalseOption(
          evalCtrl: evalCtrl,
          text: "Faux",
          icon: Icons.close,
          isSelected: isFalseSelected,
          isCorrect: isRevealed && isFalseCorrect,
          isIncorrect: isRevealed && isFalseSelected && !isFalseCorrect,
          choiceId: 0,
        ),
      ],
    );
  }

  Widget _buildTrueFalseOption({
    required EvaluationController evalCtrl,
    required String text,
    required IconData icon,
    required bool isSelected,
    required bool isCorrect,
    required bool isIncorrect,
    required int choiceId,
  }) {
    final defaultColor = choiceId == 1 ? Colors.green : Colors.red;
    final bgColor = isCorrect
        ? Colors.green.withOpacity(0.2)
        : isIncorrect
            ? Colors.red.withOpacity(0.2)
            : isSelected
                ? Colors.grey.shade300
                : Colors.transparent;

    return InkWell(
      onTap: evalCtrl.isRevealedForCurrentQuestion
          ? null
          : () {
              final questionId = evalCtrl.currentQuestionObj?.questionId ?? -1;

              evalCtrl.selectedAnswers.update(
                questionId,
                (existingChoices) {
                  if (existingChoices.contains(choiceId)) {
                    return [];
                  } else {
                    return [choiceId];
                  }
                },
                ifAbsent: () => [choiceId],
              );
            },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 1.h, horizontal: 4.w),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(0.5.h),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: defaultColor,
              size: 18.sp,
            ),
            SizedBox(width: 2.w),
            Text(
              text,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
                color: defaultColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOption(Option choice, EvaluationController evalCtrl) {
    return Obx(() {
      final questionId = evalCtrl.currentQuestionObj?.questionId ?? -1;
      final isRevealed = evalCtrl.revealedQuestions[questionId] ?? false;
      final selectedChoices = evalCtrl.getSelectedChoices();
      final isSelected = selectedChoices.contains(choice.id);

      bool showBorderOnly = false;
      bool highlightGreen = false;
      bool highlightRed = false;

      if (!isRevealed) {
        if (isSelected) {
          showBorderOnly = true;
        }
      } else {
        if (choice.isCorrect == 1) {
          highlightGreen = true;
        } else if (isSelected && (choice.isCorrect == 0)) {
          highlightRed = true;
        }
      }

      Color borderColor = Colors.grey.shade400;
      Color bgColor = Colors.transparent;

      if (highlightGreen) {
        borderColor = Colors.green;
        bgColor = Colors.green.withOpacity(0.2);
      } else if (highlightRed) {
        borderColor = Colors.red;
        bgColor = Colors.red.withOpacity(0.2);
      } else if (showBorderOnly) {
        borderColor = AppColors.primary;
      }

      return Container(
        margin: EdgeInsets.symmetric(vertical: 1.h),
        padding: EdgeInsets.symmetric(vertical: 0.5.h),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(1.h),
          border: Border.all(
            color: borderColor,
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: isRevealed
              ? null
              : () {
                  evalCtrl.selectChoice(choice.id!);
                },
          child: Row(
            children: [
              Checkbox(
                value: isSelected,
                onChanged: isRevealed
                    ? null
                    : (bool? checked) {
                        if (checked == true) {
                          evalCtrl.selectChoice(choice.id!);
                        }
                      },
                checkColor: AppColors.white,
                activeColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(0.5.h),
                ),
                side: BorderSide(
                  color: borderColor,
                  width: 0.2.h,
                ),
              ),
              if (choice.icon != null) ...[
                choice.icon!.toIconText(),
                SizedBox(width: 2.w),
              ],
              Expanded(
                child: Text(
                  choice.name ?? '',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.black,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
