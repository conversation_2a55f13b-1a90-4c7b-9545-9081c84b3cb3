<div class="observation-form">
  <h2 mat-dialog-title>
    {{ isEditMode ? 'Modifier l\'observation' : 'Créer une nouvelle observation' }}
  </h2>

  <mat-dialog-content>
    @if (isLoading) {
      <div class="flex justify-center items-center py-8">
        <mat-spinner diameter="40"></mat-spinner>
      </div>
    } @else {
      <form [formGroup]="form" class="space-y-4">
        <!-- Label -->
        <div>
          <label class="block text-sm font-medium mb-1">Libellé *</label>
          <input
            type="text"
            formControlName="label"
            placeholder="Entrez le libellé de l'observation"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          @if (form.get('label')?.invalid && form.get('label')?.touched) {
            <span class="text-red-500 text-xs mt-1">
              Le libellé est obligatoire et doit contenir au moins 3 caractères
            </span>
          }
        </div>

        <!-- Category -->
        <div>
          <label class="block text-sm font-medium mb-1">Catégorie</label>
          <input
            type="text"
            formControlName="category"
            placeholder="Entrez la catégorie (optionnel)"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <!-- Visible To -->
        <div>
          <label class="block text-sm font-medium mb-1">Visible pour *</label>
          <select
            formControlName="visible_to"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="teacher">Enseignant</option>
            <option value="parent">Parent</option>
            <option value="both">Les deux</option>
          </select>
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium mb-1">Description</label>
          <textarea
            formControlName="description"
            placeholder="Entrez la description (optionnel)"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          ></textarea>
        </div>

        <!-- Active Toggle -->
        <div class="flex items-center gap-2">
          <mat-slide-toggle formControlName="active"></mat-slide-toggle>
          <label class="text-sm font-medium">Actif</label>
        </div>

        <!-- Trigger Type -->
        <div>
          <label for="trigger_type" class="block text-sm font-medium mb-1">Type de déclencheur *</label>
          <select
            id="trigger_type"
            formControlName="trigger_type"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="parent">Parent</option>
            <option value="teacher">Enseignant</option>
            <option value="simulated_grade_shutdown">Arrêt de note simulé</option>
          </select>
          @if (form.get('trigger_type')?.invalid && form.get('trigger_type')?.touched) {
            <span class="text-red-500 text-xs mt-1">
              Veuillez sélectionner un type de déclencheur
            </span>
          }
        </div>

        <!-- Test Selection -->
        <div>
          <label for="test_id" class="block text-sm font-medium mb-1">Lier un test de santé mentale *</label>
          <select
            id="test_id"
            formControlName="test_id"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">-- Sélectionnez un test --</option>
            @for (test of availableTests; track test.id) {
              <option [value]="test.id">{{ test.title }}</option>
            }
          </select>
          @if (form.get('test_id')?.invalid && form.get('test_id')?.touched) {
            <span class="text-red-500 text-xs mt-1">
              Veuillez sélectionner un test
            </span>
          }
        </div>
      </form>
    }
  </mat-dialog-content>

  <mat-dialog-actions align="end" class="mt-6">
    <button
      (click)="onCancel()"
      class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
      [disabled]="isSubmitting"
    >
      Annuler
    </button>
    <button
      (click)="onSubmit()"
      class="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 flex items-center gap-2"
      [disabled]="isSubmitting || form.invalid"
    >
      @if (isSubmitting) {
        <mat-spinner diameter="16"></mat-spinner>
      }
      {{ isEditMode ? 'Mettre à jour' : 'Créer' }}
    </button>
  </mat-dialog-actions>
</div>

