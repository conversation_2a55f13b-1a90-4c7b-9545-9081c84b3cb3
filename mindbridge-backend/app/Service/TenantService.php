<?php

namespace App\Service;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;

class TenantService
{
    /**
     * Switch DB connection to a tenant database name.
     *
     * @param string $tenantDbName  // e.g. 'mind_bridge' (the database name stored on the School model)
     * @throws \RuntimeException on configuration / connection errors
     */
    public static function switchToTenant(string $tenantDbName)
    {
        // pick a known-good template connection from config
        $template = config('database.connections.centrale')
                  ?: config('database.connections.mysql')
                  ?: null;

        if (!is_array($template) || empty($template['driver'])) {
            throw new \RuntimeException(
                "No valid template DB connection found. Add a 'centrale' or 'mysql' connection to config/database.php."
            );
        }

        $connectionName = 'tenant';

        $tenantConfig = $template;
        $tenantConfig['database'] = $tenantDbName;

        Config::set("database.connections.{$connectionName}", $tenantConfig);

        DB::purge($connectionName);
        DB::purge('centrale');

        DB::reconnect($connectionName);

        DB::setDefaultConnection($connectionName);

        if (class_exists(\Laravel\Sanctum\PersonalAccessToken::class)) {
            \Laravel\Sanctum\PersonalAccessToken::resolveConnection($connectionName);
        }

        try {
            DB::connection($connectionName)->getPdo();
        } catch (\Throwable $e) {
            Config::set("database.connections.{$connectionName}", null);
            throw new \RuntimeException(
                "Failed connecting to tenant database '{$tenantDbName}': " . $e->getMessage()
            );
        }

        return;
    }

    public static function switchToCentrale()
    {
        $connectionName = 'centrale';
        DB::purge('tenant');
        DB::purge($connectionName);
        $centrale = config("database.connections.{$connectionName}");
        if (!is_array($centrale) || empty($centrale['driver'])) {
            throw new \RuntimeException("Missing or invalid 'centrale' connection in config/database.php");
        }

        DB::reconnect($connectionName);
        DB::setDefaultConnection($connectionName);
        return;
    }
}
