<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\NiveauCentrale;
use App\Models\MatiereCentrale;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class CentraleNiveauMatierController extends Controller
{
    /**
     * List all niveaux, with optional relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listNiveaux(Request $request)
    {
        try {
            $matiereId = $request->query('matiere');
            $page = $request->query('page');
            $limit = $request->query('limit');
            $loadRelation = $request->query('loadRelation', false);

            $query = NiveauCentrale::query();

            if ($matiereId) {
                $query->whereHas('matieres', function ($q) use ($matiereId) {
                    $q->where('matieres.id', $matiereId);
                });
            }

            if ($loadRelation) {
                $query->with('matieres');
            }

            if ($limit && $page) {
                $total = $query->count();
                $niveaux = $query
                    ->orderBy('created_at', 'desc')
                    ->skip(($page - 1) * $limit)
                    ->take($limit)
                    ->get();

                $totalPages = ceil($total / $limit);

                return response()->json([
                    'data' => $niveaux,
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'per_page' => $limit,
                    'total_items' => $total,
                ], 200);
            } else {
                $niveaux = $query->orderBy('created_at', 'desc')->get();
                return response()->json([
                    'data' => $niveaux,
                    'total_items' => $niveaux->count(),
                ], 200);
            }
        } catch (Exception $e) {
            Log::error("Error in CentraleNiveauMatierController::indexNiveaux", ['exception' => $e]);
            return response()->json(['error' => 'An error occurred while fetching niveaux.'], 500);
        }
    }

    /**
     * List all matieres, with optional relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listMatieres(Request $request)
    {
        try {
            $niveauId = $request->query('niveau');
            $limit = $request->query('limit', 10);
            $page = $request->query('page', 1);
            $loadRelation = $request->query('loadRelation', false);
    
            $query = MatiereCentrale::query();
    
            if ($niveauId) {
                $query->whereHas('niveaux', function ($q) use ($niveauId) {
                    $q->where('niveaux.id', $niveauId);
                });
            }
            if ($loadRelation) {
                $query->with('niveaux');
            }
    
            $total = $query->count();
    
                $matieres = $query
                    ->orderBy('created_at', 'desc')
                    ->skip(($page - 1) * $limit)
                    ->take($limit)
                    ->get();
    
                $totalPages = ceil($total / $limit);
                return response()->json([
                    'data' => $matieres,
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'per_page' => $limit,
                    'total_items' => $total,
                ], 200);
        } catch (Exception $e) {
            Log::error("Error in CentraleNiveauMatierController::listMatieres", ['exception' => $e]);
            return response()->json(['error' => 'An error occurred while fetching matieres.'], 500);
        }
    }

    /**
     * List niveaux for a specific matiere, with optional relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $matiereId
     * @return \Illuminate\Http\JsonResponse
     */
    public function niveauxByMatiere($matiereId)
    {
        try {
            $matiere = MatiereCentrale::with('niveaux')->findOrFail($matiereId);
            return response()->json($matiere, 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Matiere not found.'], 404);
        } catch (Exception $e) {
            Log::error("Error in CentraleNiveauMatierController::niveauxByMatiere", ['exception' => $e]);
            return response()->json(['error' => 'An error occurred while fetching niveaux for the matiere.'], 500);
        }
    }


    /**
     * List matieres for a specific niveau, with optional relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $niveauId
     * @return \Illuminate\Http\JsonResponse
     */
    public function matieresByNiveau(Request $request, $niveauId)
    {
        try {
            $niveau = NiveauCentrale::with('matieres')->findOrFail($niveauId);
            return response()->json( $niveau, 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Niveau not found.'], 404);
        } catch (Exception $e) {
            Log::error("Error in CentraleNiveauMatierController::matieresByNiveau", ['exception' => $e]);
            return response()->json(['error' => 'An error occurred while fetching matieres for the niveau.'], 500);
        }
    }
    public function getSubjects(Request $request)
    {
        try {
            $subjects = MatiereCentrale::with("niveaux")->select('id', 'name_fr', 'name_ar')
                ->get();
    
            return response()->json(['data' => $subjects], 200);
        } catch (Exception $e) {
            Log::error('Error in CentraleNiveauMatierController::getSubjectsWithLevels', [
                'exception' => $e->getMessage(),
            ]);
    
            return response()->json(['error' => 'An error occurred.'], 500);
        }
    } 
    
    public function getLevels(Request $request)
    {
        try {
            $levels = NiveauCentrale::all();
    
            return response()->json(['data' =>  $levels], 200);
        } catch (Exception $e) {
            Log::error('Error in CentraleNiveauMatierController::getSubjectsWithLevels', [
                'exception' => $e->getMessage(),
            ]);
    
            return response()->json(['error' => 'An error occurred.'], 500);
        }
    }       
}
