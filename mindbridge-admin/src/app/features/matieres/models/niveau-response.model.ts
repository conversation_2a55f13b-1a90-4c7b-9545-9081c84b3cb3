import { Pivot } from './matiere-response.model';

export interface MatieresByNiveauResponseWrapper {
  id: number;
  name: string;
  color: string;
  description: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
  matieres: MatieresByNiveauResponse[];
}

export interface MatieresByNiveauResponse {
  id: number;
  name_fr: string;
  name_ar: string;
  description: any;
  image_url: string;
  gradient_background: string;
  gradient_border: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
  pivot: Pivot;
}
