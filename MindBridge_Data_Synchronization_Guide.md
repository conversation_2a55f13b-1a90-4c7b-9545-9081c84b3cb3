# MindBridge Data Synchronization Guide

## Overview
This guide explains how to synchronize existing data from the MindBridge database with the central database. This is necessary when the MindBridge application was previously connected to another system and now needs to be integrated with the central database architecture.

## Problem Statement
The MindBridge application contains existing data (users, tests, questions, content, etc.) that references IDs from the central database (niveaux, matieres, users). However, these referenced records may not exist in the current central database, causing foreign key constraint violations and data inconsistencies.

## Solution: MindBridgeDataSyncSeeder
The `MindBridgeDataSyncSeeder` analyzes the existing MindBridge database and creates the necessary records in the central database to maintain referential integrity.

## What Gets Synchronized

### 1. Educational Levels (Niveaux)
- Extracts all `niveau_id` references from MindBridge tables
- Creates missing niveau records in the central database
- Assigns appropriate names, colors, and descriptions

### 2. Subjects (Matieres)
- Extracts all `matiere_id` references from MindBridge tables
- Creates missing matiere records in the central database
- Generates French/Arabic names, gradients, and styling

### 3. Users
- Extracts all `user_id` references from MindBridge tables
- Creates missing user records in the central database
- Links users to the MindBridge school
- Preserves student information from etudiants table

### 4. School Configuration
- Ensures MindBridge school exists in central database
- Updates school references in MindBridge data

### 5. Relationships
- Creates niveau-matiere relationships based on actual usage
- Links users to schools through pivot table
- Updates foreign key references

## Usage Methods

### Method 1: Using Artisan Command (Recommended)

#### Dry Run (Check what would be synchronized)
```bash
php artisan mindbridge:sync-data --dry-run
```

#### Run Synchronization with Confirmation
```bash
php artisan mindbridge:sync-data
```

#### Force Synchronization (No Confirmation)
```bash
php artisan mindbridge:sync-data --force
```

### Method 2: Using Database Seeder

#### Set Environment Configuration
Add to your `.env` file:
```env
SEEDER_TYPE=sync
```

#### Run the Seeder
```bash
php artisan db:seed
```

#### Or Run Specific Seeder
```bash
php artisan db:seed --class=MindBridgeDataSyncSeeder
```

## Pre-Synchronization Checklist

### 1. Database Backup
```bash
# Backup both databases before synchronization
mysqldump -u root -p centrale > centrale_backup.sql
mysqldump -u root -p mind_bridge > mind_bridge_backup.sql
```

### 2. Verify Database Connections
Ensure both database connections are working:
```bash
php artisan tinker
>>> DB::connection('centrale')->select('SELECT 1');
>>> DB::connection('mind_bridge')->select('SELECT 1');
```

### 3. Check Current Data
Run a dry run to see what will be synchronized:
```bash
php artisan mindbridge:sync-data --dry-run
```

## Post-Synchronization Verification

### 1. Check Central Database
Verify that new records were created:
```sql
-- Check niveaux
SELECT * FROM centrale.niveaux ORDER BY id;

-- Check matieres
SELECT * FROM centrale.matieres ORDER BY id;

-- Check users
SELECT * FROM centrale.users WHERE is_mind_bridge_user = 1;

-- Check relationships
SELECT * FROM centrale.niveaux_matiere;
```

### 2. Test MindBridge Functionality
- Login to MindBridge admin panel
- Verify student data displays correctly
- Test creating new tests and content
- Check that foreign key relationships work

### 3. Verify Data Integrity
```bash
php artisan tinker
>>> App\Models\MindBridge\MindBridgeEtudiant::with('niveau', 'users', 'school')->first();
>>> App\Models\MindBridge\Test::with('matiere', 'niveau', 'creator')->first();
```

## Troubleshooting

### Common Issues

#### 1. Foreign Key Constraint Errors
**Problem**: References to non-existent central database records
**Solution**: Run the synchronization seeder to create missing records

#### 2. Duplicate Key Errors
**Problem**: Trying to create records with existing IDs
**Solution**: The seeder checks for existing records before creating new ones

#### 3. Missing School Reference
**Problem**: MindBridge school not found in central database
**Solution**: The seeder automatically creates the MindBridge school entry

### Debug Mode
Enable detailed logging by setting:
```env
LOG_LEVEL=debug
```

Check logs for detailed synchronization information:
```bash
tail -f storage/logs/laravel.log
```

## Data Mapping Details

### Niveau Mapping
```php
// Default niveau names generated based on ID
1 => 'Seconde'
2 => 'Première'  
3 => 'Terminale'
4 => 'Licence 1'
// etc.
```

### Matiere Mapping
```php
// Default matiere names generated based on ID
1 => 'Français'
2 => 'Mathématiques'
3 => 'Sciences Naturelles'
4 => 'Finance'
// etc.
```

### User Mapping
- Students: Created from `etudiants` table data
- Admins: Created with default information for test creators
- All users marked with `is_mind_bridge_user = true`

## Safety Features

### 1. Non-Destructive
- Only creates new records, never modifies existing ones
- Uses `updateOrCreate` and existence checks
- Preserves existing data integrity

### 2. Idempotent
- Can be run multiple times safely
- Skips records that already exist
- No duplicate data creation

### 3. Rollback Capability
- Database backups recommended before running
- All changes are standard database inserts
- Can be manually reversed if needed

## Best Practices

### 1. Development Environment First
Always test synchronization in development before production:
```bash
# Copy production data to development
# Run synchronization in development
# Verify results
# Then run in production
```

### 2. Maintenance Window
Schedule synchronization during low-usage periods to avoid conflicts.

### 3. Monitoring
Monitor application logs during and after synchronization for any issues.

### 4. Documentation
Keep track of what was synchronized and when for future reference.

## Support

If you encounter issues during synchronization:

1. Check the Laravel logs for detailed error messages
2. Verify database connections and permissions
3. Ensure all required tables exist in both databases
4. Run the dry-run command to identify potential issues
5. Contact the development team with specific error messages

This synchronization process ensures that your existing MindBridge data is properly integrated with the central database architecture while maintaining data integrity and application functionality.
