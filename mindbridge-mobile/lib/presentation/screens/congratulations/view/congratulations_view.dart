import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/app/routes.dart';
import 'package:mindbridge/core/constants/app_strings.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import '../../../../core/utilities/app_images.dart';
import '../../../widgets/global_confetti.dart';
import '../../../widgets/global_spots.dart';
import '../../../widgets/gradient_button.dart';

class CongratulationsView extends StatelessWidget {
  const CongratulationsView({super.key});

  @override
  Widget build(BuildContext context) {
    final feedbackMessage = Get.arguments?['feedback_message'] ??
        AppStrings.congratulationsSubtitle;

    return Scaffold(
      backgroundColor: AppColors.white,
      body: Stack(
        clipBehavior: Clip.none,
        children: [
          const GlobalSpots(),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 5.w),
            child: Column(
              children: [
                Expanded(
                  child: Center(
                    child: SingleChildScrollView(
                      child: IntrinsicHeight(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              AppImages.bigOwl,
                              height: 30.h,
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              AppStrings.congratulationsTitle,
                              style: TextStyle(
                                fontSize: 20.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColors.black,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 2.h),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 4.w),
                              child: Text(
                                feedbackMessage,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.black.withOpacity(0.7),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            SizedBox(height: 4.h),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GradientButton(
                      text: AppStrings.start,
                      onPressed: () {
                        Get.offAllNamed(Routes.btmNavigationBar);
                      },
                    ),
                    SizedBox(height: 4.h),
                  ],
                ),
              ],
            ),
          ),
          const GlobalConfetti(
            colors: [
              Colors.blue,
              Colors.pink,
              Colors.purple,
              Colors.green,
              Colors.white,
            ],
          ),
        ],
      ),
    );
  }
}
