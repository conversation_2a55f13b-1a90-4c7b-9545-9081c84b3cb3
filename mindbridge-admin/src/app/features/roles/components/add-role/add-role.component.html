<div class="db dimensions_container">
  <div class="df bg-color-head">
    <h2>{{ 'Ajouter Role' }}</h2>
    <mat-icon (click)="close()">close</mat-icon>
  </div>
  <form class="filters flex-column" [formGroup]="form">
    <div class="w100 df">
      <div class="search flex-column">
        <label for="">Nom * </label>
        <input type="text">
      </div>
    </div>
    <div class="w100 flex-column">
      @if (false) {
        <p class="messege_error">Champ invalide.</p>
      }
      <label>Permissions * </label>
      <mat-select multiple>
        <mat-select-trigger>
          <!-- <span>---</span> -->
          <!-- <span>{{ (form.get('permissions')?.value[0]) + (form.get('permissions')?.value )}}</span> -->
        </mat-select-trigger>
        <!-- <mat-optgroup> -->
        <!-- *ngFor="let module of modules" [label]="module.name" -->
        <!-- <mat-option>---</mat-option> -->
        <!-- <mat-option *ngFor="let permission of module.permissions" [value]="permission">
        {{permission}}
      </mat-option> -->
    <!-- </mat-optgroup> -->
  </mat-select>
</div>
<div class="between_item">
  <button type="reset" class="bg-color-btn" (click)="close()">
    Cancel
  </button>
  <button type="submit" class="bg-color-btn bg-active-btn">
    {{ 'Enregistrer' }}
  </button>
</div>
</form>
</div>