import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:mindbridge/core/utilities/extensions.dart';
import 'package:mindbridge/presentation/screens/home/<USER>/evaluation_param.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/routes.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/models/category.dart';
import '../../../widgets/global_dialog.dart';

class SwipeableCard extends StatelessWidget {
  final Child cardData;
  final bool isHighlighted;

  const SwipeableCard({
    super.key,
    required this.cardData,
    required this.isHighlighted,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 1.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: cardData.gradientBackground.toGradient(),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: isHighlighted
            ? GradientBoxBorder(
                gradient: LinearGradient(
                  colors: cardData.gradientBorder.toGradient(),
                ),
                width: 1,
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.w),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  onTap: () {
                    GlobalInfoDialog.showInfoDialog(
                      imagePath: cardData.imageUrl ?? "",
                      description:
                          cardData.description ?? "No description available.",
                    );
                  },
                  child: Icon(
                    Icons.info,
                    size: 16.sp,
                    color: AppColors.black.withOpacity(0.7),
                  ),
                ),
                SizedBox(
                  width: 1.w,
                ),
                Flexible(
                  child: Text(
                    cardData.name ?? "Untitled",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 4.h),
          (cardData.imageUrl ?? "").toImage(
            height: 15.h,
          ),
          ElevatedButton(
            onPressed: () {
              if (cardData.enabled == true) {
                final actionType = cardData.actionType;
                if (actionType == "test") {
                  if (cardData.code == 'challenge_hebdomadaire'){
                    Get.toNamed(Routes.evaluation, arguments: EvaluationParam(type: cardData.code));
                  }
                } else if (actionType == "lecon") {
                  if (cardData.code == 'recommandation_mindbridge') {
                    Get.toNamed(Routes.recommandation);
                    return;
                  }
                  Get.toNamed(
                    Routes.selectSubject,
                    arguments: cardData.id,
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: Text(
                    cardData.buttonText ?? "Start",
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(width: 2.w),
                Icon(
                  Icons.arrow_forward_rounded,
                  size: 16.sp,
                  color: AppColors.primary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
