import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';

import '../../../core/constants/api_routes.dart';
import '../../../core/models/user.dart';
import '../../app/routes.dart';
import '../providers/local/secure_storage_service.dart';
import '../providers/remote/dio_client.dart';

class AuthRepository {
  final DioClient _dioClient;
  final SecureStorageService _secureStorage;

  AuthRepository({
    required DioClient dioClient,
    required SecureStorageService secureStorage,
  })  : _dioClient = dioClient,
        _secureStorage = secureStorage;
  Future<Map<String, dynamic>> login(String email, String password) async {
    final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;

    String? fcmToken;

    try {
      fcmToken = await firebaseMessaging.getToken();
    } catch (e) {
      // Handle FCM token error (e.g., iOS simulator)
      if (kDebugMode) {
        print("FCM token error (likely iOS simulator): $e");
      }
    }

    // If FCM token is null (iOS simulator or error), use a fake token for development
    if (fcmToken == null && kDebugMode) {
      fcmToken = 'ios_simulator_fake_token_${DateTime.now().millisecondsSinceEpoch}';
      if (kDebugMode) {
        print("Using fake FCM token for iOS simulator: $fcmToken");
      }
    }

    try {
      final response = await _dioClient.dio.post(
        ApiRoutes.login,
        data: {
          'email': email,
          'password': password,
          'fcm_token': fcmToken
        },
      );

      if (response.statusCode == 200 && response.data != null) {
        final user = User.fromJson(response.data);

        await _secureStorage.write('auth_token', user.token ?? '');
        await _secureStorage.write('test_profiling_completed',
            user.etudiant?.testProfilingCompleted ?? '');
        await _secureStorage.write('user_id', user.user?.id.toString() ?? '0');

        return {
          'success': true,
          'user': user,
        };
      }

      return {'success': false, 'message': 'Login failed'};
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data['message'] ?? 'Something went wrong',
      };
    }
  }

  Future<User?> getStoredUser() async {
    try {
      final token = await _secureStorage.read('auth_token');
      final testProfilingCompleted =
          await _secureStorage.read('test_profiling_completed');
      final userId = await _secureStorage.read('user_id');
      if (token != null && userId != null) {
        return User(
          token: token,
          user: UserClass(id: int.tryParse(userId)),
          etudiant: Etudiant(
            testProfilingCompleted: testProfilingCompleted,
          ),
        );
      }
    } catch (e) {
      print("Error retrieving stored user: $e");
    }
    return null;
  }

  Future<void> logout() async {
    await _secureStorage.clear();
    Get.offAllNamed(Routes.login);
  }
}
