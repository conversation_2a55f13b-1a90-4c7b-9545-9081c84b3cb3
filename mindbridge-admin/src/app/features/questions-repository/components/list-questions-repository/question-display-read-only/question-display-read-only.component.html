<div class="mi-auto flex w-full place-content-between gap-4">
  <div class="flex w-full flex-col rounded-lg bg-white px-4 py-4">
    <div class="flex flex-row">
      @if (question.categories.length > 0) {
        @for (
          category of question.categories | slice: 0 : 1;
          track category.id
        ) {
          <div class="text-lg font-semibold">
            {{ category.name }}
          </div>
        }
        @if (question.categories.length > 1) {
          <p
            class="nbr-niv m-0 w-fit rounded-lg px-1.5 py-0.5 text-sm font-medium"
            [matTooltip]="
              getTooltipMessageCategories(question.categories | slice: 1)
            "
            matTooltipClass=""
          >
            +{{ question.categories.length - 1 }}
          </p>
        }
      }

      @if (question.matieres.length > 0) {
        <span class="text-lg text-gray-300">|</span>
        @for (matiere of question.matieres | slice: 0 : 1; track matiere.id) {
          <div class="text-lg">
            {{ matiere.name_fr }}
          </div>
        }
        @if (question.matieres.length > 1) {
          <p
            class="nbr-niv m-0 w-fit rounded-lg px-1.5 py-0.5 text-sm font-medium"
            [matTooltip]="
              getTooltipMessageMatieres(question.matieres | slice: 1)
            "
            matTooltipClass=""
          >
            +{{ question.matieres.length - 1 }}
          </p>
        }
      }

      @if (question.niveaux.length > 0) {
        <span class="text-lg text-gray-300">|</span>
        @if (question.niveaux.length <= 2) {
          @for (niv of question.niveaux; track niv.id) {
            <p
              class="m-0 w-fit rounded-lg px-2.5 py-0.5 text-xs font-medium"
              [ngStyle]="{
                color: niv.color,
                'background-color': niv.background,
              }"
            >
              {{ niv.name }}
            </p>
          }
        } @else {
          @for (niv of question.niveaux | slice: 0 : 2; track niv.id) {
            <p
              class="m-0 w-fit rounded-lg px-2.5 py-0.5 text-xs font-medium"
              [ngStyle]="{
                color: niv.color,
                'background-color': niv.background,
              }"
            >
              {{ niv.name }}
            </p>
          }
          <p
            class="nbr-niv m-0 w-fit rounded-lg px-1.5 py-0.5 text-xs font-medium"
            [matTooltip]="getTooltipMessageNiveaux(question.niveaux | slice: 2)"
          >
            +{{ question.niveaux.length - 3 }}
          </p>
        }
      }
    </div>

    <div class="flex flex-row gap-4">
      <div class="flex w-3/4 flex-col gap-2">
        <div class="flex items-center gap-2">
          <span class="border_b span_questions box-border w-full">
            {{ question.content }}
          </span>
          @if (question.image_path) {
            <span class="text-xl" title="This question has an image">📷</span>
          }
        </div>
      </div>
      <div class="flex w-1/4 flex-col gap-1" *ngIf="false">
        <label class="font-medium" for="input6">Question form</label>
        <select
          class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
          id="input6"
        >
          <option value="one">Un choix</option>
          <option value="many">Multiple choix</option>
        </select>
      </div>
    </div>
    @if (question.description) {
      <div class="description_questions box-border w-48 w-full py-1.5">
        {{ question.description }}
      </div>
    }
    @if (question.image_path) {
      <img [src]="question.image_path" width="300" alt="image du question" />
    }

    @if (question.answer !== null) {
      <mat-radio-group [value]="question.answer" disabled>
        <mat-radio-button class="green-radio-button" [value]="true">
          Vrai
        </mat-radio-button>
        <mat-radio-button class="red-radio-button" [value]="false">
          Faux
        </mat-radio-button>
      </mat-radio-group>
    }
    <ul class="list-disc space-y-4 pl-4">
      @for (option of question.options; track option.id) {
        <li>
          <div class="flex items-center">
            {{ option.name }}

            @if (option.isCorrect) {
              <span class="df is_correct_response ml-1">
                <img src="assets/icones/correct-response.png" alt="" />
                <span>Réponse correcte</span>
              </span>
            }
          </div>
        </li>
      }
    </ul>
  </div>
</div>
