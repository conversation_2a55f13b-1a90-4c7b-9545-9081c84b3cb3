import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../app/theme/colors.dart';

class FlatButtonGlobal extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool useGradient;
  const FlatButtonGlobal({
    super.key,
    required this.text,
    required this.onPressed,
    this.useGradient = false,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(1.5.h),
        ),
        backgroundColor: Colors.transparent,
        elevation: 2,
        shadowColor: useGradient ? Colors.grey.shade300 : null,
      ),
      child: Container(
        alignment: Alignment.center,
        height: 6.h,
        decoration: BoxDecoration(
          gradient: useGradient ? AppColors.flatButtongradient : null,
          color: useGradient ? null : AppColors.white,
          borderRadius: BorderRadius.circular(1.5.h),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
            fontSize: 12.sp,
          ),
        ),
      ),
    );
  }
}
