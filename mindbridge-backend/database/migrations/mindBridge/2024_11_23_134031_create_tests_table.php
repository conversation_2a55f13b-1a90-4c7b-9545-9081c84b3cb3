<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tests', function (Blueprint $table) {
            $table->id();
    
            // Colonnes principales
            $table->string('title');
            $table->string('description')->nullable();
            $table->string('target')->nullable();
            $table->enum('type', ['test_profiling', 'test_content', 'exam_simulation', 'culture_general'])->nullable();
            $table->integer('timer')->nullable();
            $table->date('challenge_date_start')->nullable();
            $table->date('challenge_date_end')->nullable();
            $table->enum('difficulty_level', ['tres_facile', 'facile', 'intermediaire', 'difficile', 'tres_difficile'])->nullable();

            // Relations avec les autres tables
            $table->foreignId('matiere_id')
                ->nullable()
                ->constrained(config('database.connections.centrale.database') . '.matieres')
                ->cascadeOnDelete();
    
            $table->foreignId('category_id')
                ->constrained('categories')
                ->cascadeOnDelete();
    
            $table->foreignId('created_by')
                ->nullable()
                ->constrained(config('database.connections.centrale.database') . '.users')
                ->nullOnDelete();
    
            $table->foreignId('niveau_id')
                ->nullable()
                ->constrained(config('database.connections.centrale.database') . '.niveaux')
                ->nullOnDelete();
    
            $table->foreignId('content_id')
                ->nullable()
                ->constrained('contents')
                ->cascadeOnDelete();
    
            $table->timestamps();
        });
    }
    

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tests');
    }
};
