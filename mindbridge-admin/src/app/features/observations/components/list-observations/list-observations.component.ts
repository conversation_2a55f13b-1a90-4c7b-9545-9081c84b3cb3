import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ObservationsService, Observation } from '../../services/observations.service';
import { CommonService } from '../../../shared/common.service';
import { ObservationFormComponent } from '../observation-form/observation-form.component';

@Component({
  selector: 'app-list-observations',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTableModule,
    MatPaginatorModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatDialogModule,
    MatTooltipModule,
  ],
  templateUrl: './list-observations.component.html',
  styleUrl: './list-observations.component.scss',
})
export class ListObservationsComponent implements OnInit {
  private observationsService = inject(ObservationsService);
  private commonService = inject(CommonService);
  private dialog = inject(MatDialog);
  private fb = inject(FormBuilder);

  observations = signal<Observation[]>([]);
  isLoading = signal(false);
  currentPage = signal(1);
  perPage = signal(10);
  totalPages = signal(1);
  totalItems = signal(0);

  Math = Math; // Expose Math to template

  displayedColumns: string[] = ['label', 'category', 'visible_to', 'active', 'actions'];

  filterForm: FormGroup;

  constructor() {
    this.filterForm = this.fb.group({
      search: [''],
      category: [''],
      visible_to: [''],
      active: [''],
    });
  }

  ngOnInit(): void {
    this.loadObservations();
  }

  loadObservations(): void {
    this.isLoading.set(true);
    const filters = this.filterForm.value;

    this.observationsService
      .getObservations(
        this.currentPage(),
        this.perPage(),
        filters.search || undefined,
        filters.category || undefined,
        filters.visible_to || undefined,
        filters.active !== '' ? filters.active === 'true' : undefined
      )
      .subscribe({
        next: (response) => {
          this.observations.set(response.data);
          this.currentPage.set(response.current_page);
          this.perPage.set(response.per_page);
          this.totalPages.set(response.total_pages);
          this.totalItems.set(response.total_items);
          this.isLoading.set(false);
        },
        error: () => {
          this.commonService.showToast('Error loading observations', 'ERROR');
          this.isLoading.set(false);
        },
      });
  }

  onFilterChange(): void {
    this.currentPage.set(1);
    this.loadObservations();
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.currentPage.set(1);
    this.loadObservations();
  }

  openFormDialog(observation?: Observation): void {
    const dialogRef = this.dialog.open(ObservationFormComponent, {
      width: '600px',
      data: observation || null,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadObservations();
      }
    });
  }

  deleteObservation(id: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette observation ?')) {
      this.observationsService.deleteObservation(id).subscribe({
        next: () => {
          this.commonService.showToast('Observation supprimée avec succès', 'SUCCESS');
          this.loadObservations();
        },
        error: () => {
          this.commonService.showToast('Erreur lors de la suppression de l\'observation', 'ERROR');
        },
      });
    }
  }

  viewMappedTest(id: number): void {
    this.observationsService.getMappedTest(id).subscribe({
      next: (response) => {
        const testName = response.data?.title || 'Aucun test lié';
        this.commonService.showToast(
          `Test: ${testName}`,
          'INFO'
        );
      },
      error: () => {
        this.commonService.showToast('Erreur lors du chargement du test lié', 'ERROR');
      },
    });
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
      this.loadObservations();
    }
  }

  getVisibleToLabel(value: string): string {
    const labels: { [key: string]: string } = {
      teacher: 'Enseignant',
      parent: 'Parent',
      both: 'Les deux',
    };
    return labels[value] || value;
  }
}

