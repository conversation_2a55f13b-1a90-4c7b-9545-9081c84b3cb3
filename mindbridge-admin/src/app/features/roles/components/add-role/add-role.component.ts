import { Component, inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSelect, MatSelectTrigger } from '@angular/material/select';

import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-add-role',
    templateUrl: './add-role.component.html',
    styleUrl: './add-role.component.scss',
    imports: [MatIcon, ReactiveFormsModule, MatSelect, MatSelectTrigger]
})
export class AddRoleComponent {
  private dialogRef = inject<MatDialogRef<AddRoleComponent>>(MatDialogRef);
  data = inject(MAT_DIALOG_DATA);

  form: FormGroup = new FormGroup({});

  close() {
    this.dialogRef.close();
  }
}
