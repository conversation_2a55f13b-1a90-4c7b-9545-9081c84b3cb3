import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/utilities/app_images.dart';
import '../../../../core/utilities/loading_utils.dart';
import '../../../../core/utilities/validators.dart';
import '../../../widgets/global_field.dart';
import '../../../widgets/global_spots.dart';
import '../../../widgets/gradient_button.dart';
import '../controller/login_controller.dart';

class LoginView extends StatelessWidget {
  final LoginController loginController = Get.find<LoginController>();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Stack(
        children: [
          const GlobalSpots(),
          Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 5.w),
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Card(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(4.w),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.asset(
                              AppImages.logo,
                              height: 10.h,
                            ),
                            SizedBox(height: 2.h),
                            Text(
                              AppStrings.loginTitle,
                              style: TextStyle(
                                fontSize: 20.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColors.black,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 1.h),
                            Text(
                              AppStrings.loginSubtitle,
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: AppColors.black.withOpacity(0.7),
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 3.h),
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                'Email',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.black,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(height: 1.h),
                            GlobalTextField(
                              controller: emailController,
                              hintText: AppStrings.emailHint,
                            ),
                            SizedBox(height: 2.h),
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                'Password',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.black,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(height: 1.h),
                            GlobalTextField(
                              controller: passwordController,
                              hintText: AppStrings.passwordHint,
                              obscureText: true,
                            ),
                            SizedBox(height: 2.h),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Row(
                                    children: [
                                      Obx(() {
                                        return Checkbox(
                                          value: loginController
                                              .isRememberMeChecked.value,
                                          onChanged: (value) {
                                            loginController.toggleRememberMe();
                                          },
                                          activeColor: AppColors.primary,
                                          checkColor: AppColors.white,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(0.5.h),
                                          ),
                                          side: BorderSide(
                                            color: loginController
                                                    .isRememberMeChecked.value
                                                ? AppColors.primary
                                                : Colors.grey.shade400,
                                            width: 0.2.h,
                                          ),
                                        );
                                      }),
                                      const Flexible(
                                        child: Text(
                                          AppStrings.rememberMe,
                                          style:
                                              TextStyle(color: AppColors.black),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  width: 2.w,
                                ),
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      // Add forgot password logic
                                    },
                                    child: const Text(
                                      AppStrings.forgotPassword,
                                      style: TextStyle(
                                        color: AppColors.primary,
                                      ),
                                      textAlign: TextAlign.end,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 2.h),
                            GradientButton(
                              text: AppStrings.signIn,
                              onPressed: () {
                                final email = emailController.text.trim();
                                final password = passwordController.text.trim();


                                final emailError =
                                    Validators.validateEmail(email);
                                final passwordError =
                                    Validators.validatePassword(password);

                                if (emailError != null) {
                                  LoadingUtils.showError(emailError);
                                  return;
                                }

print("Email: $email");

                                
                                if (passwordError != null) {
                                  LoadingUtils.showError(passwordError);
                                  return;
                                }

                                

                                loginController.login(email, password);
                              },
                            ),
                            SizedBox(height: 2.h),
                            // Row(
                            //   mainAxisAlignment: MainAxisAlignment.center,
                            //   children: [
                            //     const Flexible(
                            //       child: Text(
                            //         AppStrings.noAccount,
                            //         style: TextStyle(color: AppColors.black),
                            //       ),
                            //     ),
                            //     SizedBox(
                            //       width: 0.5.w,
                            //     ),
                            //     Flexible(
                            //       child: InkWell(
                            //         onTap: () {
                            //           // Add sign-up logic
                            //         },
                            //         child: const Text(
                            //           AppStrings.signUp,
                            //           style: TextStyle(
                            //             color: AppColors.primary,
                            //             fontWeight: FontWeight.bold,
                            //           ),
                            //         ),
                            //       ),
                            //     ),
                            //   ],
                            // ),
                            SizedBox(
                              height: 4.h,
                            )
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: -7.h,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: Image.asset(
                          AppImages.litOwl,
                          height: 12.h,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
