<div class="w-full bg-gray-50 p-6">
  <!-- Header -->
  <div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-900">Historique des Tests Mentaux</h2>
    <p class="mt-1 text-sm text-gray-600">
      Total: {{ testHistory().length }} test(s) complété(s)
    </p>
  </div>

  <!-- Main Container -->
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
    <!-- Tests List -->
    <div class="lg:col-span-1">
      <div class="rounded-lg border border-gray-200 bg-white shadow-sm">
        <div class="border-b border-gray-200 px-4 py-3">
          <h3 class="font-semibold text-gray-900">Tests Complétés</h3>
        </div>
        <div class="max-h-96 overflow-y-auto">
          @if (testHistory().length === 0) {
            <div class="px-4 py-8 text-center text-gray-500">
              <p>Aucun test complété</p>
            </div>
          } @else {
            @for (test of testHistory(); track test.id) {
              <button
                (click)="selectTest(test)"
                [class.bg-blue-50]="selectedTest()?.id === test.id"
                class="w-full border-b border-gray-100 px-4 py-3 text-left transition-colors hover:bg-gray-50"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <p class="font-medium text-gray-900">{{ test.test.title }}</p>
                    <p class="mt-1 text-xs text-gray-500">
                      {{ formatDate(test.completed_at) }}
                    </p>
                  </div>
                  <div class="ml-2 flex flex-col items-end">
                    <span
                      class="inline-block rounded-full px-2 py-1 text-xs font-semibold"
                      [style.backgroundColor]="getStatusColor(test.status) + '20'"
                      [style.color]="getStatusColor(test.status)"
                    >
                      {{ getStatusLabel(test.status) }}
                    </span>


                    <div class="flex items-center gap-2">


                      <p class="mt-1 text-sm font-bold text-gray-900">
                      Score:
                    </p>
                       <p class="mt-1 text-sm font-bold text-gray-900">
                      {{ test.score }}/{{ test.scoring.total_questions }}
                    </p>
                    </div>
                   
                  </div>
                </div>
              </button>
            }
          }
        </div>
      </div>
    </div>

    <!-- Test Details -->
    <div class="lg:col-span-2">
      @if (selectedTest()) {
        <div class="rounded-lg border border-gray-200 bg-white shadow-sm">
          <!-- Tabs -->
          <div class="border-b border-gray-200">
            <div class="flex">
              <button
                (click)="setActiveTab('details')"
                [class.border-b-2]="activeTab() === 'details'"
                [class.border-blue-500]="activeTab() === 'details'"
                [class.text-blue-600]="activeTab() === 'details'"
                class="flex-1 px-4 py-3 text-center font-medium text-gray-600 transition-colors hover:text-gray-900"
              >
                Détails du Test
              </button>
              <button
                (click)="setActiveTab('scoring')"
                [class.border-b-2]="activeTab() === 'scoring'"
                [class.border-blue-500]="activeTab() === 'scoring'"
                [class.text-blue-600]="activeTab() === 'scoring'"
                class="flex-1 px-4 py-3 text-center font-medium text-gray-600 transition-colors hover:text-gray-900"
              >
                Scoring
              </button>
              <button
                (click)="setActiveTab('observations')"
                [class.border-b-2]="activeTab() === 'observations'"
                [class.border-blue-500]="activeTab() === 'observations'"
                [class.text-blue-600]="activeTab() === 'observations'"
                class="flex-1 px-4 py-3 text-center font-medium text-gray-600 transition-colors hover:text-gray-900"
              >
                Observations
              </button>
            </div>
          </div>

          <!-- Tab Content -->
          <div class="p-6">
            <!-- Details Tab -->
            @if (activeTab() === 'details') {
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Titre du Test</label>
                  <p class="mt-1 text-gray-900">{{ selectedTest()!.test.title }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Description</label>
                  <p class="mt-1 text-gray-900">{{ selectedTest()!.test.description || '-' }}</p>
                </div>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Catégorie</label>
                    <p class="mt-1 text-gray-900">{{ selectedTest()!.test.category.name }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Niveau de Difficulté</label>
                    <p class="mt-1 text-gray-900">{{ selectedTest()!.test.difficulty_level }}</p>
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Commencé</label>
                    <p class="mt-1 text-gray-900">{{ formatDate(selectedTest()!.started_at) }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Complété</label>
                    <p class="mt-1 text-gray-900">{{ formatDate(selectedTest()!.completed_at) }}</p>
                  </div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Durée du Test</label>
                  <p class="mt-1 text-gray-900">{{ selectedTest()!.test.timer }} minutes</p>
                </div>
              </div>
            }

            <!-- Scoring Tab -->
            @if (activeTab() === 'scoring') {
              <div class="space-y-6">
                <div class="rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 p-6">
                  <div class="grid grid-cols-3 gap-4">
                    <div class="text-center">
                      <p class="text-sm text-gray-600">Score</p>
                      <p class="mt-2 text-3xl font-bold text-blue-600">
                        {{ selectedTest()!.scoring.score }}/{{ selectedTest()!.scoring.total_questions }}
                      </p>
                    </div>
                    <div class="text-center">
                      <p class="text-sm text-gray-600">Pourcentage</p>
                      <p class="mt-2 text-3xl font-bold text-indigo-600">
                        {{ selectedTest()!.scoring.percentage }}%
                      </p>
                    </div>
                    <div class="text-center">
                      <p class="text-sm text-gray-600">Questions</p>
                      <p class="mt-2 text-3xl font-bold text-purple-600">
                        {{ selectedTest()!.scoring.total_questions }}
                      </p>
                    </div>
                  </div>
                </div>

                @if (selectedTest()!.scoring.interpretation) {
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Interprétation</label>
                    <p class="mt-2 rounded-lg bg-gray-50 p-4 text-gray-900">
                      {{ selectedTest()!.scoring.interpretation }}
                    </p>
                  </div>
                }

                @if (selectedTest()!.scoring.feedback) {
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Retour</label>
                    <p class="mt-2 rounded-lg bg-blue-50 p-4 text-gray-900">
                      {{ selectedTest()!.scoring.feedback }}
                    </p>
                  </div>
                }

                @if (selectedTest()!.scoring.recommendation) {
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Recommandation</label>
                    <p class="mt-2 rounded-lg bg-green-50 p-4 text-gray-900">
                      {{ selectedTest()!.scoring.recommendation }}
                    </p>
                  </div>
                }
              </div>
            }

            <!-- Observations Tab -->
            @if (activeTab() === 'observations') {
              @if (selectedTest()!.observations.length === 0) {
                <div class="text-center text-gray-500">
                  <p>Aucune observation pour ce test</p>
                </div>
              } @else {
                <div class="space-y-4">
                  @for (obs of selectedTest()!.observations; track obs.id) {
                    <div class="rounded-lg border border-gray-200 p-4">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <h4 class="font-semibold text-gray-900">{{ obs.label }}</h4>
                          <p class="mt-1 text-sm text-gray-600">{{ obs.description }}</p>
                          <div class="mt-2 flex gap-2">
                            <span class="inline-block rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-700">
                              {{ obs.category }}
                            </span>
                            <span class="inline-block rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-700">
                              {{ obs.visible_to }}
                            </span>
                            <span class="inline-block rounded-full bg-purple-100 px-2 py-1 text-xs text-purple-700">
                              {{ obs.trigger_type }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  }
                </div>
              }
            }
          </div>
        </div>
      } @else {
        <div class="rounded-lg border border-gray-200 bg-white p-8 text-center">
          <p class="text-gray-500">Sélectionnez un test pour voir les détails</p>
        </div>
      }
    </div>
  </div>
</div>

