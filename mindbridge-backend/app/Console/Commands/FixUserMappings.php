<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixUserMappings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mindbridge:fix-user-mappings 
                            {--dry-run : Show what would be fixed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix etudiant-user mappings to ensure emails match between etudiants and central users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 MindBridge User Mapping Fix Tool');
        $this->info('===================================');

        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            $this->analyzeCurrentMappings();
            
            if ($dryRun) {
                $this->showWhatWouldBeFixed();
            } else {
                $this->fixMappings();
            }

            $this->info('✅ User mapping fix completed successfully!');

        } catch (\Exception $e) {
            $this->error('❌ Error during user mapping fix: ' . $e->getMessage());
            Log::error('MindBridge user mapping fix error: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Analyze current mappings
     */
    private function analyzeCurrentMappings()
    {
        $this->info('🔍 Analyzing current user-etudiant mappings...');

        $etudiants = DB::connection('mind_bridge')->table('etudiants')->get();
        $correctMappings = 0;
        $incorrectMappings = 0;
        $missingUsers = 0;

        foreach ($etudiants as $etudiant) {
            $centralUser = DB::connection('centrale')->table('users')->find($etudiant->user_id);
            
            if (!$centralUser) {
                $missingUsers++;
            } elseif ($centralUser->email === $etudiant->email) {
                $correctMappings++;
            } else {
                $incorrectMappings++;
            }
        }

        $this->table(['Status', 'Count'], [
            ['✅ Correct mappings', $correctMappings],
            ['❌ Incorrect mappings', $incorrectMappings],
            ['🚫 Missing users', $missingUsers],
            ['📊 Total etudiants', $etudiants->count()],
        ]);
    }

    /**
     * Show what would be fixed in dry run mode
     */
    private function showWhatWouldBeFixed()
    {
        $this->info('🧪 Changes that would be made:');

        $etudiants = DB::connection('mind_bridge')->table('etudiants')->get();
        $changes = [];

        foreach ($etudiants as $etudiant) {
            $centralUser = DB::connection('centrale')->table('users')->find($etudiant->user_id);
            $correctUser = DB::connection('centrale')->table('users')->where('email', $etudiant->email)->first();

            if ($correctUser && (!$centralUser || $centralUser->email !== $etudiant->email)) {
                $changes[] = [
                    'Email' => $etudiant->email,
                    'Current user_id' => $etudiant->user_id,
                    'Correct user_id' => $correctUser->id,
                    'Action' => 'Update mapping'
                ];
            }
        }

        if (empty($changes)) {
            $this->info('✅ No changes needed - all mappings are correct!');
        } else {
            $this->table(['Email', 'Current user_id', 'Correct user_id', 'Action'], $changes);
        }
    }

    /**
     * Fix the mappings
     */
    private function fixMappings()
    {
        $this->info('🔧 Fixing user-etudiant mappings...');

        // Disable foreign key checks temporarily
        DB::connection('mind_bridge')->statement('SET FOREIGN_KEY_CHECKS=0');

        try {
            $etudiants = DB::connection('mind_bridge')->table('etudiants')->get();
            $fixedCount = 0;

            foreach ($etudiants as $etudiant) {
                // Find the correct user by email
                $correctUser = DB::connection('centrale')->table('users')->where('email', $etudiant->email)->first();

                if ($correctUser && $correctUser->id != $etudiant->user_id) {
                    // Update the etudiant to point to the correct user
                    DB::connection('mind_bridge')
                        ->table('etudiants')
                        ->where('id', $etudiant->id)
                        ->update(['user_id' => $correctUser->id]);

                    $this->info("✅ Fixed: {$etudiant->email} → user_id: {$etudiant->user_id} → {$correctUser->id}");
                    $fixedCount++;
                } elseif ($correctUser) {
                    $this->line("ℹ️  Already correct: {$etudiant->email} → user_id: {$etudiant->user_id}");
                } else {
                    $this->warn("❌ No user found for: {$etudiant->email}");
                }
            }

            $this->info("✅ Fixed {$fixedCount} etudiant-user mappings");

        } finally {
            // Re-enable foreign key checks
            DB::connection('mind_bridge')->statement('SET FOREIGN_KEY_CHECKS=1');
        }

        // Verify the fix
        $this->verifyMappings();
    }

    /**
     * Verify that all mappings are correct
     */
    private function verifyMappings()
    {
        $this->info('🔍 Verifying fixed mappings...');

        $etudiants = DB::connection('mind_bridge')->table('etudiants')->get();
        $allCorrect = true;

        foreach ($etudiants as $etudiant) {
            $centralUser = DB::connection('centrale')->table('users')->find($etudiant->user_id);

            if (!$centralUser || $centralUser->email !== $etudiant->email) {
                $this->error("❌ Still incorrect: {$etudiant->email} → user_id: {$etudiant->user_id}");
                $allCorrect = false;
            }
        }

        if ($allCorrect) {
            $this->info('🎉 All etudiant-user mappings are now correct!');
            $this->info('📊 Summary:');
            $this->info("  - Total etudiants: {$etudiants->count()}");
            $this->info('  - All mappings correct: ✅');
            $this->info('  - No testCreators included: ✅');
            $this->info('  - Foreign key constraints satisfied: ✅');
        } else {
            $this->error('❌ Some mappings are still incorrect');
        }
    }
}
