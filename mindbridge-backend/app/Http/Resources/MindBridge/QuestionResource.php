<?php

namespace App\Http\Resources\MindBridge;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuestionResource extends JsonResource
{
    public static $wrap = null;

    public function toArray(Request $request): array
    {
        $categories = $this->steps
            ->map(fn ($step) => $step->test?->category) 
            ->filter() 
            ->unique('id') 
            ->values(); 

        $matieres = $this->steps
            ->map(fn ($step) => $step->test?->matiere) 
            ->filter() 
            ->unique('id');

        $niveaux = $this->steps
            ->map(fn ($step) => $step->test?->niveau) 
            ->filter() 
            ->unique('id') 
            ->values(); 

        return [
            'id' => $this->id,
            'type' => $this->type,
            'content' => $this->type === 'text' ? $this->content : asset('storage/' . $this->content),
            'description' => $this->description,
            'options' => $this->options,
            'categories' => $categories, 
            'matieres' => $matieres,     
            'niveaux' => $niveaux,   
            'is_required' => $this->is_required,
            'is_true' => $this->is_true,
            'is_false' => $this->is_false,
            'answer' => $this->is_true ? true : ($this->is_false ? false : null),
            'image_path' => $this->image_path,
        ];
    }
}
