<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['text', 'image', 'video', 'audio']);
            $table->string('content');
            $table->text('description')->nullable();
            
            $table->boolean('is_true')->nullable();
            $table->boolean('is_false')->nullable();
            $table->boolean('answer')->nullable();
            
            $table->boolean('is_required')->default(true);
            $table->string('image_path')->nullable();
            $table->enum('difficulty', ['facile', 'moyen', 'difficile'])->nullable();
            $table->enum('response_type', ['one', 'many', 'true_false'])->nullable()->default('one');

            $table->foreignId('matiere_id')
                ->nullable()
                ->constrained(config('database.connections.centrale.database') . '.matieres')
                ->cascadeOnDelete();
    
            $table->foreignId('niveau_id')
                ->nullable()
                ->constrained(config('database.connections.centrale.database') . '.niveaux')
                ->nullOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
