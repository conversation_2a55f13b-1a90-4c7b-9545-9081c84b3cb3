import { HttpParams } from '@angular/common/http';
import { OPTION_ALL } from '../global.config';

/**
 * Recursively traverses an object and removes properties with null, undefined, empty string,
 * -1, empty arrays, or File objects, returning a cleaned object.
 *
 * @param {object} obj - The input object to clean.
 * @returns {object} - A new object with non-null and non-empty values.
 */
export const removeEmptyValues = (obj: object): object => {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    // Check if the value should be omitted
    if (
      value === null ||
      value === undefined ||
      value === '' ||
      value === OPTION_ALL ||
      (Array.isArray(value) && !value.length) ||
      (typeof value === 'object' && value instanceof File)
    ) {
      return acc; // Skip this value and continue to the next iteration
    }

    // Recursively handle nested objects
    if (typeof value === 'object' && !Array.isArray(value)) {
      const cleanedValue = removeEmptyValues(value);
      if (Object.keys(cleanedValue).length) {
        acc[key] = cleanedValue;
      }
    } else {
      // Add non-null, non-empty values to the cleaned object
      acc[key] = value;
    }

    return acc;
  }, {} as any);
};

/**
 * Updates an `HttpParams` object with pagination parameters.
 *
 * @param {HttpParams} params - The existing `HttpParams` object.
 * @param {Pagination} pageEvent - The page event (contains page size and page index).
 * @returns {HttpParams} - A new `HttpParams` object with the updated pagination parameters.
 */
function getPageParams(params: HttpParams, pageEvent?: Pagination): HttpParams {
  if (pageEvent) {
    params = params
      .set('page', pageEvent.current_page)
      .set('limit', pageEvent.per_page);
  }
  return params;
}

/**
 * Generates HTTP query parameters for a resource, including filtering, pagination, and sorting.
 *
 * @param {object} data - The data object for filtering.
 * @param pageEvent
 * @returns {HttpParams} - An instance of HttpParams containing the generated query parameters.
 */
export function getQuery(data: object, pageEvent?: Pagination): HttpParams {
  let params: HttpParams = getQueryParams(data);
  params = getPageParams(params, pageEvent);
  return params;
}

/**
 * Converts an input object into HTTP query parameters.
 *
 * @param {object} data - The input data object.
 * @returns {HttpParams} - An instance of HttpParams containing the generated query parameters.
 */
function getQueryParams(data: object): HttpParams {
  // Check if the input data is falsy and return an empty HttpParams object if so.
  if (!data) return new HttpParams();

  // Use Object.entries to get an array of key-value pairs from the input data.
  return Object.entries(removeEmptyValues(data)).reduce(
    // Use the reduce function to accumulate query parameters in an HttpParams instance.
    (queryParams: HttpParams, [key, value]: [string, any]) => {
      // Check if the value is an array with at least one item.
      if (Array.isArray(value) && value.length) {
        // If it's an array, iterate through its items and append each one as a separate parameter.
        value.forEach(
          (item) => (queryParams = queryParams.append(`${key}[]`, item))
        );
      } else {
        // If it's not an array, set it as a single parameter.
        queryParams = queryParams.set(key, value);
      }
      return queryParams; // Return the updated HttpParams instance.
    },
    new HttpParams() // Initialize the accumulator with an empty HttpParams instance.
  );
}
export type Pagination = { current_page: number; per_page: number };
/**
 * transformer un objet en formData
 * @param obj
 * @returns {FormData}
 */
export const objectToFormData = (obj: any): FormData => {
  const formData = new FormData();
  const flattenedObject = flattenObject(obj, [], '.');
  // eslint-disable-next-line  @typescript-eslint/no-explicit-any
  Object.entries(flattenedObject).forEach(([key, value]: [string, any]) => {
    if (value) {
      const [first, ...rest] = key.split('.');
      let formDataKey = first;
      for (const lineKey of rest) {
        formDataKey += `[${lineKey}]`;
      }
      if (value instanceof Array) {
        value.forEach((v, index) =>
          formData.append(`${formDataKey}[${index}]`, v)
        );
      } else {
        formData.append(formDataKey, value);
      }
    }
  });
  return formData;
};
/**
 * convert nested object into flat object
 * ex:  object = { a: '1', b: { c: '2', d: { e: '3' } } } => result: {a: 1, b_c: 2, b_d_e: 3}
 * @param object
 * @param parents
 * @param separator
 */
export const flattenObject = (
  object: any,
  parents: any = [],
  separator: string = '_'
): any => {
  return Object.assign(
    {},
    ...Object.entries(object).map(([k, v]) => {
      return v && typeof v === 'object' && !(v instanceof File) // si v est de type file on ne veut pas faire un flatten, pour garder la structure de l'objet File
        ? flattenObject(v, [...parents, k], separator)
        : { [[...parents, k].join(separator)]: v };
    })
  );
};
