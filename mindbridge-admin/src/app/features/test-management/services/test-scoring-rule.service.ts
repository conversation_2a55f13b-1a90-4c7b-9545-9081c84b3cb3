import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { TestScoringRule, TestScoringRuleRequest, TestScoringRuleResponse } from '../models/test-scoring-rule';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TestScoringRuleService {
  private http = inject(HttpClient);

  /**
   * Get all scoring rules for a test
   */
  getScoringRules(testId: number): Observable<TestScoringRuleResponse> {
    return this.http.get<TestScoringRuleResponse>(
      `${environment.BASE_URL_API}mind_bridge/tests/${testId}/scoring-rules`
    );
  }

  /**
   * Create a new scoring rule
   */
  createScoringRule(testId: number, rule: TestScoringRuleRequest): Observable<any> {
    return this.http.post<any>(
      `${environment.BASE_URL_API}mind_bridge/tests/${testId}/scoring-rules`,
      rule
    );
  }

  /**
   * Update a scoring rule
   */
  updateScoringRule(testId: number, ruleId: number, rule: TestScoringRuleRequest): Observable<any> {
    return this.http.put<any>(
      `${environment.BASE_URL_API}mind_bridge/tests/${testId}/scoring-rules/${ruleId}`,
      rule
    );
  }

  /**
   * Delete a scoring rule
   */
  deleteScoringRule(testId: number, ruleId: number): Observable<any> {
    return this.http.delete<any>(
      `${environment.BASE_URL_API}mind_bridge/tests/${testId}/scoring-rules/${ruleId}`
    );
  }
}

