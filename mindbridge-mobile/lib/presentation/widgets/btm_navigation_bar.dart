import 'package:curved_navigation_bar/curved_navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/app/routes.dart';
import 'package:mindbridge/core/utilities/app_images.dart';
import 'package:sizer/sizer.dart';

import '../../../../data/repositories/auth_repository.dart';
import '../../app/theme/colors.dart';
import '../screens/espace_perso/view/espace_perso.dart';
import '../screens/home/<USER>/home_controller.dart';
import '../screens/home/<USER>/home_view.dart';
import 'controller/btm_navigation_controller.dart';

class BaseScreen extends StatelessWidget {
  final Widget? child;

  BaseScreen({super.key, this.child});

  final BottomNavController bottomNavController =
      Get.put(BottomNavController());

  final List<Widget> _screens = [
    const Center(child: Text('Chat')),
    const Center(child: Text('Cart')),
    const HomeView(),
    Center(
      child: GestureDetector(
        onTap: () {
          Get.dialog(
            Center(
              child: Container(
                width: 80.w,
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Confirm Logout',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.secondary,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      'Are you sure you want to logout?',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.black,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 3.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                                horizontal: 6.w, vertical: 1.5.h),
                            backgroundColor: AppColors.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          onPressed: () async {
                            final authRepository = Get.find<AuthRepository>();
                            await authRepository.logout();
                          },
                          child: Text(
                            'Logout',
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                                horizontal: 6.w, vertical: 1.5.h),
                            backgroundColor: AppColors.lightPink,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          onPressed: () => Get.back(),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              color: AppColors.black,
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            barrierDismissible: false,
          );
        },
        child: Container(
          width: 60.w,
          padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 4.w),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.logout, color: AppColors.white, size: 20.sp),
              SizedBox(width: 2.w),
              Text(
                'Logout',
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    ),
    const EspacePersonnel(),
  ];
  final GlobalKey<CurvedNavigationBarState> _navBarKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        body: Stack(
          children: [
            IndexedStack(
              index: bottomNavController.selectedIndex.value,
              children: _screens,
            ),
            if (child != null && bottomNavController.selectedIndex.value == 2)
              Positioned.fill(
                child: child!,
              ),
          ],
        ),
        bottomNavigationBar: CurvedNavigationBar(
          key: _navBarKey,
          index: bottomNavController.selectedIndex.value,
          backgroundColor: Colors.transparent,
          color: Colors.white,
          buttonBackgroundColor: Colors.transparent,
          animationDuration: const Duration(milliseconds: 300),
          animationCurve: Curves.easeInOut,
          letIndexChange: (index) => true,
          height: (8.h).clamp(0.0, 75.0).toDouble(),
          items: <Widget>[
            Image.asset(
              AppImages.chat,
              width: 4.h,
              height: 4.h,
            ),
            Image.asset(
              AppImages.shoppingCart,
              width: 4.h,
              height: 4.h,
            ),
            Center(
              child: Image.asset(
                AppImages.litOwl,
                width: 7.h,
                height: 7.h,
              ),
            ),
            Image.asset(
              AppImages.doorSettings,
              width: 4.h,
              height: 4.h,
            ),
            Image.asset(
              AppImages.profile,
              width: 4.h,
              height: 4.h,
            ),
          ],
          onTap: (index) {
            final homeController = Get.find<HomeController>();
            final currentIndex = bottomNavController.selectedIndex.value;

            if (index == 2 && currentIndex == 2) {
              homeController.resetCarousel();
              Get.toNamed(Routes.btmNavigationBar);
            } else if (index != currentIndex) {
              bottomNavController.changeIndex(index);
            }
          },
        ),
      ),
    );
  }
}
