import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/presentation/widgets/btm_navigation_bar.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/constants/app_strings.dart';
import '../../../widgets/global_error.dart';
import '../../../widgets/global_header.dart';
import '../../../widgets/global_loader.dart';
import '../../../widgets/global_white_card.dart';
import '../controller/subject_controller.dart';
import '../widgets/subject_card.dart';

class SelectSubjectView extends StatelessWidget {
  final int categoryId;
  const SelectSubjectView({super.key, required this.categoryId});

  @override
  Widget build(BuildContext context) {
    final SubjectController controller = Get.find<SubjectController>();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.fetchSubjects(categoryId);
    });

    return BaseScreen(
      child: Scaffold(
        appBar: const GlobalHeader(),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
          child: Obx(() {
            return GlobalWhiteCard(
              title: AppStrings.selectLesson,
              child: controller.isLoading.value
                  ? const Center(child: GlobalLoader())
                  : controller.isError.value
                      ? GlobalErrorHandler(
                          onRetry: () {
                            controller.fetchSubjects(categoryId);
                          },
                        )
                      : controller.isEmptyData.value
                          ? const SizedBox.shrink()
                          : GridView.builder(
                              padding: EdgeInsets.all(2.w),
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 2,
                                crossAxisSpacing: 3.w,
                                mainAxisSpacing: 3.h,
                                childAspectRatio: 2.5 / 1.5,
                              ),
                              itemCount: controller.subjects.length,
                              itemBuilder: (context, index) {
                                return SubjectCard(
                                  subject: controller.subjects[index],
                                );
                              },
                            ),
            );
          }),
        ),
      ),
    );
  }
}
