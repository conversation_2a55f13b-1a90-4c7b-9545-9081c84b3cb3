import { Pipe, PipeTransform } from '@angular/core';
import { DifficultyLevelEnum } from '../../test-management/models/difficulty-level-enum';

@Pipe({
  name: 'formatDifficultyLevel',
  standalone: true,
})
export class FormatDifficultyLevelPipe implements PipeTransform {
  transform(difficultylevel: DifficultyLevelEnum): string {
    switch (difficultylevel) {
      case DifficultyLevelEnum.TresFacile:
        return 'Très facile';
      case DifficultyLevelEnum.Facile:
        return 'Facile';
      case DifficultyLevelEnum.Intermediaire:
        return 'Intermédiaire';
      case DifficultyLevelEnum.Difficile:
        return 'Difficile';
      case DifficultyLevelEnum.TresDifficile:
        return 'Très difficile';

      default:
        return 'Unknown difficulty level';
    }
  }
}
