import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:mindbridge/core/constants/app_strings.dart';

class LoadingUtils {
  static void initEasyLoading() {
    EasyLoading.instance
      ..loadingStyle = EasyLoadingStyle.custom
      ..indicatorType = EasyLoadingIndicatorType.doubleBounce
      ..indicatorSize = 45.0
      ..radius = 10.0
      ..progressColor = Colors.white
      ..backgroundColor = Colors.grey[600]
      ..indicatorColor = Colors.white
      ..textColor = Colors.white
      ..maskColor = Colors.black.withOpacity(0.5)
      ..userInteractions = false
      ..dismissOnTap = false;
  }

  static void showLoading([String? status]) {
    _setDefaultTheme(Colors.grey[600]!);
    EasyLoading.show(
      status: status ?? AppStrings.loading,
    );
  }

  static void showSuccess(String message) {
    _setDefaultTheme(Colors.green);
    EasyLoading.showSuccess(
      message,
      duration: const Duration(seconds: 1),
    );
  }

  static void showError(String message) {
    _setDefaultTheme(Colors.red);
    EasyLoading.showError(
      message,
      duration: const Duration(seconds: 1),
    );
  }

  static void dismiss() {
    EasyLoading.dismiss();
  }

  static void _setDefaultTheme(Color backgroundColor) {
    EasyLoading.instance
      ..loadingStyle = EasyLoadingStyle.custom
      ..backgroundColor = backgroundColor
      ..progressColor = Colors.white
      ..indicatorColor = Colors.white
      ..textColor = Colors.white;
  }
}
