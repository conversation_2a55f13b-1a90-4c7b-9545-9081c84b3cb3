import { Component, effect, inject, OnInit, Signal } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import { AppState } from '../../../../core/app.state';
import { SetLoading } from '../../../shared/store/shared.actions';
import * as matieresActions from '../../store/matieres.actions';
import {
  MatiereResponse,
  Niveau,
  NiveauMini,
} from '../../models/matiere-response.model';
import { ActivatedRoute, RouterLink, RouterLinkActive } from '@angular/router';
import { MatieresStore } from '../../matieres.store';
import { ChapterResponse } from '../../models/chapter-response.model';
import { ClearChapters, GetChapiters } from '../../store/matieres.actions';
import { Observable, of } from 'rxjs';
import { AddContentFormGroup } from '../../models/add-content-form-group.model';
import { AsyncPipe } from '@angular/common';
import {
  MatStepper,
  MatStep,
  MatStepLabel,
  MatStepperNext,
  MatStepperPrevious,
} from '@angular/material/stepper';
import { MatError } from '@angular/material/form-field';

@Component({
  selector: 'app-add-contenue',
  templateUrl: './add-contenue.component.html',
  styleUrl: './add-contenue.component.scss',
  providers: [MatieresStore],
  imports: [
    ReactiveFormsModule,
    RouterLink,
    MatError,
    MatStepper,
    MatStep,
    MatStepLabel,
    MatStepperNext,
    RouterLinkActive,
    MatStepperPrevious,
    AsyncPipe,
  ],
})
export class AddContenueComponent implements OnInit {
  private fb = inject(FormBuilder);
  private store = inject<Store<AppState>>(Store);
  private routerActive = inject(ActivatedRoute);

  selectedFile: File | null = null;

  #matieresStore = inject(MatieresStore);
  matieres: Signal<MatiereResponse[]> = this.#matieresStore.state.matieres;
  niveaux: Signal<NiveauMini[]> = this.#matieresStore.state.niveaux;
  $chapters: Observable<ChapterResponse[]> = this.store.select(
    (state) => state.matieres.chapters
  );

  matiereId!: number;
  selectedNiveau!: number;

  tryToSave = false;

  constructor() {
    this.routerActive.paramMap.subscribe((params) => {
      this.matiereId = +params.get('matiere')!;
    });

    effect(() => {
      if (this.matieres()) {
        this.#matieresStore.getNiveauxByMatiere(this.matiereId);
      }
    });
  }

  ngOnInit(): void {
    this.store.dispatch(SetLoading({ isAppLoading: true }));
    this.store.dispatch(matieresActions.AllChapitres());
    this.#matieresStore.getMatieres(null);
    this.initForm();
  }

  matiereById(id: number): MatiereResponse | undefined {
    return this.matieres().find((m) => m.id === id);
  }

  initForm() {
    this.form.controls['matiere_id'] = new FormControl(
      { value: this.matiereId.toString(), disabled: true },
      Validators.required
    );
  }

  form: FormGroup<AddContentFormGroup> = this.fb.group({
    matiere_id: new FormControl<string | null>(null, [Validators.required]),
    niveau_id: new FormControl<string | null>(null, [Validators.required]),
    chapter_id: new FormControl<string | null>(null, [Validators.required]),
    title: new FormControl<string | null>(null, [Validators.required]),
    description: new FormControl<string | null>(null),
    file: new FormControl<File | null>(null, [Validators.required]),
  });

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      // Set file at this.selectedFile
      this.selectedFile = input.files[0];
    }
  }

  save() {
    this.tryToSave = true;
    if (this.form.valid) {
      const payload = this.form.value;
      const formData = new FormData();
      formData.append(
        'matiere_id',
        this.matiereId?.toString() as string | Blob
      );
      Object.keys(payload).forEach((key) => {
        if (key === 'file') {
          formData.append(key, this.selectedFile!, this.selectedFile?.name);
        } else {
          formData.append(
            key,
            payload[key as keyof typeof payload] as string | Blob
          );
        }
      });

      this.store.dispatch(SetLoading({ isAppLoading: true }));
      this.store.dispatch(matieresActions.AddContenue({ payload: formData }));
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    }
  }

  protected readonly Number = Number;

  onNiveauSelect(event: Event): void {
    this.store.dispatch(ClearChapters());
    this.selectedNiveau = +(event.target as HTMLSelectElement).value;
    this.store.dispatch(
      GetChapiters({ matierId: this.matiereId, niveauId: this.selectedNiveau })
    );
  }
}
