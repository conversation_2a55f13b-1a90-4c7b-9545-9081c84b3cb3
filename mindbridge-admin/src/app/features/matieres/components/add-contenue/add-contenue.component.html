<div style="width: 96%; margin: 0 auto" [formGroup]="form">
  <div class="df back">
    <a [routerLink]="['/list-contenues', matiereId]">
      <img src="assets/icones/arrow_back.png" alt="" />
    </a>
    <h2>Ajouter contenu</h2>
  </div>
  <div
    class="mi-auto mt-4 flex flex-col gap-4 rounded-lg bg-white px-4 pb-4 pt-4"
  >
    <div class="flex flex-row gap-4">
      <div class="w100-2 flex h-20 flex-col items-start justify-start gap-2">
        <label class="font-medium" for="input1">Matière</label>
        <select
          class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
          id="input1"
          formControlName="matiere_id"
        >
          <option [value]="matiereId" selected>
            {{ matiereById(Number(matiereId))?.name_fr }}
          </option>
        </select>
      </div>
      <div class="w100-2 flex h-20 flex-col items-start justify-start gap-2">
        <label class="font-medium" for="input2">Niveau</label>
        <select
          class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
          id="input2"
          formControlName="niveau_id"
          (change)="onNiveauSelect($event)"
        >
          @for (niveau of niveaux(); track niveau.id) {
            <option [value]="niveau.id">{{ niveau.name }}</option>
          }
        </select>
        @if (
          form.controls['niveau_id'].hasError('required') &&
          (tryToSave || form.controls['niveau_id'].touched)
        ) {
          <mat-error class="text-red-500">* Le niveau est requis.</mat-error>
        }
      </div>
    </div>
    <div class="flex flex-row gap-4">
      <div class="w100-2 flex h-20 flex-col items-start justify-start gap-2">
        <label class="font-medium" for="input3">Chapitre</label>
        <select
          class="box-border min-h-10 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
          id="input3"
          formControlName="chapter_id"
        >
          @for (chapter of $chapters | async; track chapter.id) {
            <option [value]="chapter.id">{{ chapter.chapter }}</option>
          }
        </select>
        @if (
          form.controls['chapter_id'].hasError('required') &&
          (tryToSave || form.controls['chapter_id'].touched)
        ) {
          <mat-error class="text-red-500">* Le chapitre est requis.</mat-error>
        }
      </div>
      <div class="w100-2 flex h-20 flex-col items-start justify-start gap-2">
        <label class="font-medium" for="input4">Titre leçon</label>
        <input
          class="box-border min-h-10 w-full rounded border-2 border-solid border-gray-200 px-3"
          type="text"
          id="input4"
          placeholder="---"
          formControlName="title"
        />
        @if (
          form.controls['title'].hasError('required') &&
          (tryToSave || form.controls['title'].touched)
        ) {
          <mat-error class="text-red-500">* Le titre est requis.</mat-error>
        }
      </div>
    </div>
    <div class="flex w-full flex-row">
      <div class="flex w-full flex-col gap-2">
        <label class="font-medium" for="input5">Description</label>
        <textarea
          class="box-border min-h-32 w-48 w-full rounded border-2 border-solid border-gray-200 px-3 py-1.5"
          id="input5"
          placeholder="Entrer une description..."
          formControlName="description"
        ></textarea>
      </div>
    </div>
  </div>
  <div class="mi-auto mt-4 flex flex-col gap-4 rounded-lg">
    <mat-stepper [linear]="false" #stepper class="w-full">
      <mat-step>
        <form [formGroup]="form">
          <ng-template matStepLabel>Chargement de contenu</ng-template>
          <div
            class="steper steper_upload flex flex-col items-center justify-center"
          >
            <img class="ph_upload" src="assets/icones/upload-icon.png" alt="" />
            <input
              formControlName="file"
              type="file"
              accept="application/pdf"
              (change)="onFileSelected($event)"
            />
            <p class="placeholder_upload">
              <span>Cliquer pour charger</span>
              ou glisser-déposer
              <br />
              SVG, PNG, JPG or GIF (max. 800x400px)
            </p>
            @if (
              form.controls['file'].hasError('required') &&
              (tryToSave || form.controls['file'].touched)
            ) {
              <mat-error class="text-red-500">
                * Le contenu est requis.
              </mat-error>
            }
          </div>
          @if (false) {
            <div>
              <button mat-button matStepperNext>Next</button>
            </div>
          }
        </form>
      </mat-step>
      <mat-step>
        <ng-template matStepLabel>Création de contenu</ng-template>
        <h2>Modèles de création de contenu</h2>
        <div class="steper">
          <div class="flex-center mt-4 flex flex-col gap-4 md:flex-row">
            <div
              class="flex h-28 w-48 flex-col items-center justify-center rounded-lg bg-gradient-to-br from-[#C1EFF7] via-[#6BA4BD] to-[#7E588E]"
            >
              <img class="mb-2 h-5 w-5" src="assets/icones/vector.png" alt="" />
              <h5 class="text-white">Créer nouveau contenu</h5>
            </div>
            <div
              class="flex h-28 w-48 flex-col items-center justify-start rounded-lg border border-gray-200 bg-white pt-0"
            >
              <a class="my-0 ml-auto mr-2 mt-1 h-5 w-5">
                <!-- (click)="addTestProfile()" -->
                <img src="assets/icones/more-horizontal.png" alt="" />
              </a>
              <a
                class="flex w-full flex-col items-center"
                routerLink="/add-test"
                routerLinkActive="active-route"
              >
                <img
                  class="mx-4 my-0 h-12 w-12"
                  src="assets/icones/profiling-test.png"
                  alt=""
                />
                <div class="w-full">
                  <hr class="mb-1 mt-2 border-t border-gray-200" />
                </div>
                <h5 class="text-gray-500">Modèle 1</h5>
              </a>
            </div>
            <div
              class="flex h-28 w-48 flex-col items-center justify-start rounded-lg border border-gray-200 bg-white pt-0"
            >
              <a
                class="my-0 ml-auto mr-2 mt-1 h-5 w-5"
                routerLink="/add-test"
                routerLinkActive="active-route"
              >
                <img src="assets/icones/more-horizontal.png" alt="" />
              </a>
              <a
                class="flex w-full flex-col items-center"
                routerLink="/add-test"
                routerLinkActive="active-route"
              >
                <img
                  class="mx-4 my-0 h-12 w-12"
                  src="assets/icones/content-test.png"
                  alt=""
                />
                <div class="w-full">
                  <hr class="mb-1 mt-2 border-t border-gray-200" />
                </div>
                <h5 class="text-gray-500">Modèle 2</h5>
              </a>
            </div>
            <div
              class="flex h-28 w-48 flex-col items-center justify-start rounded-lg border border-gray-200 bg-white pt-0"
            >
              <a
                class="my-0 ml-auto mr-2 mt-1 h-5 w-5"
                routerLink="/add-test"
                routerLinkActive="active-route"
              >
                <img src="assets/icones/more-horizontal.png" alt="" />
              </a>
              <a
                class="flex w-full flex-col items-center"
                routerLink="/add-test"
                routerLinkActive="active-route"
              >
                <img
                  class="mx-4 my-0 h-12 w-12"
                  src="assets/icones/practice-test.png"
                  alt=""
                />
                <div class="w-full">
                  <hr class="mb-1 mt-2 border-t border-gray-200" />
                </div>
                <h5 class="text-gray-500">Modèle 3</h5>
              </a>
            </div>
            <div
              class="flex h-28 w-48 flex-col items-center justify-start rounded-lg border border-gray-200 bg-white pt-0"
            >
              <a
                class="my-0 ml-auto mr-2 mt-1 h-5 w-5"
                routerLink="/add-test"
                routerLinkActive="active-route"
              >
                <img src="assets/icones/more-horizontal.png" alt="" />
              </a>
              <a
                class="flex w-full flex-col items-center"
                routerLink="/add-test"
                routerLinkActive="active-route"
              >
                <img
                  class="mx-4 my-0 h-12 w-12"
                  src="assets/icones/quiz-general-culture.png"
                  alt=""
                />
                <div class="w-full">
                  <hr class="mb-1 mt-2 border-t border-gray-200" />
                </div>
                <h5 class="text-gray-500">Modèle 4</h5>
              </a>
            </div>
          </div>
        </div>
        @if (false) {
          <div>
            <button mat-button matStepperPrevious>Back</button>
            <button mat-button (click)="stepper.reset()">Reset</button>
          </div>
        }
      </mat-step>
    </mat-stepper>
  </div>
  <div
    class="mt-4 flex justify-end bg-white p-4"
    style="margin-left: -25px; width: 104%"
  >
    <button
      class="bg_btn_censel btn mr-4 flex items-center gap-1 rounded-lg px-4 py-2"
    >
      <span class="color-white font-medium">Annuler</span>
    </button>
    <button
      class="bg_btn btn flex items-center gap-1 rounded-lg px-4 py-2"
      (click)="save()"
    >
      <span class="color-white font-medium">Enregistrer</span>
    </button>
  </div>
</div>
