<?php

namespace Database\Seeders\centrale;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MatiersSeederCentrale extends Seeder
{
    /**
     * Run the database seeds.
     */

    private function calculateTitleColor($gradientBackground)
    {
        $colors = json_decode($gradientBackground);
        if (count($colors) === 2) {
            $color1 = hexdec(substr($colors[0], 2)); 
            $color2 = hexdec(substr($colors[1], 2));

            
            $red = (int)(($color1 >> 16 & 0xFF) + ($color2 >> 16 & 0xFF)) / 2;
            $green = (int)(($color1 >> 8 & 0xFF) + ($color2 >> 8 & 0xFF)) / 2;
            $blue = (int)(($color1 & 0xFF) + ($color2 & 0xFF)) / 2;

            return sprintf('#%02X%02X%02X', $red, $green, $blue);
        }
        return '#FFFFFF';
    }

    public function run(): void
    {

        $matieres = [
            ['name_fr' => 'Français', 'name_ar' => 'اللغة الفرنسية', 'description' => null, 'image_url' => 'mindbridge/francais_image.png', 'gradient_background' => json_encode(["0xffF9E8F3",
                "0xffECBADB",
                "0xffCB489F"
            ]), 'gradient_border' => json_encode([
                "0xffF9E8F3",
                "0xffECBADB",
                "0xffCB489F"
            ])], // Français
            ['name_fr' => 'Mathématiques', 'name_ar' => 'الرياضيات', 'description' => null, 'image_url' => 'mindbridge/mathematiques.png', 'gradient_background' => json_encode([
                "0xffC4DBE5",
                "0xffA6C8D7",
                "0xff6BA4BD"
            ]), 'gradient_border' => json_encode([
                "0xffC4DBE5",
                "0xffA6C8D7",
                "0xff6BA4BD"
            ])], // Mathématiques
            ['name_fr' => 'Sciences Naturelles', 'name_ar' => 'العلوم الطبيعية', 'description' => null, 'image_url' => 'mindbridge/sciences_naturelles.png', 'gradient_background' => json_encode([
                "0xffE0E8F0",
                "0xff9CAEBE",
                "0xff58738B"
            ]), 'gradient_border' => json_encode([
                "0xffE0E8F0",
                "0xff9CAEBE",
                "0xff58738B"
            ])], // Sciences de la Vie et de la Terre (SVT)
            ['name_fr' => 'Finance', 'name_ar' => 'تمويل', 'description' => null, 'image_url' => 'mindbridge/finance_image.png', 'gradient_background' => json_encode([
                "0xffBCC3D3",
                "0xff9FA5B5",
                "0xff464D5C"
            ]), 'gradient_border' => json_encode([
                "0xffBCC3D3",
                "0xff9FA5B5",
                "0xff464D5C"
            ])], // Économie (optionnel)
        ];

        foreach ($matieres as &$matiere) {
            $matiere['title_color'] = $this->calculateTitleColor($matiere['gradient_background']);
        }
    
        \DB::table('matieres')->insert($matieres);
    }
}
