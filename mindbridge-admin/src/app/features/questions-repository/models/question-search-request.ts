import { FormControl } from '@angular/forms';
import { OPTION_ALL } from '../../shared/global.config';

export interface IQuestionSearchForm {
  matiere: number;
  title: string;
  niveau: number;
  category: number;
}

export class QuestionSearchForm implements IQuestionSearchForm {
  matiere: number;
  title: string;
  niveau: number;
  category: number;
  constructor() {
    this.matiere = OPTION_ALL;
    this.title = '';
    this.niveau = OPTION_ALL;
    this.category = OPTION_ALL;
  }
}

export interface IQuestionSearchFormGroup {
  matiere: FormControl<number>;
  title: FormControl<string>;
  niveau: FormControl<number>;
  category: FormControl<number>;
}

export class QuestionSearchFormGroup implements IQuestionSearchFormGroup {
  matiere: FormControl<number>;
  title: FormControl<string>;
  niveau: FormControl<number>;
  category: FormControl<number>;

  constructor(testSearchForm: IQuestionSearchForm = new QuestionSearchForm()) {
    this.matiere = new FormControl<number>(testSearchForm.matiere);
    this.title = new FormControl<string>(testSearchForm.title);
    this.niveau = new FormControl<number>(testSearchForm.niveau);
    this.category = new FormControl<number>(testSearchForm.category);
  }
}
