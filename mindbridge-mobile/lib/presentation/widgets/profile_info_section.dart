import 'package:flutter/material.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:sizer/sizer.dart';

import '../../../../app/theme/colors.dart';
import '../../../../core/utilities/app_images.dart';

class ProfileInfoSection extends StatelessWidget {
  final String name;
  final String gamificationName;
  final String levelName;
  final String balance;
  final String ranking;
  final Gradient bgGradient;
  final bool isEspaceHeader;

  const ProfileInfoSection({
    super.key,
    required this.name,
    required this.gamificationName,
    required this.levelName,
    required this.balance,
    required this.ranking,
    required this.bgGradient,
    this.isEspaceHeader = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          name.isEmpty ? "..." : name,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.black,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 1.h),
        Text(
          levelName.isEmpty && gamificationName.isEmpty
              ? "..."
              : "$levelName | $gamificationName",
          style: TextStyle(
            fontSize: 10.sp,
            color: AppColors.black.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 2.h),
        Container(
          decoration: BoxDecoration(
            gradient: bgGradient,
            borderRadius: BorderRadius.circular(16),
            border: isEspaceHeader
                ? GradientBoxBorder(
                    gradient: AppColors.borderAiGradient,
                  )
                : null,
          ),
          padding: EdgeInsets.all(1.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                child: _buildInfoColumn(AppImages.coin, "Balance", balance),
              ),
              SizedBox(width: 2.w),
              Container(
                width: 0.5.w,
                height: 5.h,
                color: Colors.grey.shade300,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: _buildInfoColumn(
                    isEspaceHeader ? AppImages.trophy2 : AppImages.trophy,
                    "Classement",
                    ranking),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoColumn(String imagePath, String label, String value) {
    return Row(
      children: [
        Image.asset(
          imagePath,
          height: 3.h,
        ),
        SizedBox(width: 1.w),
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 10.sp,
                  color: AppColors.black.withOpacity(0.7),
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 11.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.black,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
