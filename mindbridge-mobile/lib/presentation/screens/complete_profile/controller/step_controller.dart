import 'package:get/get.dart';

import '../../../../app/routes.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/test_profiling.dart';
import '../../../../core/utilities/loading_utils.dart';
import '../../../../core/utilities/validators.dart';
import '../../../../data/providers/local/secure_storage_service.dart';
import '../../../../data/repositories/test_repository.dart';

class StepController extends GetxController {
  final TestRepository _testRepository;

  StepController({required TestRepository testRepository})
      : _testRepository = testRepository;

  var isLoading = true.obs;
  var hasError = false.obs;
  var currentStep = 1.obs;
  var totalSteps = 0.obs;
  late List<Step> steps;
  var selectedChoices = <int, List<int>>{}.obs;

  @override
  void onInit() {
    super.onInit();
    fetchTestQuestions();
  }

  Future<void> fetchTestQuestions() async {
    isLoading.value = true;
    hasError.value = false;

    try {
      final testProfiling = await _testRepository.fetchTestProfilingQuestions();

      if (testProfiling != null && testProfiling.test != null) {
        steps = testProfiling.test!.steps ?? [];
        totalSteps.value = steps.length;
      } else {
        hasError.value = true;
      }
    } catch (e) {
      hasError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> submitSelectedOptions() async {
    LoadingUtils.showLoading(AppStrings.submitting);
    try {
      final payload = {
        "test_id": steps.first.testId,
        "data": selectedChoices.entries.map((entry) {
          return {
            "question_id": steps[entry.key - 1].questionId,
            "selected_options": entry.value,
          };
        }).toList(),
      };

      final response = await _testRepository.submitTestResults(payload);

      if (response != null && response["message"] != null) {
        final secureStorage = Get.find<SecureStorageService>();
        await secureStorage.write('test_profiling_completed', "1");
        LoadingUtils.showSuccess(AppStrings.successMessage);

        Get.offAllNamed(Routes.congratulations,
            arguments: {"feedback_message": response["feedback_message"]});
      } else {
        LoadingUtils.showError(AppStrings.errorMessage);
      }
    } catch (e) {
      LoadingUtils.showError(AppStrings.errorMessage);
    } finally {
      LoadingUtils.dismiss();
    }
  }

  List<Option> get currentChoices =>
      steps[currentStep.value - 1].question?.options ?? [];

  String get currentQuestion =>
      steps[currentStep.value - 1].question?.content ?? "No Question";
  void selectChoice(int optionId) {
    final questionId = currentStep.value;
    selectedChoices.putIfAbsent(questionId, () => []);
    final choices = selectedChoices[questionId]!;
    final questionType = steps[questionId - 1].type ?? "many";

    if (questionType == "one") {
      choices.clear();
      choices.add(optionId);
    } else {
      if (choices.contains(optionId)) {
        choices.remove(optionId);
      } else {
        choices.add(optionId);
      }
    }
    selectedChoices[questionId] = choices;
  }

  List<int> getSelectedChoices() {
    return selectedChoices[currentStep.value] ?? [];
  }

  void nextStep() {
    if (currentStep.value < totalSteps.value) {
      currentStep++;
    }
  }

  void previousStep() {
    if (currentStep.value > 1) {
      currentStep--;
    }
  }

  bool isQuestionAnswered() {
    final selectedChoicesForCurrentQuestion =
        selectedChoices[currentStep.value] ?? [];
    return Validators.validateSelection(selectedChoicesForCurrentQuestion);
  }
}
