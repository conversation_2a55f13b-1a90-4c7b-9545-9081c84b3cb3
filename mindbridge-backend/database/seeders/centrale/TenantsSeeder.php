<?php

namespace Database\Seeders\centrale;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TenantsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \DB::table('organizations')->insert([
            [
                'name' => 'EL MOURADE',
            ]
        ]);
        \DB::table('schools')->insert([
            [
                'organization_id' => null,
                'name' => 'El Mourade Carrefour',
                'domain' => 'mindbridge-schoolyg-test.awlyg.tech',
                'database' => 'elmourade_carafour',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Mind Bridge',
                'domain' => 'mindbridge-admin.awlyg.tech',
                'database' => 'mind_bridge',
                "migrations_path" => "database/migrations/mindBridge/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Université Mohammed V de Rabat',
                'domain' => 'um5.awlyg.tech',
                'database' => 'universite_mohammed_v',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Université Cadi Ayyad de Marrakech',
                'domain' => 'uca.awlyg.tech',
                'database' => 'universite_cadi_ayyad',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Université Abdelmalek Essaâdi de Tétouan',
                'domain' => 'uae.awlyg.tech',
                'database' => 'universite_abdelmalek_essaadi',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Université Hassan II de Casablanca',
                'domain' => 'uh2.awlyg.tech',
                'database' => 'universite_hassan_ii',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Université Ibn Tofail de Kénitra',
                'domain' => 'uit.awlyg.tech',
                'database' => 'universite_ibn_tofail',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Université Chouaib Doukkali d’El Jadida',
                'domain' => 'ucd.awlyg.tech',
                'database' => 'universite_chouaib_doukkali',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Université Hassan 1er de Settat',
                'domain' => 'uh1.awlyg.tech',
                'database' => 'universite_hassan_1er',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Université Sultan Moulay Slimane de Béni Mellal',
                'domain' => 'usms.awlyg.tech',
                'database' => 'universite_sultan_moulay_slimane',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Université Ibn Zohr d’Agadir',
                'domain' => 'uibz.awlyg.tech',
                'database' => 'universite_ibn_zohr',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
            [
                'organization_id' => null,
                'name' => 'Université Moulay Ismaïl de Meknès',
                'domain' => 'umi.awlyg.tech',
                'database' => 'universite_moulay_ismai',
                "migrations_path" => "database/migrations/tenant/",
                'created_at' => now(),
            ],
        ]);
    }
}
