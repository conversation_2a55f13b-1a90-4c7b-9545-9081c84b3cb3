<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'mind_bridge';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mind_bridge')->table('observations', function (Blueprint $table) {
            $table->enum('trigger_type', ['parent', 'teacher', 'simulated_grade_shutdown'])
                ->default('parent')
                ->after('test_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mind_bridge')->table('observations', function (Blueprint $table) {
            $table->dropColumn('trigger_type');
        });
    }
};

