# MindBridge Backend Application Documentation

## Overview
MindBridge is a comprehensive educational platform built with Laravel 10 that provides mental health testing, educational content management, and student assessment capabilities. The application uses a multi-tenant architecture with separate databases for central management and tenant-specific data.

## Architecture Overview

### Framework & Technology Stack
- **Framework**: Laravel 10.x (PHP 8.1+)
- **Database**: MySQL 8.0.27
- **Authentication**: Laravel Sanctum
- **Permissions**: <PERSON><PERSON> Permission
- **File Processing**: PhpSpreadsheet, PhpWord, DomPDF
- **Notifications**: Firebase Cloud Messaging (FCM)
- **Containerization**: Docker with docker-compose

### Multi-Tenant Architecture
The application implements a sophisticated multi-tenant system with two primary database connections:

1. **Central Database (`centrale`)**: Manages global entities
2. **Tenant Database (`mind_bridge`)**: Handles tenant-specific data

## Database Architecture

### Database Connections

#### 1. Central Database (`centrale`)
**Purpose**: Global management and shared resources
**Tables**:
- `users` - Global user accounts
- `schools` - Educational institutions (tenants)
- `organizations` - Parent organizations
- `niveaux` - Educational levels (grades)
- `matieres` - Subjects/courses
- `permissions` & `roles` - Access control

#### 2. MindBridge Database (`mind_bridge`)
**Purpose**: Tenant-specific educational content and assessments
**Key Tables**:
- `etudiants` - Student profiles
- `tests` - Assessment tests
- `questions` - Test questions
- `steps` - Test step sequences
- `options` - Question answer options
- `categories` - Content categories
- `contents` - Educational materials
- `chapters` - Content organization
- `badges` - Achievement system
- `etudiant_points` - Student scoring
- `etudiant_test_answers` - Student responses
- `etudiant_test_assignments` - Test assignments
- `etudiant_test_status` - Test completion tracking

### Key Database Relationships

#### Student Management
```
users (centrale) 
  ↓ (1:1)
etudiants (mind_bridge)
  ↓ (1:many)
etudiant_test_answers, etudiant_points, etudiant_badges
```

#### Content Structure
```
matieres (centrale) ← chapters (mind_bridge) ← contents (mind_bridge)
                                    ↓
                                  tests (mind_bridge)
                                    ↓
                                  steps (mind_bridge)
                                    ↓
                                questions (mind_bridge)
                                    ↓
                                  options (mind_bridge)
```

#### Assessment Flow
```
tests → steps → questions → options
  ↓
etudiant_test_assignments
  ↓
etudiant_test_answers
  ↓
etudiant_test_status
```

## Core Models & Relationships

### Central Models (Connection: `centrale`)
- **User**: Base user authentication
- **School**: Tenant institutions
- **NiveauCentrale**: Educational levels
- **MatiereCentrale**: Academic subjects

### MindBridge Models (Connection: `mind_bridge`)
- **MindBridgeEtudiant**: Student profiles with soft deletes
- **Test**: Assessment definitions with categories and difficulty levels
- **Question**: Test questions with multiple types and media support
- **Step**: Test flow control and sequencing
- **Option**: Answer choices for questions
- **Category**: Content organization with hierarchical structure
- **Content**: Educational materials (PDFs, documents)
- **Chapter**: Content organization within subjects
- **Badges**: Achievement system
- **EtudiantPoints**: Student scoring and progress tracking

## API Structure

### Authentication Endpoints
```
POST /api/mind_bridge/auth/login - Admin login
POST /api/mind_bridge/auth/etudiant/login - Student login
```

### Protected Routes (Sanctum Authentication)
All routes under `/api/mind_bridge/` require authentication except auth routes.

#### Test Management
```
GET    /api/mind_bridge/tests/{id} - Get test details
GET    /api/mind_bridge/tests/{id}/step - Get first test step
POST   /api/mind_bridge/tests/storeAll - Create complete test
PUT    /api/mind_bridge/tests/updateAll/{id} - Update test
DELETE /api/mind_bridge/tests/{id} - Delete test
POST   /api/mind_bridge/run_test - Execute mental health test
```

#### Student Management
```
GET  /api/mind_bridge/etudiants/list - List students
GET  /api/mind_bridge/etudiant/detail/{id} - Student details
POST /api/mind_bridge/assign-test/{etudiantId}/{testId} - Assign test
```

#### Content Management
```
GET    /api/mind_bridge/contents - List content
POST   /api/mind_bridge/contents - Create content
GET    /api/mind_bridge/contents/{id} - Get content
PUT    /api/mind_bridge/contents/{id} - Update content
DELETE /api/mind_bridge/contents/{id} - Delete content
```

#### Student Mobile API
```
GET  /api/mind_bridge/etudiant/categories/board - Dashboard categories
GET  /api/mind_bridge/etudiant/tests - Student test profiling
POST /api/mind_bridge/etudiant/tests - Submit test answers
GET  /api/mind_bridge/etudiant/matiers/{id} - Get subjects by category
GET  /api/mind_bridge/etudiant/contents - Get lessons by subject
```

## Business Logic & Services

### TenantService
Handles dynamic database switching for multi-tenant operations:
- Switches database connections based on school domain
- Manages tenant isolation
- Handles connection purging and reconnection

### BadgePointService
Manages student achievement and scoring system:
- **Actions & XP Values**:
  - `daily_login`: 10 XP
  - `lesson_viewed`: 5 XP
  - `quiz_easy_passed`: 15 XP
  - `quiz_hard_passed`: 25 XP
  - `test_profiling_completed`: 50 XP
  - `challenge_hebdomadaire_completed`: 100 XP

### TemplateNotificationService
Handles push notifications via Firebase Cloud Messaging:
- Account creation notifications
- WhatsApp integration
- Password reset notifications

## Authentication & Authorization

### Authentication System
- **Laravel Sanctum**: API token-based authentication
- **Token Expiration**: 720 minutes (12 hours)
- **Multi-user Types**: admin, etudiant, enseignant, parrent
- **FCM Integration**: Firebase Cloud Messaging for push notifications

### Middleware Stack
1. **TenantsMiddleware**: Handles tenant resolution and database switching
2. **BeforeAuthentication**: Validates APP_SECRET header
3. **UpdateConnectionPoints**: Manages student connection streaks and rewards
4. **Sanctum Authentication**: Token validation

### User Types & Permissions
- **Admin**: Full system access, MindBridge management
- **Etudiant**: Student access, test taking, content viewing
- **Enseignant**: Teacher access (limited implementation)
- **Parrent**: Parent access (limited implementation)

## File Storage & Media
- **Storage Driver**: Local filesystem
- **Upload Paths**: 
  - Student avatars: `public/etudiants/`
  - User profiles: `public/users/`
  - Content files: `mindbridge/uploads/content/`
- **Supported Formats**: PDF, images, documents

## Docker Configuration

### Services
1. **mindbridge_api**: Laravel application (Port 7562)
2. **db**: MySQL 8.0.27 database
3. **phpmyadmin**: Database management interface (Port 8080)

### Environment Variables
```
DB_CONNECTION=centrale
DB_HOST=db
DB_DATABASE=centrale
DB_DATABASE_MINDBRIDGE=mind_bridge
DB_USERNAME=root
DB_PASSWORD=password
```

## Key Features

### 1. Mental Health Testing System
- Profiling tests for student assessment
- Multi-step test flows with conditional logic
- Difficulty-based question selection
- Real-time scoring and feedback

### 2. Educational Content Management
- Hierarchical content organization (Subject → Chapter → Content)
- PDF document storage and serving
- Content-based test generation
- Progress tracking per student

### 3. Achievement System
- Point-based rewards for student activities
- Badge unlocking based on achievements
- Connection streak tracking
- Motivational messaging system

### 4. Multi-tenant Architecture
- School-based tenant isolation
- Dynamic database switching
- Domain-based tenant resolution
- Centralized user management

### 5. Mobile API Support
- RESTful API for mobile applications
- Optimized endpoints for mobile consumption
- Offline-capable data structures
- Push notification integration

## Security Features
- API secret validation
- Sanctum token authentication
- Role-based access control
- Tenant data isolation
- SQL injection protection via Eloquent ORM
- CORS configuration for cross-origin requests

## Performance Considerations
- Database connection pooling
- Eager loading for related models
- Pagination for large datasets
- File storage optimization
- Cache-based locking for concurrent operations

## Detailed Database Schema

### Central Database Tables

#### users
```sql
- id (Primary Key)
- name (string)
- email (string, unique, nullable)
- phone (string, unique)
- password (string, nullable)
- avatar (string, default path)
- active (boolean, default true)
- is_mind_bridge_user (boolean, default false)
- type (enum: parrent, enseignant, admin, etudiant)
- fcm_token (string, nullable)
- status (enum: active, inactive, default active)
- created_at, updated_at, deleted_at
```

#### schools
```sql
- id (Primary Key)
- name (string)
- domain (string) - Used for tenant resolution
- database (string) - Tenant database name
- organization_id (Foreign Key to organizations)
- migrations_path (string)
- created_at, updated_at
```

#### niveaux (Educational Levels)
```sql
- id (Primary Key)
- niveau (string) - Grade/Level name
- created_at, updated_at
```

#### matieres (Subjects)
```sql
- id (Primary Key)
- matiere (string) - Subject name
- created_at, updated_at
```

### MindBridge Database Tables

#### etudiants (Students)
```sql
- id (Primary Key)
- first_name (string, nullable)
- last_name (string, nullable)
- email (string, unique, nullable)
- last_connection_at (datetime, nullable)
- connection_streak (integer, default 0)
- identifiant (string, nullable)
- nni (string, nullable) - National ID
- avatar (string, default path)
- test_profiling_completed (string, default false)
- user_id (Foreign Key to centrale.users)
- school_id (Foreign Key to centrale.schools)
- niveau_id (Foreign Key to centrale.niveaux, nullable)
- created_at, updated_at, deleted_at
```

#### tests
```sql
- id (Primary Key)
- title (string)
- description (string, nullable)
- target (string, nullable)
- type (enum: test_profiling, test_content, exam_simulation, culture_general)
- timer (integer, nullable) - Time limit in minutes
- challenge_date_start (date, nullable)
- challenge_date_end (date, nullable)
- difficulty_level (enum: tres_facile, facile, intermediaire, difficile, tres_difficile)
- matiere_id (Foreign Key to centrale.matieres, nullable)
- category_id (Foreign Key to categories)
- created_by (Foreign Key to centrale.users, nullable)
- niveau_id (Foreign Key to centrale.niveaux, nullable)
- content_id (Foreign Key to contents, nullable)
- created_at, updated_at
```

#### questions
```sql
- id (Primary Key)
- type (string)
- description (text)
- content (text)
- image_path (string, nullable)
- is_required (boolean)
- difficulty (string)
- matier_id (Foreign Key to centrale.matieres, nullable)
- niveau_id (Foreign Key to centrale.niveaux, nullable)
- response_type (string)
- is_true (boolean)
- is_false (boolean)
- answer (boolean)
- created_at, updated_at
```

#### steps
```sql
- id (Primary Key)
- order (integer) - Step sequence number
- required (boolean)
- condition (string, nullable)
- type (string)
- test_id (Foreign Key to tests)
- question_id (Foreign Key to questions)
- created_at, updated_at
```

#### options
```sql
- id (Primary Key)
- option_text (text)
- is_correct (boolean)
- question_id (Foreign Key to questions)
- created_at, updated_at
```

#### categories
```sql
- id (Primary Key)
- name (string)
- parent_id (Foreign Key to categories, nullable) - Self-referencing for hierarchy
- image_url (string, nullable)
- description (text, nullable)
- is_active (boolean)
- position (integer)
- code (string, nullable)
- gradient_background (string, nullable)
- button_text (string, nullable)
- count (integer, nullable)
- gradient_border (string, nullable)
- action_type (string, nullable)
- is_mobile (boolean)
- icon (string, nullable)
- is_bo (boolean) - Back office flag
- created_at, updated_at
```

#### contents
```sql
- id (Primary Key)
- type (string)
- title (string)
- content (text) - File path or content
- description (text, nullable)
- chapter_id (Foreign Key to chapters, nullable)
- niveau_id (Foreign Key to centrale.niveaux, nullable)
- matiere_id (Foreign Key to centrale.matieres, nullable)
- created_at, updated_at
```

#### chapters
```sql
- id (Primary Key)
- chapter (string) - Chapter name
- description (text, nullable)
- matiere_id (Foreign Key to centrale.matieres, nullable)
- niveau_id (Foreign Key to centrale.niveaux, nullable)
- created_at, updated_at
```

### Student Progress & Assessment Tables

#### etudiant_test_answers
```sql
- id (Primary Key)
- test_id (Foreign Key to tests)
- etudiant_id (Foreign Key to etudiants)
- question_id (Foreign Key to questions)
- comment (text, nullable)
- score (integer, nullable)
- selected_options (JSON) - Array of selected option IDs
- created_at, updated_at, deleted_at
```

#### etudiant_test_assignments
```sql
- id (Primary Key)
- etudiant_id (Foreign Key to etudiants)
- test_id (Foreign Key to tests)
- status (enum: ATTRIBUE, TERMINE, default ATTRIBUE)
- assigned_at (timestamp, nullable)
- completed_at (timestamp, nullable)
- created_at, updated_at
```

#### etudiant_test_status
```sql
- id (Primary Key)
- etudiant_id (Foreign Key to etudiants)
- test_id (Foreign Key to tests)
- status (string)
- score (integer, nullable)
- created_at, updated_at
```

### Achievement System Tables

#### badges
```sql
- id (Primary Key)
- name (string)
- description (text, nullable)
- icon (string, nullable)
- points_required (integer)
- created_at, updated_at
```

#### etudiant_badges
```sql
- id (Primary Key)
- eb_etudiant_id (Foreign Key to etudiants)
- eb_badge_id (Foreign Key to badges)
- created_at, updated_at
```

#### etudiant_points
```sql
- id (Primary Key)
- ep_etudiant_id (Foreign Key to etudiants)
- total_points (integer, default 0)
- created_at, updated_at
```

#### etudiant_matiere_level
```sql
- id (Primary Key)
- etudiant_id (Foreign Key to etudiants)
- matiere_id (Foreign Key to centrale.matieres)
- level (string) - Student's level in the subject
- created_at, updated_at
```

### Additional Tables

#### student_notes
```sql
- id (Primary Key)
- etudiant_id (Foreign Key to etudiants)
- matiere_id (Foreign Key to centrale.matieres)
- note (decimal) - Grade/Score
- created_at, updated_at
```

#### user_message_states
```sql
- id (Primary Key)
- user_id (Foreign Key to centrale.users)
- category (string)
- sub_category (string, nullable)
- last_index (integer, default -1)
- created_at, updated_at
```

#### hooty_requests
```sql
- id (Primary Key)
- file_path (string)
- action (string, nullable)
- result (text, nullable)
- created_at, updated_at
```

## Cross-Database Relationships

The application maintains referential integrity across databases using foreign key constraints that reference tables in different databases:

### From MindBridge to Central
- `etudiants.user_id` → `centrale.users.id`
- `etudiants.school_id` → `centrale.schools.id`
- `etudiants.niveau_id` → `centrale.niveaux.id`
- `tests.matiere_id` → `centrale.matieres.id`
- `tests.created_by` → `centrale.users.id`
- `tests.niveau_id` → `centrale.niveaux.id`
- `questions.matier_id` → `centrale.matieres.id`
- `questions.niveau_id` → `centrale.niveaux.id`
- `contents.niveau_id` → `centrale.niveaux.id`
- `contents.matiere_id` → `centrale.matieres.id`
- `chapters.matiere_id` → `centrale.matieres.id`
- `chapters.niveau_id` → `centrale.niveaux.id`

## Detailed API Endpoints

### Authentication Flow

#### Admin Login
```
POST /api/mind_bridge/auth/login
Headers:
  - APPSECRET: {app_secret}
  - Content-Type: application/json
Body:
  {
    "email": "<EMAIL>",
    "password": "password"
  }
Response:
  {
    "token": "sanctum_token",
    "user": {UserResource}
  }
```

#### Student Login
```
POST /api/mind_bridge/auth/etudiant/login
Headers:
  - APPSECRET: {app_secret}
Body:
  {
    "email": "<EMAIL>", // or "phone": "123456789"
    "password": "password",
    "fcm_token": "firebase_token" // optional
  }
Response:
  {
    "token": "sanctum_token",
    "user": {User},
    "etudiant": {MindBridgeEtudiant},
    "welcome_message": "Personalized welcome message"
  }
```

### Test Management API

#### Create Complete Test
```
POST /api/mind_bridge/tests/storeAll
Headers: Authorization: Bearer {token}
Body:
  {
    "title": "Test Title",
    "description": "Test Description",
    "type": "test_profiling",
    "category_id": 1,
    "matiere_id": 1,
    "niveau_id": 1,
    "timer": 30,
    "difficulty_level": "intermediaire",
    "steps": [
      {
        "order": 1,
        "question": {
          "type": "multiple_choice",
          "description": "Question text",
          "options": [
            {"option_text": "Option 1", "is_correct": true},
            {"option_text": "Option 2", "is_correct": false}
          ]
        }
      }
    ]
  }
```

#### Run Mental Health Test
```
POST /api/mind_bridge/run_test
Body:
  {
    "category_id": 2,
    "sub_category_id": 3
  }
Response:
  {
    "tests": [TestCollection],
    "next_difficulty_level": "difficile",
    "restarted": false
  }
```

### Student Mobile API

#### Get Dashboard Categories
```
GET /api/mind_bridge/etudiant/categories/board
Headers: Authorization: Bearer {token}
Response:
  {
    "data": [
      {
        "id": 1,
        "name": "Mental Health",
        "subcategories": [...],
        "gradient_background": "#FF6B6B",
        "icon": "brain"
      }
    ]
  }
```

#### Submit Test Profiling
```
POST /api/mind_bridge/etudiant/tests
Body:
  {
    "answers": [
      {
        "question_id": 1,
        "selected_options": [1, 3],
        "comment": "Optional comment"
      }
    ],
    "action_test_type": "test_profiling",
    "got_easy_question_right": true,
    "got_difficult_question_right": false
  }
Response:
  {
    "message": "Tests stored successfully.",
    "feedback_message": "Great job! You earned 50 points."
  }
```

#### Get Lessons by Subject
```
GET /api/mind_bridge/etudiant/contents?matiere_id=1
Response:
  {
    "contents": [
      {
        "id": 1,
        "title": "Introduction to Mathematics",
        "type": "lesson",
        "content": "path/to/pdf",
        "chapter": {ChapterResource}
      }
    ]
  }
```

### Content Management API

#### Create Content
```
POST /api/mind_bridge/contents
Headers:
  - Authorization: Bearer {token}
  - Content-Type: multipart/form-data
Body:
  - title: "Lesson Title"
  - description: "Lesson Description"
  - niveau_id: 1
  - matiere_id: 1
  - chapter_id: 1
  - file: {PDF file}
```

#### Get Content by Filters
```
GET /api/mind_bridge/contents?matiere=1&niveau=2&chapter=3&limit=10&page=1
Response:
  {
    "data": [ContentResource],
    "current_page": 1,
    "total_pages": 5,
    "per_page": 10,
    "total_items": 50
  }
```

## Business Logic Flows

### 1. Student Registration & Onboarding
```
1. Admin creates User in central database (type: etudiant)
2. User assigned to School (tenant)
3. MindBridgeEtudiant record created in tenant database
4. Student completes test profiling
5. System calculates initial subject levels
6. Achievement points awarded for completion
```

### 2. Test Taking Flow
```
1. Student selects category from dashboard
2. System filters available tests by:
   - Student's niveau_id
   - Category
   - Completion status
   - Date availability (for challenges)
3. Test steps loaded in sequence
4. Student answers questions
5. Answers stored in etudiant_test_answers
6. Test completion triggers:
   - Score calculation
   - Badge evaluation
   - Points award
   - Progress tracking update
```

### 3. Content Learning Flow
```
1. Student browses subjects by category
2. System shows chapters for selected subject
3. Student selects lesson (content)
4. Content file served from storage
5. Lesson completion tracked
6. Points awarded for lesson_viewed action
7. Related tests become available
```

### 4. Achievement System Flow
```
1. Student performs action (login, complete test, view lesson)
2. BadgePointService.processAction() called
3. Points calculated based on action type
4. Points added to etudiant_points
5. Badge eligibility checked
6. New badges awarded if criteria met
7. Notification sent for achievements
```

### 5. Multi-Tenant Request Flow
```
1. Request received with Origin header
2. TenantsMiddleware extracts domain
3. School lookup by domain
4. TenantService.switchToTenant() called
5. Database connection switched to tenant DB
6. Request processed with tenant context
7. Response returned
```

## Error Handling & Logging

### Common Error Responses
```json
// Validation Error (422)
{
  "error": {
    "field_name": ["Validation message"]
  }
}

// Authentication Error (401)
{
  "message": "Unauthenticated"
}

// Not Found (404)
{
  "error": "Resource not found"
}

// Server Error (500)
{
  "error": "An unexpected error occurred"
}
```

### Logging Strategy
- **Authentication**: All login attempts logged
- **Test Submissions**: Complete audit trail
- **Errors**: Full exception stack traces
- **Performance**: Database query logging
- **Security**: Failed authentication attempts

## Configuration Files

### Key Configuration
- `config/database.php` - Multi-database connections
- `config/auth.php` - Sanctum authentication
- `config/permission.php` - Role-based access control
- `config/motivational_messages.php` - Student feedback messages
- `config/platforms_secrets.php` - API security keys

### Environment Variables
```
# Database
DB_CONNECTION=centrale
DB_HOST=db
DB_DATABASE=centrale
DB_DATABASE_MINDBRIDGE=mind_bridge

# Authentication
SANCTUM_EXPIRES_IN=720

# Firebase
GOOGLE_APPLICATION_CREDENTIALS=config/firebase-key.json

# Notifications
SERVICE_NOTIF_URL=https://notification-service.com
```

This documentation provides a comprehensive overview of the MindBridge backend architecture, database design, API endpoints, and core functionality. The system is designed to scale across multiple educational institutions while maintaining data isolation and providing rich educational assessment capabilities.
