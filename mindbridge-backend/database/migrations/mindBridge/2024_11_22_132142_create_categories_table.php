<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('parent_id')
                ->nullable()
                ->constrained('categories')
                ->cascadeOnDelete();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('button_text')->nullable();
            $table->json('gradient_background')->nullable();
            $table->json('gradient_border')->nullable();
            $table->string('icon')->nullable();
            $table->string('image_url')->nullable();
            $table->enum('action_type', ['lecon', 'test'])->nullable();
            $table->boolean('is_mobile')->nullable()->default(false);
            $table->boolean('is_bo')->nullable()->default(true);
            $table->boolean('is_active')->default(true);
            $table->string('code')->unique()->nullable();
            $table->unsignedInteger('position')->default(0);
            $table->integer('count')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
