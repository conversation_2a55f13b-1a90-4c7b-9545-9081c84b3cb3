<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('etudiant_test_answers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('test_id');
            $table->unsignedBigInteger('etudiant_id');
            $table->unsignedBigInteger('question_id');
            $table->text('comment')->nullable();
            $table->integer('score')->nullable();
            $table->json('selected_options')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('test_id')->references('id')->on('tests')->onDelete('cascade');
            $table->foreign('etudiant_id')->references('id')->on('etudiants')->onDelete('cascade');
            $table->foreign('question_id')->references('id')->on('questions')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('etudiant_tests');
    }
};
