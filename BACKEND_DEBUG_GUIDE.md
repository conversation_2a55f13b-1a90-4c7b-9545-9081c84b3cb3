# Backend Debug Guide - <PERSON><PERSON>e Tests

## Check Backend Logs

### View Docker Container Logs
```bash
# View real-time logs
docker logs -f mindbridge_api

# View last 100 lines
docker logs --tail 100 mindbridge_api
```

## Check Database

### Access MySQL Container
```bash
# Get correct credentials from docker-compose.yml
docker exec -it mindbridge-backend_db_1 mysql -u <user> -p<password> mindbridge
```

### Check Categories
```sql
-- View all categories
SELECT id, name, parent_id, is_bo, is_mobile FROM categories;

-- View category 2 (Sante Mentale) and its subcategories
SELECT id, name, parent_id, is_bo, is_mobile FROM categories WHERE id = 2 OR parent_id = 2;

-- View category 11 specifically
SELECT id, name, parent_id, is_bo, is_mobile FROM categories WHERE id = 11;
```

### Check Tests for Category 11
```sql
-- Count tests in category 11
SELECT COUNT(*) as test_count FROM tests WHERE category_id = 11;

-- View tests in category 11
SELECT id, title, category_id FROM tests WHERE category_id = 11 LIMIT 10;

-- View tests with steps and questions
SELECT 
  t.id, 
  t.title, 
  t.category_id,
  COUNT(DISTINCT s.id) as step_count,
  COUNT(DISTINCT q.id) as question_count
FROM tests t
LEFT JOIN steps s ON t.id = s.test_id
LEFT JOIN questions q ON s.question_id = q.id
WHERE t.category_id = 11
GROUP BY t.id;
```

## Test API Endpoints

### Using cURL

#### 1. Get Categories Board (requires auth)
```bash
curl -X GET http://localhost:7562/api/mind_bridge/categories/board \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

#### 2. Get Tests for Category 11 (requires auth)
```bash
curl -X GET http://localhost:7562/api/mind_bridge/categories/11/tests \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### Using Postman

1. **Create new request**
2. **Set method to GET**
3. **Enter URL**: `http://localhost:7562/api/mind_bridge/categories/11/tests`
4. **Go to Authorization tab**
5. **Select "Bearer Token"**
6. **Paste your authentication token**
7. **Send request**

## Check Backend Controller

### File: `mindbridge-backend/app/Http/Controllers/MindBridge/MindBridgeTestController.php`

#### Method: `getTestByCategoryId($categoryId)`
- Location: Line 574
- Fetches tests with steps, questions, and options
- Returns 404 if no tests found
- Returns 200 with data array if tests found

### Expected Response Format
```json
{
  "message": "Test for this category",
  "data": [
    {
      "id": 1,
      "title": "Test Title",
      "description": "Test Description",
      "type": "sante_mentale",
      "category_id": 11,
      "steps": [
        {
          "id": 1,
          "test_id": 1,
          "question_id": 1,
          "question": {
            "id": 1,
            "content": "Question text",
            "options": [...]
          }
        }
      ]
    }
  ]
}
```

## Common Issues

### Issue 1: 404 Error
**Cause**: No tests in category 11 or category doesn't exist
**Solution**: 
- Check database for tests with `category_id = 11`
- Verify category 11 exists and is active

### Issue 2: 401 Unauthorized
**Cause**: Missing or invalid authentication token
**Solution**:
- Ensure admin panel is logged in
- Check token is being sent in Authorization header
- Verify token hasn't expired

### Issue 3: Empty Children Array
**Cause**: Category 2 has no subcategories with `is_bo = true`
**Solution**:
- Check database: `SELECT * FROM categories WHERE parent_id = 2 AND is_bo = true;`
- Ensure subcategories have `is_bo = true`

## Verify Fix

After making changes, verify:

1. **Frontend Console**: Check for console logs without errors
2. **Network Tab**: Verify API requests return 200 status
3. **Response Data**: Verify response contains test data
4. **UI Display**: Verify tests display in admin panel

