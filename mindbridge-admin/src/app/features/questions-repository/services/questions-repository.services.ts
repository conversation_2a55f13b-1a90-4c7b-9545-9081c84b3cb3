import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { QuestionRepository } from '../models/questions-repository-response';
import { getQuery, Pagination } from '../../shared/helpers/query.helper';
import { QuestionSearchForm } from '../models/question-search-request';
import { QuestionResponse } from '../models/questions-response';
import { ApiResponse } from '../../../core/models/api-response';

@Injectable({
  providedIn: 'root',
})
export class QuestionRepositoryService {
  private http = inject(HttpClient);

  allQuestionsRepository(): Observable<any> {
    return this.http.get<any>(
      environment.BASE_URL_API + 'mind_bridge/questions'
    );
  }
  searchQuestions(
    searchForm: QuestionSearchForm,
    pagination: Pagination
  ): Observable<ApiResponse<QuestionResponse>> {
    const params = getQuery(searchForm, pagination);
    return this.http.get<any>(
      environment.BASE_URL_API + 'mind_bridge/questions',
      { params }
    );
  }

  addQuestionRepository(payload: QuestionRepository): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/questions',
      payload
    );
  }

  updateQuestionRepository(
    payload: QuestionRepository,
    id: number
  ): Observable<any> {
    // Remove id from payload
    const { id: _, ...updatedPayload } = payload;

    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/questions/' + id,
      payload
    );
  }

  deleteQuestionRepository(id: number): Observable<any> {
    // Remove id from payload
    return this.http.delete<any>(
      environment.BASE_URL_API + 'mind_bridge/questions/' + id
    );
  }

  uploadImage(formData: FormData): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/questions/upload-image',
      formData
    );
  }

  deleteImage(imagePath: string): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL_API + 'mind_bridge/questions/delete-image',
      { image_path: imagePath }
    );
  }
}
