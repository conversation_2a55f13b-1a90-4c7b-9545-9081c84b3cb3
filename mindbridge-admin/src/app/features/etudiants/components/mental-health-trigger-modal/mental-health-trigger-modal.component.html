<div class="modal-container">
  <div class="modal-header">
    <h2>Déclencher un test de santé mentale</h2>
    <button class="close-btn" (click)="cancel()">×</button>
  </div>

  <div class="modal-body">
    <!-- Trigger Type Selection -->
    <div class="trigger-selection">
      <h3>Sélectionnez le type de déclencheur:</h3>
      
      <div class="trigger-options">
        <!-- Parent Trigger -->
        <button
          class="trigger-btn"
          [class.active]="triggerType() === 'parent'"
          (click)="selectTriggerType('parent')"
        >
          <div class="trigger-icon">👨‍👩‍👧</div>
          <div class="trigger-label">Parent</div>
          <div class="trigger-desc">Sélectionner une observation parent</div>
        </button>

        <!-- Teacher Trigger -->
        <button
          class="trigger-btn"
          [class.active]="triggerType() === 'teacher'"
          (click)="selectTriggerType('teacher')"
        >
          <div class="trigger-icon">👨‍🏫</div>
          <div class="trigger-label">Enseignant</div>
          <div class="trigger-desc">Déclencher par l'enseignant</div>
        </button>

        <!-- Significant Grade Drop -->
        <button
          class="trigger-btn"
          [class.active]="triggerType() === 'significant_grade_drop'"
          (click)="selectTriggerType('significant_grade_drop')"
        >
          <div class="trigger-icon">�</div>
          <div class="trigger-label">Baisse significative des notes</div>
          <div class="trigger-desc">Déclencher par baisse de notes</div>
        </button>
      </div>
    </div>

    <!-- Observation Selection (for Parent trigger) -->
SIMAR2030
    {{showObservationList()}}

    {{triggerType()}}
    @if (showObservationList()) {
      <div class="observation-selection">
        <h3>Sélectionnez une observation:</h3>
        
        @if (isLoading()) {
          <div class="loading">Chargement des observations...</div>
        } @else if (observations().length > 0) {
          <div class="observation-list">
            @for (obs of observations(); track obs.id) {
              <div
                class="observation-item"
                [class.selected]="selectedObservation()?.id === obs.id"
                (click)="selectObservation(obs)"
              >
                <div class="obs-label">{{ obs.label }}</div>
                <div class="obs-description">{{ obs.description }}</div>
                @if (obs.category) {
                  <div class="obs-category">{{ obs.category }}</div>
                }
              </div>
            }
          </div>
        } @else {
          <div class="no-observations">Aucune observation disponible</div>
        }
      </div>
    }
  </div>

  <div class="modal-footer">
    <button class="btn-cancel" (click)="cancel()">Annuler</button>
    <button
      class="btn-confirm"
      (click)="confirm()"
      [disabled]="!triggerType() || ((triggerType() === 'parent' || triggerType() === 'significant_grade_drop') && !selectedObservation())"
    >
      Confirmer
    </button>
  </div>
</div>

