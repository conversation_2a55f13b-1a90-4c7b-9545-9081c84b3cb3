<?php

namespace App\Http\Controllers\MindBridge;

use DB;
use App\Http\Controllers\Controller;
use App\Http\Resources\MindBridge\ContentResource;
use App\Models\MindBridge\Content;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class ContentController extends Controller
{
    public function index(Request $request)
    {
        $matiere = $request->input('matiere');
        $niveau = $request->input('niveau');
        $chapter = $request->input('chapter');
        $limit = $request->input('limit', 10);
        $page = $request->input('page', 1);

        $query = Content::with('matiere', 'niveau', 'chapter');

        if ($matiere) {
            $query->where('matiere_id', $matiere);
        }

        if ($niveau) {
            $query->where('niveau_id', $niveau);
        }

        if ($chapter) {
            $query->where('chapter_id', $chapter);
        }

        $total = $query->count();
        $contents = $query->orderBy('created_at', 'desc')
            ->skip(($page - 1) * $limit)
            ->take($limit)
            ->get();

        $totalPages = ceil($total / $limit);

        return response()->json([
            'data' => ContentResource::collection($contents),
            'current_page' => $page,
            'total_pages' => $totalPages,
            'per_page' => $limit,
            'total_items' => $total,
        ]);
    }


    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            Log::info("ContentController::store => Start");
            Log::info($request->all());
            
            // TODO: Fix Content-Length of (n) bytes exceeds the limit of (n) bytes

            $request->validate([
                "title" => 'required|string',
                "description" => 'nullable|string',
                'niveau_id' => 'required|exists:niveaux,id',
                'matiere_id' => 'required|exists:matieres,id',
                'chapter_id' => 'required|exists:mind_bridge.chapters,id',
                "file" => 'required|file|mimes:pdf|mimetypes:application/pdf',
            ]);

            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $directory = "mindbridge/uploads/content/{$request->type}";

                if (!Storage::disk('public')->exists($directory)) {
                    Storage::disk('public')->makeDirectory($directory);
                }

                $uniqueFilename = time() . '-' . Str::random(5) . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs($directory, $uniqueFilename, 'public');

                $content = Content::create([
                    "title" => $request->title,
                    "description" => $request->description,
                    "niveau_id" => $request->niveau_id,
                    "matiere_id" => $request->matiere_id,
                    "chapter_id" => $request->chapter_id,
                    "content" => $path,
                ]);

                DB::commit();
                return response()->json(ContentResource::make($content), 201);
            } else {
                DB::rollBack();
                return response()->json(['error' => 'File not found.'], 404);
            }
        } catch (ValidationException $e) {
            Log::info("ContentController::store => ValidationException: {$e}");
            DB::rollBack();
            return response()->json($e->validator->errors(), 422);
        } catch (Exception $e) {
            Log::info("ContentController::store => Error: {$e}");
            DB::rollBack();
            return response()->json([
                'error' => "Failed to create content",
                'message' => 'Content not found'
            ], 500);
        }
    }

    public function show($id)
    {
        try {
            $content = Content::with('matiere', 'niveau', 'chapter')->findOrFail($id);
            return response()->json(ContentResource::make($content), 200);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'message' => 'Content not found'
            ], 404);
        } catch (Exception $e) {
            Log::info("ContentController::show => Error: {$e}");
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    public function update($id, Request $request)
    {
        try {
            DB::beginTransaction();
            Log::info("ContentController::update => Start");
            Log::info($request->all());

            $rules = [
                "title" => 'nullable|string',
                "description" => 'nullable|string',
                'niveau_id' => 'nullable|exists:niveaux,id',
                'matiere_id' => 'nullable|exists:matieres,id',
                'chapter_id' => 'nullable|exists:mind_bridge.chapters,id',
                "file" => 'nullable|file|mimes:pdf',
            ];

            $validatedData = $request->only(array_keys($rules));
            $request->validate(array_intersect_key($rules, $validatedData));

            $content = Content::findOrFail($id);

            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $directory = "mindbridge/uploads/content/{$request->type}";

                if (!Storage::disk('public')->exists($directory)) {
                    Storage::disk('public')->makeDirectory($directory);
                }

                $uniqueFilename = time() . '-' . Str::random(5) . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs($directory, $uniqueFilename, 'public');

                // Delete the old file if it exists
                // if ($content->content && Storage::disk('public')->exists($content->content)) {
                //     Storage::disk('public')->delete($content->content);
                // }
                $validatedData['content'] = $path;
            }

            $content->update($validatedData);

            DB::commit();

            return response()->json(ContentResource::make($content), 200);
        } catch (ModelNotFoundException $e) {
            Log::error("ContentController::update => Content not found: ID {$id}", ['exception' => $e]);
            DB::rollBack();
            return response()->json(['error' => 'Content not found.'], 404);
        } catch (ValidationException $e) {
            Log::info("ContentController::update => ValidationException: {$e}");
            DB::rollBack();
            return response()->json($e->validator->errors(), 422);
        } catch (Exception $e) {
            Log::error("ContentController::update => Unexpected error: {$e}");
            DB::rollBack();
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }


    public function destroy($id)
    {
        try {
            $chapter = Content::find($id);
            $chapter->delete();
            return response()->json([
                'message' => 'Chapter deleted successfully'
            ], 204);
        } catch (Exception $e) {
            \Log::info("Unexpected error in ContentController@destroy: ", ['exception' => $e]);
            Log::error($e);
            return response()->json([
                'error' => 'An unexpected error occurred.',
                "message" => "Failed to delete content"
            ], 500);
        }
    }


    public function byMatiere(Request $request)
    {

        try {

            $matiereId = $request->input('matiere_id');
            $niveau = $request->input('niveau');
            $chapter = $request->input('chapter');
            $limit = $request->input('limit', 10);
            $page = $request->input('page', 1);

            $query = Content::with('matiere', 'niveau', 'chapter');

            if ($matiereId) {
                $query->where('matiere_id', $matiereId);
            }

            if ($niveau) {
                $query->where('niveau_id', $niveau);
            }

            if ($chapter) {
                $query->where('chapter_id', $chapter);
            }

            $total = $query->count();
            $contents = $query->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->get();

            $totalPages = ceil($total / $limit);

            return response()->json([
                'data' => ContentResource::collection($contents),
                'current_page' => $page,
                'total_pages' => $totalPages,
                'per_page' => $limit,
                'total_items' => $total,
            ]);

        } catch (Exception $e) {
            Log::error("ContentController::byMatiere => Unexpected error: {$e}");
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    public function byNiveauMatiere(Request $request)
    {
        Log::info('ContentController::byNiveauMatiere - Start', $request->only(['matiere_id', 'niveau_id']));

        try {
            $validated = $request->validate([
                'matiere_id' => 'required|exists:centrale.centrale.matieres,id',
                'niveau_id'  => 'required|exists:centrale.centrale.niveaux,id',
            ]);

            $contents = Content::with(['matiere', 'niveau', 'chapter'])
                ->where('matiere_id', $validated['matiere_id'])
                ->where('niveau_id', $validated['niveau_id'])
                ->get();

            return ContentResource::collection($contents);
        } catch (ValidationException $e) {
            Log::warning('ContentController::byNiveauMatiere - ValidationException', [
                'errors' => $e->validator->errors()
            ]);

            return response()->json($e->validator->errors(), 422);
        } catch (\Exception $e) {
            Log::error('ContentController::byNiveauMatiere - Unexpected error', [
                'message' => $e->getMessage(),
                'trace'   => $e->getTraceAsString()
            ]);

            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }
}
