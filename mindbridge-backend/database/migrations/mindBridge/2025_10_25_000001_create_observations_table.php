<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'mind_bridge';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mind_bridge')->create('observations', function (Blueprint $table) {
            $table->id();
            $table->string('label');
            $table->string('category')->nullable();
            $table->enum('visible_to', ['teacher', 'parent', 'both'])->default('both');
            $table->text('description')->nullable();
            $table->boolean('active')->default(true);
            $table->foreignId('test_id')
                ->nullable()
                ->constrained('tests')
                ->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mind_bridge')->dropIfExists('observations');
    }
};

